
# React Native rules
- Use functional components with hooks
- Follow a consistent folder structure (components, screens, navigation, services, hooks, utils)
- Use React Navigation for screen navigation
- Use StyleSheet for styling instead of inline styles
- Use FlatList for rendering lists instead of map + ScrollView
- Use custom hooks for reusable logic
- Implement proper error boundaries and loading states
- Optimize images and assets for mobile performance
- Enforce the Single Responsibility Principle (SRP): Components should have one, and only one, reason to change. Avoid "God Components" that manage state, animations, and complex rendering all at once.
- Build UIs with Component Composition: Create small, reusable "dumb" components and compose them into larger "smart" feature components.
- Establish a Single Source of Truth: Avoid duplicating state. State should live in one place and be passed down via props (controlled components). Do not sync props to local state with `useEffect`.
- Lift State Up: When multiple components need access to the same state, move it to their closest common ancestor.
- Extract Complex Logic into Custom Hooks: Any reusable or complex logic (state management, animations, data fetching) should be moved out of components and into custom hooks to keep components clean and focused on rendering.
- Centralize Constants: Group magic numbers (colors, durations, dimensions) into a central `constants.ts` file to ensure consistency and improve readability.

# Packages
- do not use packages with Expo dependencies
- update packages to the latest version where available after checking the changelog for compatibility issues
- work with 3rd party packages already setup in package.json, unless you see better alternatives

# Tools
- use yarn, not npm

# Configurations

# Testing
1. test suite are in `tests` folder
2. When fixing failed tests:
  - always analyse and understand the test cases thoroughly
  - understand the expected outcomes
  - understand the actual outcomes
  - be able to explain the root cause of the failures
  - reference past fixes, consider reverting previous fixes if they are not needed
  - before fixing them

# Security
- NEVER expose actual endpoint domains in any code files
- Always use placeholders like '<primary-endpoint>' instead of real domains
- Endpoint values should only be generated at runtime through secure mechanisms
- Avoid logging actual endpoint values; always mask them in logs
- Do not add test domains or example endpoints to the codebase

# Documentation
1. All documentation is found in the `docs` folder, except for the README.md file
2. `references` folder contains references of past implementations
3. `plans` folder contains implementation plans
4. use context7 mcp for latest documentation for reactive native and public packages before searching online

# Performance
- Always measure and log key performance metrics

# UI Experience
- Use tabs to separate different content sections
- Provide a clean, uncluttered interface with adequate spacing
- Implement collapsible sections for detailed information
- Use modals for non-critical information (like performance metrics)
- Implement proper keyboard handling for form inputs

# Models
- Use the base Model class approach for all database models
- Define schema in the class using Realm.ObjectSchema
- Always include id, createdAt, and updatedAt fields in models
- Use proper TypeScript typing for all properties
- Register schemas via the static initializer pattern
- Use getters for relationship access instead of functions
- Centralize Realm instance management

# Database Best Practices
- Use Realm's built-in sorting/filtering capabilities instead of JavaScript
- Always sort by createdAt descending (newest first) for better UX
- Implement business logic methods directly in model classes
- Use bulkUpdate for batch operations to improve performance
- Always create relationships with IDs, not object references
- Use QueryBuilder for complex queries
- Encapsulate Realm access through model methods, never expose directly
- Add domain-specific finder methods directly to model classes

# Clean Architecture & Migration Guidelines
## Version-Agnostic Architecture
- **NEVER include version numbers in directory paths** (e.g., use `src/data/schemas/` not `src/data/schemas/v7/`)
- **NEVER include version numbers in class names** (e.g., use `DatabaseManager` not `V7DatabaseManager`)
- **NEVER include version numbers in export names** (e.g., use `CLEAN_SCHEMAS` not `V7_CLEAN_SCHEMAS`)
- **NEVER include version numbers in configuration keys** (e.g., use `perkd-redux-key` not `perkd-v7-redux-key`)
- **NEVER include version numbers in documentation** except when specifically discussing version history

## Naming Conventions
- **Avoid superfluous qualifiers** in class and file names (e.g., use `DataService` not `OptimizedDataService`)
- **Use direct, purpose-driven naming** that describes what something does, not how it does it
- **Use consistent naming patterns** across related components (e.g., `DataService`, `DatabaseManager`)
- **Prefer shorter, clearer names** over longer, more qualified ones
- **Use semantic versioning** for the app as a whole, not for individual components

## Schema Evolution
- **Use a single schema version number** for the entire database, not per-model versions
- **Implement migration strategies** that handle all models together, not individually
- **Keep schema history in migration code only**, not in permanent architecture
- **Use a clean schema approach** with a fresh start strategy for major versions
- **Maintain backward compatibility** through transformation engines, not schema versioning

## Performance Metrics
- **Use version-agnostic metric names** (e.g., `target` and `baseline`, not `v7Target` and `v6Baseline`)
- **Document performance improvements** without tying them to specific versions
- **Benchmark against previous implementations** without embedding version numbers
- **Focus on relative improvements** rather than version-specific targets

## Migration Strategy
- **Implement a clean break migration approach** rather than incremental versioning
- **Extract data from legacy systems** using dedicated, temporary components
- **Transform extracted data** to fit the clean schema using a transformation engine
- **Validate data integrity** through comprehensive testing
- **Preserve 100% of business logic** while eliminating technical debt
- **Keep migration code separate** from permanent architecture components
- **Document migration strategies** without embedding version numbers in code

## Code Organization
- **Organize by feature and domain**, not by technical layer or version
- **Keep related code together** regardless of technical type
- **Use clean imports** without version qualifiers
- **Maintain consistent directory structure** across the entire codebase
- **Avoid deep nesting** of directories based on versions or technical layers

## Testing Guidelines
- **Write version-agnostic tests** that focus on behavior, not implementation details
- **Use clean import paths** in test files (e.g., `../data/schemas/index` not `../data/schemas/v7/index`)
- **Mock clean interfaces** rather than version-specific implementations
- **Test migration logic separately** from permanent architecture components
- **Validate architectural integrity** with automated tests that prevent version proliferation
- **Focus on business logic preservation** rather than technical migration details

## Documentation Standards
- **Write documentation that ages well** by avoiding version-specific references
- **Use generic terms** like "current implementation" instead of "v7 implementation"
- **Document patterns and principles** rather than specific version details
- **Keep migration documentation separate** from architectural documentation
- **Update documentation** to remove version references during refactoring
- **Focus on "why" and "what"** rather than "when" and "which version"

## Future-Proofing Principles
- **Design for evolution**, not revolution - architecture should support gradual improvement
- **Separate concerns** so that business logic is independent of technical implementation
- **Use dependency injection** to make components testable and replaceable
- **Implement clean interfaces** that hide implementation details
- **Plan for data migration** without embedding migration logic in permanent code
- **Build modular systems** that can be upgraded piece by piece
- **Avoid tight coupling** between components that might evolve at different rates

## Anti-Patterns to Avoid
- **Version proliferation**: Creating v1, v2, v3 directories or classes
- **Superfluous naming**: Adding "Optimized", "Enhanced", "New" to class names
- **Technical debt accumulation**: Keeping old code "just in case"
- **Migration logic in permanent code**: Embedding version checks in business logic
- **Inconsistent naming**: Using different patterns for similar components
- **Deep version hierarchies**: Nesting directories by version numbers
- **Version-specific documentation**: Writing docs that become obsolete with updates

## Validation & Enforcement
- **Create architectural validation scripts** to prevent regression to version-proliferation patterns
- **Use automated tests** to enforce clean architecture principles
- **Implement pre-commit hooks** that check for version numbers in permanent code
- **Regular architecture reviews** to ensure guidelines are being followed
- **Refactor proactively** when patterns start to drift from clean architecture
- **Document violations** and their fixes to improve future decision-making
- **Train team members** on clean architecture principles and migration strategies

## Success Metrics
- **Zero version numbers** in permanent architecture file paths
- **Consistent naming patterns** across all components
- **Clean import statements** without version qualifiers
- **Maintainable codebase** that supports evolution without technical debt
- **Fast development cycles** enabled by clean, predictable architecture
- **Successful migrations** that preserve 100% of business logic
- **Reduced cognitive overhead** from simplified, version-agnostic design

## Key Takeaways
1. **Version numbers belong in git tags and release notes, not in code structure**
2. **Clean architecture enables evolution without revolution**
3. **Migration strategies should be temporary, not permanent architectural features**
4. **Consistent naming reduces cognitive overhead and improves maintainability**
5. **Future-proof design prevents the need for major refactoring**
6. **Business logic preservation is more important than technical implementation details**
7. **Validation and enforcement prevent architectural drift over time**

# Ignore files
*.env
*.pem
dist/
references/