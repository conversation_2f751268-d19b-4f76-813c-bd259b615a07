module.exports = {
  globalEventEmitter: {
    safeEmit: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
    removeAllListeners: jest.fn(),
    resetMetrics: jest.fn(),
    enableReduxBridge: jest.fn(),
    disableReduxBridge: jest.fn(),
    isReduxBridgeEnabled: jest.fn(() => true),
    getMetrics: jest.fn(() => ({
      totalEvents: 0,
      errorCount: 0,
      listenerCount: 0,
      averageProcessingTime: 0,
      eventsByType: {},
      thresholds: {
        maxListeners: 100,
        maxProcessingTime: 100,
      },
      memoryUsage: 0,
      lastActivity: Date.now(),
    })),
  },
  legacyEventWrapper: {
    emit: jest.fn(),
    on: jest.fn(),
  },
  PerkdEventEmitter: jest.fn().mockImplementation(() => ({
    safeEmit: jest.fn(),
    on: jest.fn(),
    off: jest.fn(),
    removeAllListeners: jest.fn(),
    resetMetrics: jest.fn(),
    enableReduxBridge: jest.fn(),
    disableReduxBridge: jest.fn(),
    isReduxBridgeEnabled: jest.fn(() => true),
    getMetrics: jest.fn(() => ({
      totalEvents: 0,
      errorCount: 0,
      listenerCount: 0,
      averageProcessingTime: 0,
      eventsByType: {},
      thresholds: {
        maxListeners: 100,
        maxProcessingTime: 100,
      },
      memoryUsage: 0,
      lastActivity: Date.now(),
    })),
  })),
};
