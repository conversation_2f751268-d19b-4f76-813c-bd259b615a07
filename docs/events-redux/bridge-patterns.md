# Perkd v7.0 Event-Redux Bridge Patterns - Implementation Guide

**Document Version:** 1.0  
**Date:** January 2025  
**Status:** AI Implementation Ready  
**Target:** AI Coding Agents and Development Team  

## Overview

This document provides comprehensive patterns and implementation details for the critical bridge layer between Perkd's proven event-driven architecture and the new Redux Toolkit integration. The bridge ensures seamless communication while preserving business logic integrity.

## System Integration Context

**Important**: The Event-Redux bridge works **alongside** the existing Realm middleware, not as a replacement. The complete v7 architecture includes:

1. **Realm Middleware** (PRESERVE): `src/store/middleware/realmMiddleware.ts` - Database persistence and Redux-Realm sync
2. **Event System** (ENHANCE): `$.Event` global interface - Business logic coordination
3. **Event-Redux Bridge** (ADD): `src/core/store/middleware/eventBridge.ts` - Event-Redux communication

### Bridge Purpose and Scope

The Event-Redux bridge specifically handles:
- **Event → Redux**: Business events updating UI state (loading states, notifications, etc.)
- **Redux → Event**: UI actions triggering business events (analytics, workflows, etc.)
- **Performance Optimization**: Debouncing, batching, and error recovery
- **Type Safety**: TypeScript integration for event-action mappings

**What the bridge does NOT handle**:
- Database operations (handled by Realm middleware)
- Data persistence (handled by Realm + MMKV)
- Core business logic (handled by Event system)

## Bridge Architecture Principles

### Core Design Philosophy

1. **Single Source of Truth**: Redux holds UI state, Events coordinate business logic
2. **Unidirectional Flow**: Clear data flow patterns prevent circular dependencies
3. **Separation of Concerns**: UI state vs business logic boundaries maintained
4. **Performance First**: Minimal overhead for bridge operations
5. **Error Isolation**: Bridge failures don't cascade to core systems

## Event → Redux Bridge Patterns

### Pattern 1: Business Event to UI State Update

**Use Case**: When business operations complete, update UI to reflect changes

```typescript
// Location: src/core/store/middleware/eventToReduxBridge.ts

interface EventToReduxMapping {
  eventPattern: string;
  reduxAction: string;
  slice: string;
  transformer?: (eventData: any) => any;
  condition?: (eventData: any) => boolean;
  debounce?: number;
}

const EVENT_TO_REDUX_MAPPINGS: EventToReduxMapping[] = [
  {
    eventPattern: 'card.updated',
    reduxAction: 'cards/updateCard',
    slice: 'cards',
    transformer: (eventData) => ({
      id: eventData.id,
      ...eventData,
      lastUpdated: new Date().toISOString()
    }),
    condition: (eventData) => !!eventData.id && eventData.source !== 'redux'
  },
  
  {
    eventPattern: 'card.accepted',
    reduxAction: 'cards/updateCard',
    slice: 'cards',
    transformer: (eventData) => ({
      ...eventData.card,
      status: 'accepted',
      acceptedAt: eventData.timestamp
    })
  },
  
  {
    eventPattern: 'sync.started',
    reduxAction: 'sync/setSyncing',
    slice: 'sync',
    transformer: () => true
  },
  
  {
    eventPattern: 'sync.completed',
    reduxAction: 'sync/setSyncing',
    slice: 'sync',
    transformer: () => false
  },
  
  {
    eventPattern: 'offer.redeemed',
    reduxAction: 'offers/markRedeemed',
    slice: 'offers',
    transformer: (eventData) => ({
      offerId: eventData.offerId,
      redeemedAt: eventData.timestamp,
      redemptionId: eventData.redemptionId
    }),
    debounce: 100 // Prevent rapid-fire redemption UI updates
  },
  
  {
    eventPattern: 'auth.login_success',
    reduxAction: 'auth/login',
    slice: 'auth',
    transformer: (eventData) => ({
      user: eventData.user,
      tokens: eventData.tokens,
      method: eventData.method
    })
  }
];

class EventToReduxBridge {
  private store: any;
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
  
  constructor(store: any) {
    this.store = store;
    this.initializeEventListeners();
  }
  
  private initializeEventListeners(): void {
    EVENT_TO_REDUX_MAPPINGS.forEach(mapping => {
      globalEventEmitter.on(mapping.eventPattern, (eventData) => {
        this.processEventToRedux(mapping, eventData);
      });
    });
  }
  
  private processEventToRedux(mapping: EventToReduxMapping, eventData: any): void {
    try {
      // Apply condition check
      if (mapping.condition && !mapping.condition(eventData)) {
        return;
      }
      
      // Handle debouncing
      if (mapping.debounce) {
        const key = `${mapping.eventPattern}-${mapping.reduxAction}`;
        
        if (this.debounceTimers.has(key)) {
          clearTimeout(this.debounceTimers.get(key)!);
        }
        
        const timer = setTimeout(() => {
          this.executeReduxUpdate(mapping, eventData);
          this.debounceTimers.delete(key);
        }, mapping.debounce);
        
        this.debounceTimers.set(key, timer);
      } else {
        this.executeReduxUpdate(mapping, eventData);
      }
      
    } catch (error) {
      console.error(`Event to Redux bridge error for ${mapping.eventPattern}:`, error);
      globalEventEmitter.safeEmit('bridge.error', {
        type: 'event_to_redux',
        event: mapping.eventPattern,
        action: mapping.reduxAction,
        error: error.message
      });
    }
  }
  
  private executeReduxUpdate(mapping: EventToReduxMapping, eventData: any): void {
    const transformedData = mapping.transformer ? 
      mapping.transformer(eventData) : eventData;
    
    const action = {
      type: mapping.reduxAction,
      payload: transformedData
    };
    
    this.store.dispatch(action);
    
    // Log for debugging in development
    if (__DEV__) {
      console.log(`Event→Redux: ${mapping.eventPattern} → ${mapping.reduxAction}`, {
        originalData: eventData,
        transformedData,
        action
      });
    }
  }
}
```

### Pattern 2: Bulk Event Processing

**Use Case**: When multiple related events need to be processed as a batch

```typescript
// Location: src/core/store/middleware/batchEventBridge.ts

interface BatchMapping {
  eventPatterns: string[];
  reduxAction: string;
  batchWindow: number; // milliseconds
  accumulator: (events: any[]) => any;
}

const BATCH_MAPPINGS: BatchMapping[] = [
  {
    eventPatterns: ['card.updated', 'card.created', 'card.deleted'],
    reduxAction: 'cards/batchUpdate',
    batchWindow: 200,
    accumulator: (events) => ({
      updates: events.filter(e => e.type === 'card.updated').map(e => e.data),
      creates: events.filter(e => e.type === 'card.created').map(e => e.data),
      deletes: events.filter(e => e.type === 'card.deleted').map(e => e.data.id)
    })
  },
  
  {
    eventPatterns: ['analytics.*'],
    reduxAction: 'analytics/batchTrack',
    batchWindow: 1000,
    accumulator: (events) => ({
      events: events.map(e => ({
        type: e.type,
        data: e.data,
        timestamp: e.timestamp
      }))
    })
  }
];

class BatchEventProcessor {
  private store: any;
  private eventQueues: Map<string, any[]> = new Map();
  private timers: Map<string, NodeJS.Timeout> = new Map();
  
  constructor(store: any) {
    this.store = store;
    this.initializeBatchListeners();
  }
  
  private initializeBatchListeners(): void {
    BATCH_MAPPINGS.forEach(mapping => {
      mapping.eventPatterns.forEach(pattern => {
        if (pattern.includes('*')) {
          // Wildcard pattern - listen to all events and filter
          globalEventEmitter.onAny((type, data) => {
            if (this.matchesPattern(type, pattern)) {
              this.addToBatch(mapping, { type, data, timestamp: Date.now() });
            }
          });
        } else {
          globalEventEmitter.on(pattern, (data) => {
            this.addToBatch(mapping, { type: pattern, data, timestamp: Date.now() });
          });
        }
      });
    });
  }
  
  private addToBatch(mapping: BatchMapping, event: any): void {
    const key = mapping.reduxAction;
    
    if (!this.eventQueues.has(key)) {
      this.eventQueues.set(key, []);
    }
    
    this.eventQueues.get(key)!.push(event);
    
    // Reset timer
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key)!);
    }
    
    const timer = setTimeout(() => {
      this.processBatch(mapping);
    }, mapping.batchWindow);
    
    this.timers.set(key, timer);
  }
  
  private processBatch(mapping: BatchMapping): void {
    const key = mapping.reduxAction;
    const events = this.eventQueues.get(key) || [];
    
    if (events.length === 0) return;
    
    try {
      const batchData = mapping.accumulator(events);
      
      this.store.dispatch({
        type: mapping.reduxAction,
        payload: batchData
      });
      
      // Clear batch
      this.eventQueues.set(key, []);
      this.timers.delete(key);
      
    } catch (error) {
      console.error(`Batch processing error for ${mapping.reduxAction}:`, error);
    }
  }
  
  private matchesPattern(eventType: string, pattern: string): boolean {
    if (pattern.endsWith('*')) {
      const prefix = pattern.slice(0, -1);
      return eventType.startsWith(prefix);
    }
    return eventType === pattern;
  }
}
```

## Redux → Event Bridge Patterns

### Pattern 1: UI Action to Business Event

**Use Case**: When user interactions need to trigger business logic

```typescript
// Location: src/core/store/middleware/reduxToEventBridge.ts

interface ReduxToEventMapping {
  actionPattern: string;
  eventName: string;
  transformer?: (action: any) => any;
  condition?: (action: any) => boolean;
  async?: boolean;
}

const REDUX_TO_EVENT_MAPPINGS: ReduxToEventMapping[] = [
  {
    actionPattern: 'cards/selectCard',
    eventName: 'card.view',
    transformer: (action) => ({
      cardId: action.payload.cardId,
      context: action.payload.context || 'redux_selection',
      timestamp: new Date().toISOString()
    })
  },
  
  {
    actionPattern: 'offers/selectOffer',
    eventName: 'offer.view',
    transformer: (action) => ({
      offerId: action.payload.offerId,
      source: 'ui_selection',
      timestamp: new Date().toISOString()
    })
  },
  
  {
    actionPattern: 'auth/logout',
    eventName: 'auth.logout_initiated',
    transformer: (action) => ({
      source: 'user_action',
      timestamp: new Date().toISOString()
    })
  },
  
  {
    actionPattern: 'cards/setFilters',
    eventName: 'card.filter_changed',
    transformer: (action) => ({
      filters: action.payload,
      timestamp: new Date().toISOString()
    }),
    condition: (action) => Object.keys(action.payload).length > 0
  }
];

export const createReduxToEventMiddleware = (): Middleware => {
  return (store) => (next) => (action) => {
    // Execute the action first
    const result = next(action);
    
    // Then check for event mappings
    REDUX_TO_EVENT_MAPPINGS.forEach(mapping => {
      if (matchesActionPattern(action.type, mapping.actionPattern)) {
        processReduxToEvent(mapping, action, store);
      }
    });
    
    return result;
  };
};

function matchesActionPattern(actionType: string, pattern: string): boolean {
  if (pattern.includes('*')) {
    const regex = new RegExp(pattern.replace('*', '.*'));
    return regex.test(actionType);
  }
  return actionType === pattern;
}

function processReduxToEvent(
  mapping: ReduxToEventMapping, 
  action: any, 
  store: any
): void {
  try {
    // Apply condition check
    if (mapping.condition && !mapping.condition(action)) {
      return;
    }
    
    const eventData = mapping.transformer ? 
      mapping.transformer(action) : action.payload;
    
    if (mapping.async) {
      // Emit asynchronously to prevent blocking Redux
      setTimeout(() => {
        globalEventEmitter.safeEmit(mapping.eventName, eventData);
      }, 0);
    } else {
      globalEventEmitter.safeEmit(mapping.eventName, eventData);
    }
    
    if (__DEV__) {
      console.log(`Redux→Event: ${action.type} → ${mapping.eventName}`, {
        action,
        eventData
      });
    }
    
  } catch (error) {
    console.error(`Redux to Event bridge error for ${action.type}:`, error);
    globalEventEmitter.safeEmit('bridge.error', {
      type: 'redux_to_event',
      action: action.type,
      event: mapping.eventName,
      error: error.message
    });
  }
}
```

### Pattern 2: State Change Monitoring

**Use Case**: Monitor Redux state changes and emit business events

```typescript
// Location: src/core/store/middleware/stateChangeMonitor.ts

interface StateChangeMapping {
  selector: (state: RootState) => any;
  eventName: string;
  transformer?: (newValue: any, oldValue: any) => any;
  condition?: (newValue: any, oldValue: any) => boolean;
}

const STATE_CHANGE_MAPPINGS: StateChangeMapping[] = [
  {
    selector: (state) => state.auth.isAuthenticated,
    eventName: 'auth.status_changed',
    transformer: (newValue, oldValue) => ({
      authenticated: newValue,
      previouslyAuthenticated: oldValue,
      timestamp: new Date().toISOString()
    }),
    condition: (newValue, oldValue) => newValue !== oldValue
  },
  
  {
    selector: (state) => state.cards.selectedCardId,
    eventName: 'card.selection_changed',
    transformer: (newValue, oldValue) => ({
      selectedCardId: newValue,
      previousCardId: oldValue,
      timestamp: new Date().toISOString()
    }),
    condition: (newValue, oldValue) => newValue !== oldValue
  },
  
  {
    selector: (state) => state.sync.syncing,
    eventName: 'sync.status_changed',
    transformer: (newValue) => ({
      syncing: newValue,
      timestamp: new Date().toISOString()
    })
  }
];

export const createStateChangeMonitor = (): Middleware => {
  let previousState: RootState;
  
  return (store) => (next) => (action) => {
    const result = next(action);
    const currentState = store.getState();
    
    if (previousState) {
      STATE_CHANGE_MAPPINGS.forEach(mapping => {
        const oldValue = mapping.selector(previousState);
        const newValue = mapping.selector(currentState);
        
        if (!mapping.condition || mapping.condition(newValue, oldValue)) {
          const eventData = mapping.transformer ? 
            mapping.transformer(newValue, oldValue) : 
            { newValue, oldValue };
          
          globalEventEmitter.safeEmit(mapping.eventName, eventData);
        }
      });
    }
    
    previousState = currentState;
    return result;
  };
};
```

## Advanced Bridge Patterns

### Pattern 3: Bidirectional Synchronization

**Use Case**: When state needs to be kept in sync between Redux and external systems

```typescript
// Location: src/core/store/middleware/bidirectionalSync.ts

interface SyncMapping {
  reduxPath: string;
  eventPrefix: string;
  conflictResolution: 'redux_wins' | 'event_wins' | 'merge' | 'custom';
  merger?: (reduxValue: any, eventValue: any) => any;
}

const SYNC_MAPPINGS: SyncMapping[] = [
  {
    reduxPath: 'cards.cards',
    eventPrefix: 'card',
    conflictResolution: 'event_wins' // Business logic wins over UI state
  },
  
  {
    reduxPath: 'auth.user',
    eventPrefix: 'auth.user',
    conflictResolution: 'merge',
    merger: (reduxValue, eventValue) => ({
      ...reduxValue,
      ...eventValue,
      lastUpdated: Math.max(
        new Date(reduxValue.lastUpdated || 0).getTime(),
        new Date(eventValue.lastUpdated || 0).getTime()
      )
    })
  }
];

class BidirectionalSyncManager {
  private store: any;
  private syncLocks: Set<string> = new Set();
  
  constructor(store: any) {
    this.store = store;
    this.initializeSync();
  }
  
  private initializeSync(): void {
    // Monitor Redux changes
    let previousState = this.store.getState();
    
    this.store.subscribe(() => {
      const currentState = this.store.getState();
      
      SYNC_MAPPINGS.forEach(mapping => {
        const lockKey = `redux_${mapping.reduxPath}`;
        
        if (!this.syncLocks.has(lockKey)) {
          const oldValue = this.getValueAtPath(previousState, mapping.reduxPath);
          const newValue = this.getValueAtPath(currentState, mapping.reduxPath);
          
          if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
            this.syncReduxToEvent(mapping, newValue);
          }
        }
      });
      
      previousState = currentState;
    });
    
    // Monitor event changes
    SYNC_MAPPINGS.forEach(mapping => {
      globalEventEmitter.on(`${mapping.eventPrefix}.updated`, (eventData) => {
        this.syncEventToRedux(mapping, eventData);
      });
    });
  }
  
  private syncReduxToEvent(mapping: SyncMapping, value: any): void {
    const lockKey = `event_${mapping.eventPrefix}`;
    
    if (this.syncLocks.has(lockKey)) return;
    
    this.syncLocks.add(`redux_${mapping.reduxPath}`);
    
    globalEventEmitter.safeEmit(`${mapping.eventPrefix}.sync_from_redux`, {
      value,
      timestamp: new Date().toISOString()
    });
    
    setTimeout(() => {
      this.syncLocks.delete(`redux_${mapping.reduxPath}`);
    }, 100);
  }
  
  private syncEventToRedux(mapping: SyncMapping, eventData: any): void {
    const lockKey = `redux_${mapping.reduxPath}`;
    
    if (this.syncLocks.has(lockKey)) return;
    
    this.syncLocks.add(`event_${mapping.eventPrefix}`);
    
    const currentValue = this.getValueAtPath(this.store.getState(), mapping.reduxPath);
    let finalValue = eventData.value;
    
    // Handle conflicts
    if (mapping.conflictResolution === 'merge' && mapping.merger) {
      finalValue = mapping.merger(currentValue, eventData.value);
    } else if (mapping.conflictResolution === 'redux_wins') {
      finalValue = currentValue;
    }
    
    // Dispatch update to Redux
    this.updateReduxAtPath(mapping.reduxPath, finalValue);
    
    setTimeout(() => {
      this.syncLocks.delete(`event_${mapping.eventPrefix}`);
    }, 100);
  }
  
  private getValueAtPath(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
  
  private updateReduxAtPath(path: string, value: any): void {
    const [slice, ...pathParts] = path.split('.');
    
    // This would need to be implemented based on specific slice structure
    // Example for cards:
    if (slice === 'cards' && pathParts[0] === 'cards') {
      this.store.dispatch({ type: 'cards/setCards', payload: value });
    }
  }
}
```

### Pattern 4: Error Recovery and Resilience

```typescript
// Location: src/core/store/middleware/bridgeErrorRecovery.ts

interface ErrorRecoveryConfig {
  maxRetries: number;
  retryDelay: number;
  fallbackStrategies: {
    [errorType: string]: (error: any, context: any) => void;
  };
}

const ERROR_RECOVERY_CONFIG: ErrorRecoveryConfig = {
  maxRetries: 3,
  retryDelay: 1000,
  fallbackStrategies: {
    'bridge_timeout': (error, context) => {
      // Log error and continue without bridge
      console.warn('Bridge timeout, continuing without sync:', error);
    },
    
    'transform_error': (error, context) => {
      // Use raw data without transformation
      console.warn('Transform error, using raw data:', error);
      if (context.store && context.action) {
        context.store.dispatch({
          type: context.action.type,
          payload: context.originalData
        });
      }
    },
    
    'event_emission_failure': (error, context) => {
      // Store for later retry
      deferredEventProcessor.defer(context.eventName, context.eventData);
    }
  }
};

class BridgeErrorRecovery {
  private retryQueues: Map<string, any[]> = new Map();
  private retryTimers: Map<string, NodeJS.Timeout> = new Map();
  
  handleError(
    errorType: string, 
    error: Error, 
    context: any,
    retryFunction?: () => Promise<void>
  ): void {
    const strategy = ERROR_RECOVERY_CONFIG.fallbackStrategies[errorType];
    
    if (strategy) {
      strategy(error, context);
    }
    
    if (retryFunction) {
      this.scheduleRetry(errorType, retryFunction, context);
    }
    
    // Emit error for monitoring
    globalEventEmitter.safeEmit('bridge.error_handled', {
      errorType,
      error: error.message,
      context,
      timestamp: new Date().toISOString()
    });
  }
  
  private scheduleRetry(
    errorType: string,
    retryFunction: () => Promise<void>,
    context: any
  ): void {
    const key = `${errorType}_${JSON.stringify(context)}`;
    
    if (!this.retryQueues.has(key)) {
      this.retryQueues.set(key, []);
    }
    
    const retries = this.retryQueues.get(key)!;
    
    if (retries.length >= ERROR_RECOVERY_CONFIG.maxRetries) {
      console.error(`Max retries exceeded for ${errorType}:`, context);
      this.retryQueues.delete(key);
      return;
    }
    
    retries.push({ retryFunction, context, timestamp: Date.now() });
    
    const timer = setTimeout(async () => {
      try {
        await retryFunction();
        this.retryQueues.delete(key);
        this.retryTimers.delete(key);
      } catch (retryError) {
        console.error(`Retry failed for ${errorType}:`, retryError);
        this.scheduleRetry(errorType, retryFunction, context);
      }
    }, ERROR_RECOVERY_CONFIG.retryDelay * retries.length);
    
    this.retryTimers.set(key, timer);
  }
}

export const bridgeErrorRecovery = new BridgeErrorRecovery();
```

## Bridge Initialization and Setup

### Complete Bridge Setup

```typescript
// Location: src/core/store/middleware/index.ts

import { EventToReduxBridge } from './eventToReduxBridge';
import { BatchEventProcessor } from './batchEventBridge';
import { createReduxToEventMiddleware } from './reduxToEventBridge';
import { createStateChangeMonitor } from './stateChangeMonitor';
import { BidirectionalSyncManager } from './bidirectionalSync';

export const initializeBridgeSystem = (store: any) => {
  // Initialize all bridge components
  const eventToReduxBridge = new EventToReduxBridge(store);
  const batchProcessor = new BatchEventProcessor(store);
  const syncManager = new BidirectionalSyncManager(store);
  
  // Set up error handling
  globalEventEmitter.on('bridge.error', (errorData) => {
    console.error('Bridge system error:', errorData);
    // Send to crash analytics
    // Bugsnag.notify(new Error(errorData.error), {
    //   metaData: { bridge: errorData }
    // });
  });
  
  return {
    eventToReduxBridge,
    batchProcessor,
    syncManager
  };
};

// Middleware array for store configuration
export const bridgeMiddlewares = [
  createReduxToEventMiddleware(),
  createStateChangeMonitor()
];
```

## Testing Bridge Patterns

### Unit Tests for Bridge Components

```typescript
// Location: src/__tests__/bridge/eventToReduxBridge.test.ts

import { EventToReduxBridge } from '../core/store/middleware/eventToReduxBridge';
import { createMockStore } from './helpers/mockStore';

describe('EventToReduxBridge', () => {
  let store: any;
  let bridge: EventToReduxBridge;
  
  beforeEach(() => {
    store = createMockStore();
    bridge = new EventToReduxBridge(store);
  });
  
  it('should map card.updated event to Redux action', () => {
    const cardData = { id: 'test-card', name: 'Test Card' };
    
    globalEventEmitter.emit('card.updated', cardData);
    
    expect(store.dispatch).toHaveBeenCalledWith({
      type: 'cards/updateCard',
      payload: expect.objectContaining({
        id: 'test-card',
        name: 'Test Card',
        lastUpdated: expect.any(String)
      })
    });
  });
  
  it('should respect condition checks', () => {
    const invalidCardData = { name: 'No ID Card' }; // Missing required ID
    
    globalEventEmitter.emit('card.updated', invalidCardData);
    
    expect(store.dispatch).not.toHaveBeenCalled();
  });
  
  it('should handle debouncing correctly', (done) => {
    const offerData = { offerId: 'test-offer' };
    
    // Emit multiple rapid events
    globalEventEmitter.emit('offer.redeemed', offerData);
    globalEventEmitter.emit('offer.redeemed', offerData);
    globalEventEmitter.emit('offer.redeemed', offerData);
    
    // Should only dispatch once after debounce period
    setTimeout(() => {
      expect(store.dispatch).toHaveBeenCalledTimes(1);
      done();
    }, 150);
  });
});
```

### Integration Tests

```typescript
// Location: src/__tests__/bridge/integration.test.ts

import { store } from '../core/store';
import { globalEventEmitter } from '../core/events/globalEventEmitter';

describe('Bridge Integration', () => {
  it('should maintain bidirectional sync between events and Redux', (done) => {
    const testCard = { id: 'test-card', name: 'Integration Test Card' };
    
    // Step 1: Event should update Redux
    globalEventEmitter.emit('card.updated', testCard);
    
    setTimeout(() => {
      const cardState = store.getState().cards;
      const updatedCard = cardState.cards.find(c => c.id === 'test-card');
      
      expect(updatedCard).toEqual(expect.objectContaining(testCard));
      
      // Step 2: Redux action should emit event
      store.dispatch({
        type: 'cards/selectCard',
        payload: { cardId: 'test-card', context: 'integration_test' }
      });
      
      globalEventEmitter.once('card.view', (eventData) => {
        expect(eventData.cardId).toBe('test-card');
        expect(eventData.context).toBe('integration_test');
        done();
      });
    }, 50);
  });
});
```

## Performance Monitoring

### Bridge Performance Metrics

```typescript
// Location: src/core/store/middleware/bridgeMetrics.ts

interface BridgeMetrics {
  eventToReduxCount: number;
  reduxToEventCount: number;
  averageEventToReduxTime: number;
  averageReduxToEventTime: number;
  errorCount: number;
  lastError?: string;
}

class BridgePerformanceMonitor {
  private metrics: BridgeMetrics = {
    eventToReduxCount: 0,
    reduxToEventCount: 0,
    averageEventToReduxTime: 0,
    averageReduxToEventTime: 0,
    errorCount: 0
  };
  
  private timings: {
    eventToRedux: number[];
    reduxToEvent: number[];
  } = {
    eventToRedux: [],
    reduxToEvent: []
  };
  
  recordEventToRedux(duration: number): void {
    this.metrics.eventToReduxCount++;
    this.timings.eventToRedux.push(duration);
    
    // Keep only last 100 measurements
    if (this.timings.eventToRedux.length > 100) {
      this.timings.eventToRedux.shift();
    }
    
    this.metrics.averageEventToReduxTime = 
      this.timings.eventToRedux.reduce((a, b) => a + b, 0) / 
      this.timings.eventToRedux.length;
  }
  
  recordReduxToEvent(duration: number): void {
    this.metrics.reduxToEventCount++;
    this.timings.reduxToEvent.push(duration);
    
    if (this.timings.reduxToEvent.length > 100) {
      this.timings.reduxToEvent.shift();
    }
    
    this.metrics.averageReduxToEventTime = 
      this.timings.reduxToEvent.reduce((a, b) => a + b, 0) / 
      this.timings.reduxToEvent.length;
  }
  
  recordError(error: string): void {
    this.metrics.errorCount++;
    this.metrics.lastError = error;
  }
  
  getMetrics(): BridgeMetrics {
    return { ...this.metrics };
  }
  
  reset(): void {
    this.metrics = {
      eventToReduxCount: 0,
      reduxToEventCount: 0,
      averageEventToReduxTime: 0,
      averageReduxToEventTime: 0,
      errorCount: 0
    };
    this.timings.eventToRedux = [];
    this.timings.reduxToEvent = [];
  }
}

export const bridgePerformanceMonitor = new BridgePerformanceMonitor();
```

## Implementation Tasks for AI Agents

### Task 1: Core Bridge Infrastructure
```bash
# AI Implementation Tasks
□ Implement EventToReduxBridge class with mapping system
□ Create ReduxToEventMiddleware with action pattern matching
□ Add error handling and recovery mechanisms
□ Implement performance monitoring and metrics

# Validation Tasks
□ Unit tests for all bridge components
□ Integration tests for bidirectional communication
□ Performance benchmarking
□ Error scenario testing
```

### Task 2: Advanced Bridge Patterns
```bash
# AI Implementation Tasks
□ Implement BatchEventProcessor for bulk operations
□ Create StateChangeMonitor for Redux state watching
□ Add BidirectionalSyncManager for complex sync scenarios
□ Implement conflict resolution strategies

# Testing Tasks
□ Batch processing validation
□ State change monitoring verification
□ Sync conflict resolution testing
□ Performance impact assessment
```

### Task 3: Error Recovery and Resilience
```bash
# AI Implementation Tasks
□ Implement BridgeErrorRecovery with retry logic
□ Add fallback strategies for different error types
□ Create error monitoring and alerting
□ Implement graceful degradation patterns

# Quality Assurance
□ Error injection testing
□ Recovery mechanism validation
□ Fallback strategy verification
□ Monitoring system testing
```

### Task 4: Performance Optimization
```bash
# AI Implementation Tasks
□ Implement performance monitoring system
□ Add debouncing and throttling mechanisms
□ Create memory usage optimization
□ Implement selective bridge activation

# Performance Tasks
□ Benchmark bridge overhead
□ Memory leak detection
□ Performance regression testing
□ Optimization validation
```

## Usage Guidelines

### When to Use Each Bridge Pattern

#### Event → Redux Bridge
**Use for:**
- Updating UI state based on business events
- Reflecting completion of business operations
- Synchronizing external system changes
- Analytics and monitoring updates

**Example:**
```typescript
// Card acceptance business logic completes
globalEventEmitter.emit('card.accepted', { card, timestamp });

// Bridge automatically updates Redux UI state
// No manual Redux dispatch needed
```

#### Redux → Event Bridge
**Use for:**
- User interactions that need business logic
- UI state changes that affect business processes
- Analytics tracking of user actions
- Cross-component communication

**Example:**
```typescript
// User selects a card in UI
dispatch(cardActions.selectCard({ cardId, context: 'user_click' }));

// Bridge automatically emits business event
// Business logic controllers can respond
```

#### Bidirectional Sync
**Use for:**
- Critical state that must stay consistent
- Real-time collaborative features
- External system synchronization
- Complex state reconciliation

**Example:**
```typescript
// User profile changes in both UI and business logic
// Sync manager ensures consistency across both systems
```

### Best Practices

#### Performance Optimization
1. **Use debouncing** for high-frequency events
2. **Batch related operations** when possible
3. **Implement selective activation** for expensive bridges
4. **Monitor performance metrics** continuously

#### Error Handling
1. **Always provide fallback strategies**
2. **Implement retry logic** for transient failures
3. **Log errors comprehensively** for debugging
4. **Fail gracefully** without breaking user experience

#### Testing Strategy
1. **Test each bridge pattern independently**
2. **Validate integration scenarios**
3. **Performance test under load**
4. **Test error and recovery scenarios**

#### Development Workflow
1. **Start with simple mappings**
2. **Add complexity incrementally**
3. **Monitor performance continuously**
4. **Document all bridge configurations**

## Migration Strategy

### Phase 1: Basic Bridge Setup
- Implement core Event → Redux bridge
- Add basic Redux → Event middleware
- Test with simple mappings
- Monitor performance impact

### Phase 2: Advanced Patterns
- Add batch processing capabilities
- Implement state change monitoring
- Create bidirectional sync for critical data
- Add comprehensive error handling

### Phase 3: Optimization
- Fine-tune performance characteristics
- Implement advanced conflict resolution
- Add comprehensive monitoring
- Optimize for production deployment

### Phase 4: Full Integration
- Complete all bridge mappings
- Validate all business scenarios
- Performance test under load
- Deploy with monitoring

## Monitoring and Debugging

### Development Tools Integration
```typescript
// Location: src/core/store/middleware/bridgeDevTools.ts

class BridgeDevTools {
  private isEnabled: boolean = __DEV__;
  private eventLog: any[] = [];
  private maxLogSize: number = 1000;
  
  logEventToRedux(event: string, data: any, action: any): void {
    if (!this.isEnabled) return;
    
    this.addToLog({
      type: 'event_to_redux',
      timestamp: Date.now(),
      event,
      data,
      action,
      stackTrace: new Error().stack
    });
  }
  
  logReduxToEvent(action: any, event: string, data: any): void {
    if (!this.isEnabled) return;
    
    this.addToLog({
      type: 'redux_to_event',
      timestamp: Date.now(),
      action,
      event,
      data,
      stackTrace: new Error().stack
    });
  }
  
  private addToLog(entry: any): void {
    this.eventLog.push(entry);
    
    if (this.eventLog.length > this.maxLogSize) {
      this.eventLog.shift();
    }
    
    // Send to Redux DevTools if available
    if (window.__REDUX_DEVTOOLS_EXTENSION__) {
      window.__REDUX_DEVTOOLS_EXTENSION__.send(
        `Bridge: ${entry.type}`,
        entry
      );
    }
  }
  
  getEventLog(): any[] {
    return [...this.eventLog];
  }
  
  clearLog(): void {
    this.eventLog = [];
  }
}

export const bridgeDevTools = new BridgeDevTools();
```

### Production Monitoring
```typescript
// Location: src/core/store/middleware/bridgeMonitoring.ts

interface BridgeAlert {
  type: 'performance' | 'error' | 'volume';
  threshold: number;
  action: (metrics: any) => void;
}

const BRIDGE_ALERTS: BridgeAlert[] = [
  {
    type: 'performance',
    threshold: 10, // ms
    action: (metrics) => {
      console.warn('Bridge performance degraded:', metrics);
      // Send to monitoring service
    }
  },
  
  {
    type: 'error',
    threshold: 5, // errors per minute
    action: (metrics) => {
      console.error('High bridge error rate:', metrics);
      // Alert development team
    }
  },
  
  {
    type: 'volume',
    threshold: 1000, // events per minute
    action: (metrics) => {
      console.info('High bridge volume:', metrics);
      // Scale resources if needed
    }
  }
];

class BridgeMonitoring {
  private metrics: Map<string, number> = new Map();
  private alertTimers: Map<string, NodeJS.Timeout> = new Map();
  
  recordMetric(type: string, value: number): void {
    const key = `${type}_${Math.floor(Date.now() / 60000)}`; // Per minute
    const current = this.metrics.get(key) || 0;
    this.metrics.set(key, current + value);
    
    this.checkAlerts(type);
  }
  
  private checkAlerts(type: string): void {
    const alert = BRIDGE_ALERTS.find(a => a.type === type);
    if (!alert) return;
    
    const currentMinute = Math.floor(Date.now() / 60000);
    const key = `${type}_${currentMinute}`;
    const value = this.metrics.get(key) || 0;
    
    if (value > alert.threshold) {
      const alertKey = `${type}_alert`;
      
      if (!this.alertTimers.has(alertKey)) {
        alert.action({ type, value, threshold: alert.threshold });
        
        // Prevent alert spam
        const timer = setTimeout(() => {
          this.alertTimers.delete(alertKey);
        }, 60000);
        
        this.alertTimers.set(alertKey, timer);
      }
    }
  }
  
  getMetrics(): Map<string, number> {
    return new Map(this.metrics);
  }
  
  cleanup(): void {
    const cutoff = Math.floor(Date.now() / 60000) - 60; // Keep last hour
    
    for (const [key] of this.metrics) {
      const timestamp = parseInt(key.split('_').pop() || '0');
      if (timestamp < cutoff) {
        this.metrics.delete(key);
      }
    }
  }
}

export const bridgeMonitoring = new BridgeMonitoring();

// Cleanup old metrics every 10 minutes
setInterval(() => bridgeMonitoring.cleanup(), 600000);
```

## Conclusion

This bridge system provides a robust, performant, and maintainable integration between Perkd's event-driven architecture and Redux Toolkit. The patterns ensure:

1. **Business Logic Preservation**: Complex business workflows remain in the proven event system
2. **UI State Management**: Modern Redux patterns for UI state and developer experience  
3. **Performance Optimization**: Minimal overhead with intelligent batching and debouncing
4. **Error Resilience**: Comprehensive error handling and recovery mechanisms
5. **Development Experience**: Rich tooling and debugging capabilities

The implementation allows for gradual migration while maintaining full backward compatibility and business continuity.