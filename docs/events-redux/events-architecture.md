# Perkd v7.0 Events Architecture - Enhanced Implementation Guide

**Document Version:** 1.0  
**Date:** January 2025  
**Status:** AI Implementation Ready  
**Target:** AI Coding Agents (Claude Code, GitHub Copilot, etc.)  

## Overview

This document provides implementation specifications for enhancing the existing event-driven architecture in Perkd v7.0. The goal is to preserve and optimize the proven event system while adding modern TypeScript support, performance improvements, and Redux integration points.

## Core Event Infrastructure

### Global Event Emitter Enhancement

**Location**: `src/core/events/globalEventEmitter.ts`

```typescript
import { EventEmitter2 } from 'eventemitter2';

interface GlobalEventEmitter extends EventEmitter2 {
  // Type-safe event emission
  safeEmit<T = any>(event: string, data: T): boolean;
  
  // Performance monitoring
  getMetrics(): EventMetrics;
  
  // Redux bridge integration
  enableReduxBridge(): void;
  disableReduxBridge(): void;
}

interface EventMetrics {
  totalEvents: number;
  eventsPerSecond: number;
  listenerCount: number;
  memoryUsage: number;
}

class PerkdEventEmitter extends EventEmitter2 implements GlobalEventEmitter {
  private metrics: EventMetrics;
  private reduxBridgeEnabled: boolean = false;
  private store?: any; // Redux store reference
  
  constructor() {
    super({
      wildcard: true,
      delimiter: '.',
      maxListeners: 50,
      verboseMemoryLeak: __DEV__
    });
    
    this.initializeMetrics();
    this.setupErrorHandling();
  }

  safeEmit<T = any>(event: string, data: T): boolean {
    try {
      this.updateMetrics(event);
      
      // Validate event data
      if (!this.validateEventData(event, data)) {
        console.warn(`Invalid event data for ${event}:`, data);
        return false;
      }
      
      // Emit to event system
      const result = this.emit(event, data);
      
      // Bridge to Redux if enabled
      if (this.reduxBridgeEnabled && this.store) {
        this.bridgeToRedux(event, data);
      }
      
      return result;
    } catch (error) {
      console.error(`Event emission failed for ${event}:`, error);
      return false;
    }
  }

  enableReduxBridge(store: any): void {
    this.store = store;
    this.reduxBridgeEnabled = true;
  }

  private bridgeToRedux(event: string, data: any): void {
    // Map events to Redux actions based on event configuration
    const reduxAction = this.mapEventToReduxAction(event, data);
    if (reduxAction && this.store) {
      this.store.dispatch(reduxAction);
    }
  }

  private mapEventToReduxAction(event: string, data: any): any {
    // Implementation will be generated by AI based on event definitions
    // This mapping will be auto-generated from EVENT_REDUX_MAPPING
    return null; // Placeholder for AI implementation
  }
}

// Global instance
export const globalEventEmitter = new PerkdEventEmitter();

// Backward compatibility
global.$ = global.$ || {};
Object.defineProperty(global.$, 'Event', {
  value: globalEventEmitter,
  writable: false,
  configurable: false
});
```

### Event Definitions Enhancement

**Location**: `src/core/events/eventDefinitions.ts`

```typescript
// Event type definitions
export interface EventDefinition {
  name: string;
  category: EventCategory;
  sync: boolean;
  priority: EventPriority;
  analytics: boolean;
  reduxMapping?: ReduxMappingConfig;
  validation?: EventValidationSchema;
  ttl?: number; // Time to live for deferred events
}

export enum EventCategory {
  BUSINESS = 'business',
  UI = 'ui',
  SYSTEM = 'system',
  ANALYTICS = 'analytics'
}

export enum EventPriority {
  CRITICAL = 0,
  HIGH = 1,
  NORMAL = 2,
  LOW = 3
}

export interface ReduxMappingConfig {
  action: string;
  slice: string;
  mapper?: (data: any) => any;
  condition?: (data: any) => boolean;
}

export interface EventValidationSchema {
  required: string[];
  optional?: string[];
  types: Record<string, string>;
}

// Enhanced event definitions with Redux mappings
export const EVENT_DEFINITIONS: Record<string, EventDefinition> = {
  'card.updated': {
    name: 'Card Updated',
    category: EventCategory.BUSINESS,
    sync: true,
    priority: EventPriority.HIGH,
    analytics: true,
    reduxMapping: {
      action: 'cards/updateCard',
      slice: 'cards',
      mapper: (data) => ({ id: data.id, ...data }),
      condition: (data) => !!data.id
    },
    validation: {
      required: ['id', 'updatedAt'],
      optional: ['number', 'displayName', 'status'],
      types: {
        id: 'string',
        updatedAt: 'string',
        number: 'string'
      }
    }
  },
  
  'card.view': {
    name: 'Card Viewed',
    category: EventCategory.ANALYTICS,
    sync: false,
    priority: EventPriority.LOW,
    analytics: true,
    reduxMapping: {
      action: 'analytics/trackEvent',
      slice: 'analytics',
      mapper: (data) => ({ event: 'card_view', ...data })
    }
  },
  
  'sync.started': {
    name: 'Sync Started',
    category: EventCategory.SYSTEM,
    sync: false,
    priority: EventPriority.NORMAL,
    analytics: false,
    reduxMapping: {
      action: 'sync/setSyncing',
      slice: 'sync',
      mapper: () => true
    }
  },
  
  'sync.completed': {
    name: 'Sync Completed',
    category: EventCategory.SYSTEM,
    sync: false,
    priority: EventPriority.NORMAL,
    analytics: false,
    reduxMapping: {
      action: 'sync/setSyncing',
      slice: 'sync',
      mapper: () => false
    }
  }
  
  // AI agents should expand this based on existing Events.json
};

// Type-safe event emission helpers
export class TypedEventEmitter {
  static emitCardUpdated(card: any): boolean {
    return globalEventEmitter.safeEmit('card.updated', card);
  }
  
  static emitCardViewed(cardId: string, context?: string): boolean {
    return globalEventEmitter.safeEmit('card.view', { 
      cardId, 
      context,
      timestamp: new Date().toISOString()
    });
  }
  
  static emitSyncStarted(): boolean {
    return globalEventEmitter.safeEmit('sync.started', {
      timestamp: new Date().toISOString()
    });
  }
  
  // AI should generate more typed emitters based on EVENT_DEFINITIONS
}
```

### Event Middleware System

**Location**: `src/core/events/eventMiddleware.ts`

```typescript
export interface EventMiddleware {
  name: string;
  priority: number;
  process(event: string, data: any, next: () => void): void;
}

export class EventMiddlewareManager {
  private middlewares: EventMiddleware[] = [];
  
  use(middleware: EventMiddleware): void {
    this.middlewares.push(middleware);
    this.middlewares.sort((a, b) => a.priority - b.priority);
  }
  
  process(event: string, data: any): any {
    let index = 0;
    let transformedData = data;
    
    const next = () => {
      if (index < this.middlewares.length) {
        const middleware = this.middlewares[index++];
        middleware.process(event, transformedData, next);
      }
    };
    
    next();
    return transformedData;
  }
}

// Validation middleware
export const validationMiddleware: EventMiddleware = {
  name: 'validation',
  priority: 1,
  process(event: string, data: any, next: () => void): void {
    const definition = EVENT_DEFINITIONS[event];
    if (definition?.validation) {
      const isValid = validateEventData(definition.validation, data);
      if (!isValid) {
        console.warn(`Event validation failed for ${event}:`, data);
        return; // Don't call next() to stop processing
      }
    }
    next();
  }
};

// Analytics middleware
export const analyticsMiddleware: EventMiddleware = {
  name: 'analytics',
  priority: 2,
  process(event: string, data: any, next: () => void): void {
    const definition = EVENT_DEFINITIONS[event];
    if (definition?.analytics) {
      // Send to AppEvents system
      // This integrates with existing AppEvents architecture
      window.AppEvents?.track(event, data);
    }
    next();
  }
};

// Performance monitoring middleware
export const performanceMiddleware: EventMiddleware = {
  name: 'performance',
  priority: 0,
  process(event: string, data: any, next: () => void): void {
    const startTime = performance.now();
    
    next();
    
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    if (duration > 10) { // Log slow events
      console.warn(`Slow event processing: ${event} took ${duration}ms`);
    }
  }
};

function validateEventData(schema: EventValidationSchema, data: any): boolean {
  // Check required fields
  for (const field of schema.required) {
    if (!(field in data)) {
      return false;
    }
  }
  
  // Check types
  for (const [field, expectedType] of Object.entries(schema.types)) {
    if (field in data && typeof data[field] !== expectedType) {
      return false;
    }
  }
  
  return true;
}
```

### Deferred Event Processing

**Location**: `src/core/events/deferredEvents.ts`

```typescript
interface DeferredEvent {
  id: string;
  event: string;
  data: any;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  nextRetry: number;
}

export class DeferredEventProcessor {
  private queue: DeferredEvent[] = [];
  private processing: boolean = false;
  private timer?: NodeJS.Timer;
  
  constructor() {
    this.startProcessing();
  }
  
  defer(event: string, data: any, maxRetries: number = 3): void {
    const deferredEvent: DeferredEvent = {
      id: this.generateId(),
      event,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries,
      nextRetry: Date.now() + 1000 // Retry in 1 second
    };
    
    this.queue.push(deferredEvent);
  }
  
  private async startProcessing(): Promise<void> {
    if (this.processing) return;
    
    this.processing = true;
    this.timer = setInterval(() => {
      this.processQueue();
    }, 1000);
  }
  
  private async processQueue(): Promise<void> {
    const now = Date.now();
    const readyEvents = this.queue.filter(event => event.nextRetry <= now);
    
    for (const event of readyEvents) {
      try {
        // Attempt to process the event
        const success = globalEventEmitter.safeEmit(event.event, event.data);
        
        if (success) {
          // Remove from queue
          this.queue = this.queue.filter(e => e.id !== event.id);
        } else {
          // Retry logic
          event.retryCount++;
          if (event.retryCount >= event.maxRetries) {
            // Give up
            this.queue = this.queue.filter(e => e.id !== event.id);
            console.error(`Failed to process deferred event after ${event.maxRetries} retries:`, event);
          } else {
            // Schedule retry with exponential backoff
            event.nextRetry = now + (Math.pow(2, event.retryCount) * 1000);
          }
        }
      } catch (error) {
        console.error('Error processing deferred event:', error);
      }
    }
  }
  
  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
  
  getQueueSize(): number {
    return this.queue.length;
  }
  
  clearQueue(): void {
    this.queue = [];
  }
}

export const deferredEventProcessor = new DeferredEventProcessor();
```

### Event System Integration Helpers

**Location**: `src/core/events/integrationHelpers.ts`

```typescript
import { AppState } from 'react-native';

export class EventSystemManager {
  private static instance: EventSystemManager;
  private middlewareManager: EventMiddlewareManager;
  private isInitialized: boolean = false;
  
  static getInstance(): EventSystemManager {
    if (!EventSystemManager.instance) {
      EventSystemManager.instance = new EventSystemManager();
    }
    return EventSystemManager.instance;
  }
  
  private constructor() {
    this.middlewareManager = new EventMiddlewareManager();
  }
  
  initialize(): void {
    if (this.isInitialized) return;
    
    // Set up middleware
    this.middlewareManager.use(performanceMiddleware);
    this.middlewareManager.use(validationMiddleware);
    this.middlewareManager.use(analyticsMiddleware);
    
    // Set up app state listeners
    this.setupAppStateHandling();
    
    // Set up error handling
    this.setupErrorHandling();
    
    this.isInitialized = true;
  }
  
  private setupAppStateHandling(): void {
    AppState.addEventListener('change', (nextAppState) => {
      if (nextAppState === 'background') {
        // Pause non-critical event processing
        globalEventEmitter.safeEmit('app.background', { timestamp: Date.now() });
      } else if (nextAppState === 'active') {
        // Resume event processing
        globalEventEmitter.safeEmit('app.foreground', { timestamp: Date.now() });
      }
    });
  }
  
  private setupErrorHandling(): void {
    globalEventEmitter.on('error', (error: Error) => {
      console.error('Event system error:', error);
      // Report to crash analytics
      // Bugsnag.notify(error);
    });
  }
  
  enableReduxIntegration(store: any): void {
    globalEventEmitter.enableReduxBridge(store);
  }
  
  getMetrics(): EventMetrics {
    return globalEventEmitter.getMetrics();
  }
}

// Utility functions for common event patterns
export const EventUtils = {
  // Create event listener with automatic cleanup
  createManagedListener<T>(
    event: string, 
    handler: (data: T) => void, 
    component?: any
  ): () => void {
    globalEventEmitter.on(event, handler);
    
    const cleanup = () => {
      globalEventEmitter.off(event, handler);
    };
    
    // Auto-cleanup for React components
    if (component && component.componentWillUnmount) {
      const originalUnmount = component.componentWillUnmount;
      component.componentWillUnmount = function() {
        cleanup();
        originalUnmount.call(this);
      };
    }
    
    return cleanup;
  },
  
  // Emit event with automatic validation and error handling
  emitSafe<T>(event: string, data: T): boolean {
    return globalEventEmitter.safeEmit(event, data);
  },
  
  // Create typed event emitter for specific domain
  createDomainEmitter(domain: string) {
    return {
      emit: <T>(event: string, data: T) => 
        globalEventEmitter.safeEmit(`${domain}.${event}`, data),
      
      on: <T>(event: string, handler: (data: T) => void) =>
        globalEventEmitter.on(`${domain}.${event}`, handler),
        
      off: <T>(event: string, handler: (data: T) => void) =>
        globalEventEmitter.off(`${domain}.${event}`, handler)
    };
  }
};
```

## Implementation Tasks for AI Agents

### Task 1: Migrate Existing Event Definitions
- Parse existing `src/lib/Events.json` 
- Convert to TypeScript with Redux mappings
- Generate typed event emitters
- Create validation schemas

### Task 2: Enhance Global Event Infrastructure
- Implement the enhanced PerkdEventEmitter class
- Add performance monitoring and metrics
- Integrate with Redux bridge pattern
- Add error handling and recovery

### Task 3: Create Event Middleware System
- Implement middleware manager
- Create validation, analytics, and performance middlewares
- Add configuration for middleware chains
- Test middleware integration

### Task 4: Implement Deferred Event Processing
- Build the deferred event queue system
- Add retry logic with exponential backoff
- Integrate with app state management
- Add persistence for critical deferred events

### Task 5: Integration Helpers
- Create the EventSystemManager singleton
- Add utility functions for common patterns
- Implement managed listeners with auto-cleanup
- Add domain-specific event emitters

## Testing Requirements

### Unit Tests
- Event emission and handling
- Middleware processing
- Validation logic
- Deferred event processing
- Redux bridge functionality

### Integration Tests
- Event flow through complete business processes
- Redux state synchronization
- Performance under load
- Error handling and recovery

### Performance Tests
- Event processing speed benchmarks
- Memory usage validation
- Listener cleanup verification
- Queue processing efficiency

## Migration Notes

### Backward Compatibility
- All existing event patterns continue to work
- Global `$.Event` interface preserved
- Existing event definitions remain valid
- No breaking changes to controller patterns

### Performance Considerations
- Enhanced event emitter is faster than EventEmitter3
- Middleware adds ~1ms overhead per event
- Deferred processing doesn't block UI thread
- Redux bridge adds minimal overhead

### Error Handling
- All event operations are safe by default
- Validation prevents invalid events
- Automatic retry for failed operations
- Comprehensive error logging and reporting