# Perkd v7.0 Overall Architecture - Event-Driven with Selective Redux

**Document Version:** 1.0  
**Date:** January 2025  
**Status:** Final Implementation Guide  
**Context:** Preserving proven event-driven architecture with strategic Redux integration  

## Executive Summary

This document defines the complete architectural approach for Perkd v7.0, preserving the sophisticated event-driven system while adding Redux Toolkit for specific use cases that benefit from standardized patterns and AI code generation.

## Complementary Architecture Understanding

**Critical Insight**: The Events-Redux architecture works **alongside** the existing Realm middleware, not as a replacement. These systems serve different, complementary purposes in a three-layer integration pattern.

### System Integration Overview

The v7 architecture consists of three complementary layers:

1. **Business Logic Layer**: Event-driven workflows and cross-domain coordination
2. **State Management Layer**: Redux for UI state + Event-Redux bridge for coordination
3. **Persistence Layer**: Realm middleware for database sync + MMKV for Redux persistence

```mermaid
graph TB
    subgraph "🎯 Business Logic Layer"
        EVENTS[Event System<br/>$.Event Global Bus]
        CONTROLLERS[Business Controllers<br/>Complex Workflows]
        ANALYTICS[AppEvents<br/>Analytics Pipeline]
    end

    subgraph "🔄 State Management Layer"
        REDUX[Redux Store<br/>UI State & Cache]
        SLICES[Redux Slices<br/>cards, offers, person]
    end

    subgraph "🗄️ Persistence Layer"
        REALM_MW[Realm Middleware<br/>Database Sync]
        REALM_DB[(Realm Database<br/>Persistent Storage)]
        MMKV[(MMKV Storage<br/>Redux Persistence)]
    end

    subgraph "🎨 UI Layer"
        COMPONENTS[React Components<br/>UI Interactions]
        SCREENS[Screen Components<br/>Navigation]
    end

    %% Business Logic Flow
    EVENTS -.->|Coordinates| CONTROLLERS
    CONTROLLERS -.->|Triggers| ANALYTICS

    %% Event-Redux Bridge (NEW)
    EVENTS -.->|Business Events| REDUX
    REDUX -.->|UI Actions| EVENTS

    %% Realm-Redux Bridge (EXISTING)
    REDUX <-->|Bidirectional Sync| REALM_MW
    REALM_MW <-->|CRUD Operations| REALM_DB

    %% Redux Persistence
    REDUX <-->|State Persistence| MMKV

    %% UI Integration
    COMPONENTS -->|Dispatch Actions| REDUX
    REDUX -->|State Updates| COMPONENTS
    SCREENS --> COMPONENTS
```

### System Responsibilities

#### 1. Realm Middleware (EXISTING - PRESERVE)
**Purpose**: Database Persistence Layer
- **Bidirectional Sync**: Redux ↔ Realm Database
- **Data Persistence**: Ensures Redux state changes are persisted to database
- **Database Change Notifications**: Updates Redux when database changes externally
- **CRUD Operations**: Handles create, read, update, delete operations

**Implementation**: `src/store/middleware/realmMiddleware.ts` (already working)

#### 2. Events-Redux Bridge (NEW - ADD)
**Purpose**: Business Logic Coordination & UI State Management
- **Event System**: Complex business workflows and cross-domain coordination
- **Redux**: UI state, API caching, standardized patterns
- **Bridge Middleware**: Event ↔ Redux communication for business logic

**Implementation**: `src/core/store/middleware/eventBridge.ts` (to be added)

#### 3. MMKV Storage (UPGRADE)
**Purpose**: Redux State Persistence
- **UI State Persistence**: User preferences, navigation state, form data
- **Performance**: Faster than AsyncStorage for Redux state
- **Separate from Database**: Complements Realm for different data types

**Implementation**: `src/core/store/storage.ts` (to be enhanced)

## Architectural Philosophy

### Core Principle: **Preserve What Works, Enhance What Needs It**

The v7 architecture maintains the proven event-driven foundation while introducing Redux Toolkit for scenarios where it provides clear value:

- **Events**: Complex business logic, cross-domain coordination, analytics, mobile-specific concerns
- **Redux**: UI state management, form handling, API caching, standardized patterns for AI generation

## Implementation Strategy: Enhance, Don't Replace

### What We Keep (PRESERVE)

#### Realm Middleware System
- **Location**: `src/store/middleware/realmMiddleware.ts`
- **Status**: ✅ **Working perfectly - DO NOT CHANGE**
- **Purpose**: Bidirectional Redux ↔ Realm database synchronization
- **Functionality**:
  - Database change listeners update Redux state
  - Redux actions persist to Realm database
  - CRUD operations with error handling
  - Performance optimized for v7 clean schema

#### Event System Foundation
- **Location**: `src/lib/common/utils/Emitter.js`, `$.Event` global interface
- **Status**: ✅ **Production-tested - ENHANCE ONLY**
- **Purpose**: Complex business logic coordination
- **Functionality**:
  - 240+ event definitions in `Events.json`
  - AppEvents analytics pipeline
  - Cross-domain business workflows
  - Mobile lifecycle management

### What We Add (NEW)

#### Event-Redux Bridge Middleware
- **Location**: `src/core/store/middleware/eventBridge.ts` (to be created)
- **Purpose**: Connect business events with Redux UI state
- **Functionality**:
  - Event → Redux: Business events update UI state
  - Redux → Event: UI actions trigger business events
  - Performance optimized with debouncing and batching
  - Error handling and recovery mechanisms

#### Enhanced Redux Store Configuration
- **Location**: `src/core/store/index.ts` (to be enhanced)
- **Purpose**: Modern Redux setup with MMKV persistence
- **Functionality**:
  - MMKV storage for Redux state persistence
  - Integration with both Realm middleware and Event bridge
  - TypeScript definitions and validation
  - Redux DevTools integration

### Final Store Configuration

```typescript
// Store with BOTH middleware systems working together
export const store = configureStore({
  reducer: { /* existing slices */ },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
      .concat(realmListenerMiddleware.middleware)  // KEEP: Database sync
      .concat(eventBridgeMiddleware.middleware),   // ADD: Event-Redux bridge
});
```

## System Architecture Overview

```mermaid
graph TB
    subgraph "🎯 Business Layer"
        EVENTS[Event System<br/>$.Event Global Bus]
        BUSINESS[Business Logic<br/>Controllers & Services]
        ANALYTICS[AppEvents<br/>Analytics Pipeline]
    end

    subgraph "🔄 State Layer"
        REDUX[Redux Toolkit<br/>UI State & Cache]
        REALM[Realm Database<br/>Persistent Storage]
        MEMORY[Memory Cache<br/>Runtime State]
    end

    subgraph "🎨 Presentation Layer"
        SCREENS[Screen Components<br/>Navigation Targets]
        FEATURES[Feature Components<br/>Business Functions]
        SHARED[Shared UI<br/>Design System]
    end

    subgraph "🌐 External Layer"
        API[API Services<br/>Network Requests]
        PLATFORM[Platform Services<br/>Native Features]
        SYNC[Sync Engine<br/>Background Operations]
    end

    %% Business Logic Flow
    EVENTS -.->|Coordinates| BUSINESS
    BUSINESS -.->|Triggers| ANALYTICS
    EVENTS -.->|Enriches| ANALYTICS

    %% State Management Flow
    BUSINESS --> REALM
    REDUX --> MEMORY
    EVENTS -.->|Updates| REDUX
    REALM -.->|Loads| REDUX

    %% UI Flow
    REDUX --> SCREENS
    REDUX --> FEATURES
    FEATURES --> SHARED
    SCREENS --> FEATURES

    %% External Integration
    BUSINESS --> API
    BUSINESS --> PLATFORM
    API -.->|Success/Error| EVENTS
    PLATFORM -.->|Hardware Events| EVENTS
    SYNC -.->|Background Sync| EVENTS

    %% Styling
    classDef business fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef state fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef ui fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef external fill:#d69e2e,stroke:#ed8936,stroke-width:2px,color:#ffffff

    class EVENTS,BUSINESS,ANALYTICS business
    class REDUX,REALM,MEMORY state
    class SCREENS,FEATURES,SHARED ui
    class API,PLATFORM,SYNC external
```

## Domain Boundaries and Responsibilities

### Event System Domain (Preserved & Enhanced)

**Primary Responsibilities:**
- Business process orchestration (card lifecycle, offer redemption)
- Cross-domain coordination (card → offer → analytics → notification)
- Mobile lifecycle management (background sync, offline queuing)
- Analytics and business intelligence pipeline
- Regulatory compliance and audit trails

**Why Events Excel Here:**
- **Decoupled Communication**: Business domains can evolve independently
- **Mobile Optimization**: Built for offline scenarios and background operations
- **Business Intelligence**: Comprehensive event enrichment and analytics
- **Temporal Coordination**: Complex workflows across multiple app sessions

### Redux Domain (New for v7)

**Primary Responsibilities:**
- UI state management (loading states, form data, navigation preferences)
- API response caching and synchronization
- Standardized patterns for AI code generation
- Development tooling and debugging
- User preference persistence

**Why Redux Excels Here:**
- **DevTools Integration**: Superior debugging and state inspection
- **Predictable Patterns**: Standardized actions, reducers, selectors
- **AI-Friendly**: Clear patterns that AI can generate effectively
- **Type Safety**: Excellent TypeScript integration
- **Testing**: Straightforward unit testing patterns

## Integration Strategy

### Event → Redux Bridge Pattern

Events trigger Redux state updates for UI-relevant changes:

```typescript
// Event listeners update Redux state
$.Event.on(EVENT.Card.updated, (card) => {
  store.dispatch(cardSlice.actions.updateCard(card));
});

$.Event.on(EVENT.Sync.started, () => {
  store.dispatch(syncSlice.actions.setSyncing(true));
});
```

### Redux → Event Bridge Pattern

Redux actions trigger business events when needed:

```typescript
// Redux middleware emits business events
const businessEventMiddleware: Middleware = (store) => (next) => (action) => {
  const result = next(action);
  
  // Emit events for business-relevant actions
  if (action.type === 'cards/selectCard') {
    $.Event.emit(EVENT.Card.view, { 
      cardId: action.payload,
      context: 'user_selection'
    });
  }
  
  return result;
};
```

## Data Flow Patterns

### Business Operations Flow

1. **User Action** → Component
2. **Component** → Business Controller (existing pattern)
3. **Controller** → Business Event emission
4. **Event System** → Cross-domain coordination
5. **Event Listeners** → Redux state updates
6. **Redux State** → UI re-render

### UI State Management Flow

1. **User Interaction** → Component
2. **Component** → Redux Action dispatch
3. **Redux Reducer** → State update
4. **React-Redux** → Component re-render
5. **Optional**: Redux Middleware → Business Event emission

## Feature Implementation Strategy

### New Features (Redux-First)

New v7 features should use Redux Toolkit patterns:

```typescript
// Feature slice with RTK Query
export const newFeatureApi = createApi({
  reducerPath: 'newFeatureApi',
  baseQuery: fetchBaseQuery({ /* config */ }),
  endpoints: (builder) => ({
    // AI can easily generate these patterns
  })
});

export const newFeatureSlice = createSlice({
  name: 'newFeature',
  initialState,
  reducers: {
    // Standard Redux patterns
  }
});
```

### Existing Features (Event-Preserved)

Maintain existing event-driven patterns:

```typescript
// Preserve existing controller patterns
class CardController {
  async acceptCard(cardId) {
    // Business logic remains in controllers
    const result = await CardService.accept(cardId);
    
    // Events coordinate cross-domain effects
    $.Event.emit(EVENT.Card.accepted, {
      cardId,
      timestamp: new Date(),
      context: this.context
    });
    
    return result;
  }
}
```

## Performance Considerations

### Event System Optimizations

- **Selective Event Processing**: Continue using skip patterns and conditional emission
- **Deferred Operations**: Maintain background processing for non-critical events
- **Memory Management**: Proper listener cleanup and weak references
- **Batch Operations**: Group related events for efficient processing

### Redux Optimizations

- **Selective Subscriptions**: Use React-Redux hooks with proper selectors
- **Memoization**: Implement reselect for expensive computations
- **Normalized State**: Structure for efficient updates and lookups
- **Code Splitting**: Lazy load feature slices

## Security Considerations

### Event System Security

- **Event Validation**: Validate event payloads before processing
- **Access Control**: Ensure events respect user permissions
- **Audit Logging**: Maintain comprehensive event audit trails
- **Data Classification**: Handle sensitive events with appropriate security

### Redux Security

- **State Sanitization**: Ensure sensitive data doesn't persist in Redux
- **Action Validation**: Validate action payloads
- **Middleware Security**: Secure handling of sensitive operations
- **DevTools**: Disable in production builds

## Migration Strategy

### Phase 1: Foundation (Weeks 1-2)
- Set up Redux Toolkit store configuration
- Implement basic bridge middleware
- Create initial slice patterns
- Establish development workflow

### Phase 2: New Features (Weeks 3-6)
- Implement new v7 features using Redux patterns
- Establish AI coding patterns and templates
- Create shared component libraries
- Test integration patterns

### Phase 3: Selective Migration (Weeks 7-12)
- Migrate UI-heavy features to Redux (settings, preferences)
- Preserve business-critical event flows
- Optimize bridge performance
- Complete testing and validation

### Phase 4: Optimization (Weeks 13-16)
- Performance optimization
- Documentation completion
- Team training
- Production deployment

## Success Metrics

### Technical Metrics
- **Code Generation Efficiency**: 80%+ AI-generated code for Redux features
- **Performance**: No degradation in app startup or operation speed
- **Reliability**: Zero regressions in existing business logic
- **Maintainability**: Reduced complexity for new feature development

### Business Metrics
- **Development Velocity**: Faster feature delivery for new functionality
- **Bug Reduction**: Fewer UI state synchronization issues
- **Team Productivity**: Reduced onboarding time for new developers
- **Scalability**: Improved ability to add new features and integrations

## Conclusion

This architecture preserves the proven event-driven foundation while strategically introducing Redux for scenarios where it provides clear benefits. The approach minimizes migration risk while enabling modern development patterns and AI-assisted code generation.

The key insight: **Use the right tool for the right job.** Events excel at business logic coordination; Redux excels at UI state management. Together, they provide a robust foundation for Perkd v7.0 and beyond.