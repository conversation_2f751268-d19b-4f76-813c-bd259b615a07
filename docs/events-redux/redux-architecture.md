# Perkd v7.0 Redux Architecture - Implementation Specification

**Document Version:** 1.0  
**Date:** January 2025  
**Status:** AI Implementation Ready  
**Target:** AI Coding Agents (Claude Code, GitHub Copilot, etc.)  

## Overview

This document provides complete implementation specifications for Redux Toolkit integration in Perkd v7.0. The Redux layer handles UI state management, API caching, and provides standardized patterns while integrating seamlessly with the existing event system.

## Store Configuration

### Root Store Setup

**Location**: `src/core/store/index.ts`

```typescript
import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { 
  persistStore, 
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER 
} from 'redux-persist';
import { MMKV } from 'react-native-mmkv';

// Import all slices and APIs
import { authSlice } from '../auth/authSlice';
import { cardSlice } from '../cards/cardSlice';
import { offerSlice } from '../offers/offerSlice';
import { syncSlice } from '../sync/syncSlice';
import { uiSlice } from '../ui/uiSlice';
import { analyticsSlice } from '../analytics/analyticsSlice';

// Import API slices
import { authApi } from '../auth/authApi';
import { cardApi } from '../cards/cardApi';
import { offerApi } from '../offers/offerApi';
import { syncApi } from '../sync/syncApi';

// Import middleware
import { eventBridgeMiddleware } from './middleware/eventBridge';
import { errorHandlingMiddleware } from './middleware/errorHandling';
import { analyticsMiddleware } from './middleware/analytics';

// Enhanced MMKV storage for Redux persistence
const mmkvStorage = new MMKV({
  id: 'redux-storage',
  encryptionKey: 'perkd-v7-redux-encryption-key'
});

const storage = {
  setItem: (key: string, value: string): Promise<void> => {
    mmkvStorage.set(key, value);
    return Promise.resolve();
  },
  getItem: (key: string): Promise<string | null> => {
    const value = mmkvStorage.getString(key);
    return Promise.resolve(value ?? null);
  },
  removeItem: (key: string): Promise<void> => {
    mmkvStorage.delete(key);
    return Promise.resolve();
  }
};

// Persistence configuration
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'cards', 'offers', 'ui'], // Only persist specific slices
  blacklist: ['sync', 'analytics'], // Don't persist temporary state
  version: 1,
  migrate: (state: any) => {
    // Handle migration between versions
    return Promise.resolve(state);
  }
};

// Root reducer
const rootReducer = {
  // Feature slices
  auth: authSlice.reducer,
  cards: cardSlice.reducer,
  offers: offerSlice.reducer,
  sync: syncSlice.reducer,
  ui: uiSlice.reducer,
  analytics: analyticsSlice.reducer,
  
  // API slices
  authApi: authApi.reducer,
  cardApi: cardApi.reducer,
  offerApi: offerApi.reducer,
  syncApi: syncApi.reducer
};

const persistedReducer = persistReducer(persistConfig, combineReducers(rootReducer));

// Store configuration
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
      immutableCheck: false, // Performance optimization for large state
    })
    .concat(
      // RTK Query middleware
      authApi.middleware,
      cardApi.middleware,
      offerApi.middleware,
      syncApi.middleware,
      
      // Custom middleware
      eventBridgeMiddleware,
      errorHandlingMiddleware,
      analyticsMiddleware
    ),
  devTools: __DEV__ && {
    name: 'Perkd v7.0',
    maxAge: 50,
    trace: true,
    traceLimit: 25
  }
});

export const persistor = persistStore(store);

// Set up RTK Query listeners
setupListeners(store.dispatch);

// Types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Initialize event bridge
import { EventSystemManager } from '../events/integrationHelpers';
EventSystemManager.getInstance().enableReduxIntegration(store);
```

### Event Bridge Middleware

**Location**: `src/core/store/middleware/eventBridge.ts`

```typescript
import { createListenerMiddleware, isAnyOf } from '@reduxjs/toolkit';
import { TypedEventEmitter } from '../../events/eventDefinitions';
import { globalEventEmitter } from '../../events/globalEventEmitter';

export const eventBridgeMiddleware = createListenerMiddleware();

// Redux → Event Bridge: Emit events when specific actions occur
eventBridgeMiddleware.startListening({
  matcher: isAnyOf(
    cardSlice.actions.selectCard,
    cardSlice.actions.updateCard,
    offerSlice.actions.selectOffer,
    authSlice.actions.login,
    authSlice.actions.logout
  ),
  effect: (action, listenerApi) => {
    const { type, payload } = action;
    
    switch (type) {
      case 'cards/selectCard':
        TypedEventEmitter.emitCardViewed(payload.cardId, 'redux_selection');
        break;
        
      case 'cards/updateCard':
        // Don't emit here - this action is triggered BY events
        break;
        
      case 'offers/selectOffer':
        globalEventEmitter.safeEmit('offer.view', {
          offerId: payload.offerId,
          source: 'redux_selection',
          timestamp: new Date().toISOString()
        });
        break;
        
      case 'auth/login':
        globalEventEmitter.safeEmit('auth.login_success', {
          userId: payload.userId,
          method: payload.method,
          timestamp: new Date().toISOString()
        });
        break;
        
      case 'auth/logout':
        globalEventEmitter.safeEmit('auth.logout', {
          userId: payload.userId,
          timestamp: new Date().toISOString()
        });
        break;
    }
  },
});

// Event → Redux Bridge: Listen to events and update Redux state
const setupEventListeners = (dispatch: AppDispatch) => {
  // Card events
  globalEventEmitter.on('card.updated', (data) => {
    dispatch(cardSlice.actions.updateCard(data));
  });
  
  globalEventEmitter.on('card.created', (data) => {
    dispatch(cardSlice.actions.addCard(data));
  });
  
  globalEventEmitter.on('card.deleted', (data) => {
    dispatch(cardSlice.actions.removeCard(data.id));
  });
  
  // Offer events
  globalEventEmitter.on('offer.updated', (data) => {
    dispatch(offerSlice.actions.updateOffer(data));
  });
  
  globalEventEmitter.on('offer.redeemed', (data) => {
    dispatch(offerSlice.actions.markRedeemed(data));
  });
  
  // Sync events
  globalEventEmitter.on('sync.started', () => {
    dispatch(syncSlice.actions.setSyncing(true));
  });
  
  globalEventEmitter.on('sync.completed', (data) => {
    dispatch(syncSlice.actions.setSyncing(false));
    dispatch(syncSlice.actions.setLastSync(data.timestamp));
  });
  
  globalEventEmitter.on('sync.failed', (data) => {
    dispatch(syncSlice.actions.setSyncing(false));
    dispatch(syncSlice.actions.setSyncError(data.error));
  });
  
  // Authentication events
  globalEventEmitter.on('auth.token_refreshed', (data) => {
    dispatch(authSlice.actions.updateTokens(data));
  });
  
  globalEventEmitter.on('auth.session_expired', () => {
    dispatch(authSlice.actions.logout());
  });
};

// Initialize event listeners when store is created
export const initializeEventBridge = (dispatch: AppDispatch) => {
  setupEventListeners(dispatch);
};
```

## Feature Slices

### Auth Slice

**Location**: `src/core/auth/authSlice.ts`

```typescript
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { authApi } from './authApi';

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  tokens: {
    access: string | null;
    refresh: string | null;
    expiresAt: string | null;
  };
  biometric: {
    enabled: boolean;
    type: 'fingerprint' | 'face' | 'passcode' | null;
  };
  loading: {
    login: boolean;
    logout: boolean;
    refresh: boolean;
    biometric: boolean;
  };
  error: string | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  tokens: {
    access: null,
    refresh: null,
    expiresAt: null
  },
  biometric: {
    enabled: false,
    type: null
  },
  loading: {
    login: false,
    logout: false,
    refresh: false,
    biometric: false
  },
  error: null
};

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Manual state updates (triggered by events)
    login: (state, action: PayloadAction<{
      user: User;
      tokens: any;
      method: string;
    }>) => {
      state.isAuthenticated = true;
      state.user = action.payload.user;
      state.tokens = action.payload.tokens;
      state.error = null;
    },
    
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.tokens = initialState.tokens;
      state.error = null;
    },
    
    updateTokens: (state, action: PayloadAction<any>) => {
      state.tokens = action.payload;
    },
    
    setBiometric: (state, action: PayloadAction<{
      enabled: boolean;
      type: 'fingerprint' | 'face' | 'passcode' | null;
    }>) => {
      state.biometric = action.payload;
    },
    
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    }
  },
  
  // Handle RTK Query states
  extraReducers: (builder) => {
    builder
      // Login
      .addMatcher(authApi.endpoints.login.matchPending, (state) => {
        state.loading.login = true;
        state.error = null;
      })
      .addMatcher(authApi.endpoints.login.matchFulfilled, (state, action) => {
        state.loading.login = false;
        // Don't update state here - let events handle it
      })
      .addMatcher(authApi.endpoints.login.matchRejected, (state, action) => {
        state.loading.login = false;
        state.error = action.error.message || 'Login failed';
      })
      
      // Logout
      .addMatcher(authApi.endpoints.logout.matchPending, (state) => {
        state.loading.logout = true;
      })
      .addMatcher(authApi.endpoints.logout.matchFulfilled, (state) => {
        state.loading.logout = false;
      })
      .addMatcher(authApi.endpoints.logout.matchRejected, (state) => {
        state.loading.logout = false;
      })
      
      // Biometric
      .addMatcher(authApi.endpoints.enableBiometric.matchPending, (state) => {
        state.loading.biometric = true;
      })
      .addMatcher(authApi.endpoints.enableBiometric.matchFulfilled, (state) => {
        state.loading.biometric = false;
      })
      .addMatcher(authApi.endpoints.enableBiometric.matchRejected, (state, action) => {
        state.loading.biometric = false;
        state.error = action.error.message || 'Biometric setup failed';
      });
  }
});

export const authActions = authSlice.actions;

// Selectors
export const selectAuth = (state: RootState) => state.auth;
export const selectIsAuthenticated = (state: RootState) => state.auth.isAuthenticated;
export const selectUser = (state: RootState) => state.auth.user;
export const selectTokens = (state: RootState) => state.auth.tokens;
export const selectBiometric = (state: RootState) => state.auth.biometric;
export const selectAuthLoading = (state: RootState) => state.auth.loading;
export const selectAuthError = (state: RootState) => state.auth.error;
```

### Cards Slice

**Location**: `src/core/cards/cardSlice.ts`

```typescript
import { createSlice, PayloadAction, createSelector } from '@reduxjs/toolkit';
import { cardApi } from './cardApi';

export interface CardState {
  cards: Card[];
  selectedCardId: string | null;
  viewMode: 'list' | 'grid' | 'big';
  sortOrder: 'custom' | 'name' | 'recent' | 'brand';
  filters: {
    categories: string[];
    brands: string[];
    status: ('active' | 'expired' | 'suspended')[];
  };
  searchQuery: string;
  loading: {
    fetch: boolean;
    update: boolean;
    delete: boolean;
    accept: boolean;
  };
  error: string | null;
  lastUpdated: string | null;
}

const initialState: CardState = {
  cards: [],
  selectedCardId: null,
  viewMode: 'big',
  sortOrder: 'custom',
  filters: {
    categories: [],
    brands: [],
    status: ['active']
  },
  searchQuery: '',
  loading: {
    fetch: false,
    update: false,
    delete: false,
    accept: false
  },
  error: null,
  lastUpdated: null
};

export const cardSlice = createSlice({
  name: 'cards',
  initialState,
  reducers: {
    // Card management (triggered by events)
    addCard: (state, action: PayloadAction<Card>) => {
      const existingIndex = state.cards.findIndex(c => c.id === action.payload.id);
      if (existingIndex >= 0) {
        state.cards[existingIndex] = action.payload;
      } else {
        state.cards.push(action.payload);
      }
      state.lastUpdated = new Date().toISOString();
    },
    
    updateCard: (state, action: PayloadAction<Card>) => {
      const index = state.cards.findIndex(c => c.id === action.payload.id);
      if (index >= 0) {
        state.cards[index] = { ...state.cards[index], ...action.payload };
        state.lastUpdated = new Date().toISOString();
      }
    },
    
    removeCard: (state, action: PayloadAction<string>) => {
      state.cards = state.cards.filter(c => c.id !== action.payload);
      if (state.selectedCardId === action.payload) {
        state.selectedCardId = null;
      }
      state.lastUpdated = new Date().toISOString();
    },
    
    setCards: (state, action: PayloadAction<Card[]>) => {
      state.cards = action.payload;
      state.lastUpdated = new Date().toISOString();
    },
    
    // UI state management
    selectCard: (state, action: PayloadAction<{ cardId: string; context?: string }>) => {
      state.selectedCardId = action.payload.cardId;
    },
    
    clearSelection: (state) => {
      state.selectedCardId = null;
    },
    
    setViewMode: (state, action: PayloadAction<'list' | 'grid' | 'big'>) => {
      state.viewMode = action.payload;
    },
    
    setSortOrder: (state, action: PayloadAction<'custom' | 'name' | 'recent' | 'brand'>) => {
      state.sortOrder = action.payload;
    },
    
    setFilters: (state, action: PayloadAction<Partial<CardState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    
    clearFilters: (state) => {
      state.filters = initialState.filters;
      state.searchQuery = '';
    },
    
    setError: (state, action: PayloadAction<string>) => {
      state.error = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    }
  },
  
  extraReducers: (builder) => {
    builder
      // Fetch cards
      .addMatcher(cardApi.endpoints.getCards.matchPending, (state) => {
        state.loading.fetch = true;
        state.error = null;
      })
      .addMatcher(cardApi.endpoints.getCards.matchFulfilled, (state, action) => {
        state.loading.fetch = false;
        // Cards are updated via events, not directly here
      })
      .addMatcher(cardApi.endpoints.getCards.matchRejected, (state, action) => {
        state.loading.fetch = false;
        state.error = action.error.message || 'Failed to fetch cards';
      })
      
      // Accept card
      .addMatcher(cardApi.endpoints.acceptCard.matchPending, (state) => {
        state.loading.accept = true;
        state.error = null;
      })
      .addMatcher(cardApi.endpoints.acceptCard.matchFulfilled, (state) => {
        state.loading.accept = false;
      })
      .addMatcher(cardApi.endpoints.acceptCard.matchRejected, (state, action) => {
        state.loading.accept = false;
        state.error = action.error.message || 'Failed to accept card';
      });
  }
});

export const cardActions = cardSlice.actions;

// Selectors
export const selectCards = (state: RootState) => state.cards;
export const selectAllCards = (state: RootState) => state.cards.cards;
export const selectSelectedCardId = (state: RootState) => state.cards.selectedCardId;
export const selectCardViewMode = (state: RootState) => state.cards.viewMode;
export const selectCardSortOrder = (state: RootState) => state.cards.sortOrder;
export const selectCardFilters = (state: RootState) => state.cards.filters;
export const selectCardSearchQuery = (state: RootState) => state.cards.searchQuery;
export const selectCardLoading = (state: RootState) => state.cards.loading;
export const selectCardError = (state: RootState) => state.cards.error;

// Memoized selectors
export const selectSelectedCard = createSelector(
  [selectAllCards, selectSelectedCardId],
  (cards, selectedId) => cards.find(card => card.id === selectedId) || null
);

export const selectFilteredCards = createSelector(
  [selectAllCards, selectCardFilters, selectCardSearchQuery],
  (cards, filters, searchQuery) => {
    let filtered = cards;
    
    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(card => filters.status.includes(card.status));
    }
    
    // Apply category filter
    if (filters.categories.length > 0) {
      filtered = filtered.filter(card => 
        filters.categories.some(cat => card.categories?.includes(cat))
      );
    }
    
    // Apply brand filter
    if (filters.brands.length > 0) {
      filtered = filtered.filter(card => filters.brands.includes(card.brand));
    }
    
    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(card =>
        card.displayName?.toLowerCase().includes(query) ||
        card.brand?.toLowerCase().includes(query) ||
        card.description?.toLowerCase().includes(query)
      );
    }
    
    return filtered;
  }
);

export const selectSortedCards = createSelector(
  [selectFilteredCards, selectCardSortOrder],
  (cards, sortOrder) => {
    const sorted = [...cards];
    
    switch (sortOrder) {
      case 'name':
        return sorted.sort((a, b) => (a.displayName || '').localeCompare(b.displayName || ''));
      case 'brand':
        return sorted.sort((a, b) => (a.brand || '').localeCompare(b.brand || ''));
      case 'recent':
        return sorted.sort((a, b) => 
          new Date(b.lastUsed || 0).getTime() - new Date(a.lastUsed || 0).getTime()
        );
      case 'custom':
      default:
        return sorted.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
    }
  }
);
```

## RTK Query API Slices

### Auth API

**Location**: `src/core/auth/authApi.ts`

```typescript
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { RootState } from '../store';

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/auth/',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.tokens.access;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Auth', 'User'],
  endpoints: (builder) => ({
    login: builder.mutation<
      { user: User; tokens: any },
      { email: string; password: string; biometric?: boolean }
    >({
      query: (credentials) => ({
        url: 'login',
        method: 'POST',
        body: credentials,
      }),
      invalidatesTags: ['Auth', 'User'],
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          const { data } = await queryFulfilled;
          // Let events handle the actual state update
          globalEventEmitter.safeEmit('auth.login_success', {
            user: data.user,
            tokens: data.tokens,
            method: arg.biometric ? 'biometric' : 'password'
          });
        } catch (error) {
          globalEventEmitter.safeEmit('auth.login_failed', {
            error: error.error?.message || 'Login failed',
            method: arg.biometric ? 'biometric' : 'password'
          });
        }
      },
    }),
    
    logout: builder.mutation<void, void>({
      query: () => ({
        url: 'logout',
        method: 'POST',
      }),
      invalidatesTags: ['Auth', 'User'],
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
          globalEventEmitter.safeEmit('auth.logout_success', {
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          // Logout locally even if server request fails
          globalEventEmitter.safeEmit('auth.logout_success', {
            timestamp: new Date().toISOString(),
            offline: true
          });
        }
      },
    }),
    
    refreshToken: builder.mutation<
      { tokens: any },
      { refresh: string }
    >({
      query: (data) => ({
        url: 'refresh',
        method: 'POST',
        body: data,
      }),
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          const { data } = await queryFulfilled;
          globalEventEmitter.safeEmit('auth.token_refreshed', data.tokens);
        } catch (error) {
          globalEventEmitter.safeEmit('auth.session_expired', {
            error: error.error?.message || 'Session expired'
          });
        }
      },
    }),
    
    enableBiometric: builder.mutation<
      { success: boolean },
      { type: 'fingerprint' | 'face' | 'passcode' }
    >({
      query: (data) => ({
        url: 'biometric/enable',
        method: 'POST',
        body: data,
      }),
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
          globalEventEmitter.safeEmit('auth.biometric_enabled', {
            type: arg.type,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          globalEventEmitter.safeEmit('auth.biometric_failed', {
            error: error.error?.message || 'Biometric setup failed',
            type: arg.type
          });
        }
      },
    }),
    
    getProfile: builder.query<User, void>({
      query: () => 'profile',
      providesTags: ['User'],
    }),
  }),
});

export const {
  useLoginMutation,
  useLogoutMutation,
  useRefreshTokenMutation,
  useEnableBiometricMutation,
  useGetProfileQuery,
} = authApi;
```

### Cards API

**Location**: `src/core/cards/cardApi.ts`

```typescript
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { RootState } from '../store';

export const cardApi = createApi({
  reducerPath: 'cardApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/cards/',
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth.tokens.access;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Card', 'CardList'],
  endpoints: (builder) => ({
    getCards: builder.query<Card[], void>({
      query: () => '',
      providesTags: ['CardList'],
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          const { data } = await queryFulfilled;
          globalEventEmitter.safeEmit('cards.fetched', {
            cards: data,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          globalEventEmitter.safeEmit('cards.fetch_failed', {
            error: error.error?.message || 'Failed to fetch cards'
          });
        }
      },
    }),
    
    getCard: builder.query<Card, string>({
      query: (id) => id,
      providesTags: (result, error, id) => [{ type: 'Card', id }],
    }),
    
    acceptCard: builder.mutation<
      Card,
      { cardId: string; location?: any }
    >({
      query: ({ cardId, location }) => ({
        url: `${cardId}/accept`,
        method: 'POST',
        body: { location },
      }),
      invalidatesTags: (result, error, { cardId }) => [
        { type: 'Card', id: cardId },
        'CardList'
      ],
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          const { data } = await queryFulfilled;
          globalEventEmitter.safeEmit('card.accepted', {
            card: data,
            location: arg.location,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          globalEventEmitter.safeEmit('card.accept_failed', {
            cardId: arg.cardId,
            error: error.error?.message || 'Failed to accept card'
          });
        }
      },
    }),
    
    deleteCard: builder.mutation<void, string>({
      query: (id) => ({
        url: id,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Card', id },
        'CardList'
      ],
      onQueryStarted: async (cardId, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
          globalEventEmitter.safeEmit('card.deleted', {
            cardId,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          globalEventEmitter.safeEmit('card.delete_failed', {
            cardId,
            error: error.error?.message || 'Failed to delete card'
          });
        }
      },
    }),
    
    updateCard: builder.mutation<
      Card,
      { id: string; updates: Partial<Card> }
    >({
      query: ({ id, updates }) => ({
        url: id,
        method: 'PATCH',
        body: updates,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Card', id },
        'CardList'
      ],
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          const { data } = await queryFulfilled;
          globalEventEmitter.safeEmit('card.updated', {
            card: data,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          globalEventEmitter.safeEmit('card.update_failed', {
            cardId: arg.id,
            error: error.error?.message || 'Failed to update card'
          });
        }
      },
    }),
  }),
});

export const {
  useGetCardsQuery,
  useGetCardQuery,
  useAcceptCardMutation,
  useDeleteCardMutation,
  useUpdateCardMutation,
} = cardApi;
```

## Typed Hooks

**Location**: `src/core/store/hooks.ts`

```typescript
import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import type { RootState, AppDispatch } from './index';

// Typed hooks for Redux
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Custom hooks for common patterns
export const useAuthState = () => {
  return useAppSelector(selectAuth);
};

export const useCardState = () => {
  return useAppSelector(selectCards);
};

export const useIsLoading = (feature: string) => {
  return useAppSelector((state: RootState) => {
    switch (feature) {
      case 'auth':
        return Object.values(state.auth.loading).some(Boolean);
      case 'cards':
        return Object.values(state.cards.loading).some(Boolean);
      default:
        return false;
    }
  });
};

// Hook for handling API loading states
export const useApiLoading = (api: string, endpoint: string) => {
  return useAppSelector((state: RootState) => {
    const apiState = (state as any)[api];
    return apiState?.queries?.[endpoint]?.isLoading || false;
  });
};
```

## Implementation Tasks for AI Agents

### Task 1: Store Configuration
- Set up the Redux store with persistence
- Configure RTK Query APIs
- Implement custom middleware
- Add TypeScript types

### Task 2: Feature Slices
- Implement auth slice with biometric support
- Create cards slice with filtering/sorting
- Add offers slice for promotions
- Build sync slice for background operations
- Create UI slice for app preferences

### Task 3: API Slices  
- Build auth API with event integration
- Create cards API with CRUD operations
- Implement offers API for redemptions
- Add sync API for background sync

### Task 4: Bridge Integration
- Implement event bridge middleware
- Create Redux action → event mappings
- Set up event listeners for Redux updates
- Add error handling for bridge failures

### Task 5: Performance Optimization
- Configure selector memoization
- Implement lazy loading for slices
- Add bundle size optimization
- Set up development tools integration

### Task 6: Testing Infrastructure
- Create test utilities for Redux
- Add mock API responses
- Test event bridge integration
- Validate persistence functionality

## Usage Patterns for Components

### Using Redux in React Components

```typescript
import React from 'react';
import { useAppSelector, useAppDispatch } from '../core/store/hooks';
import { cardActions, selectSortedCards, selectCardLoading } from '../core/cards/cardSlice';
import { useGetCardsQuery } from '../core/cards/cardApi';

export const CardList: React.FC = () => {
  const dispatch = useAppDispatch();
  const cards = useAppSelector(selectSortedCards);
  const loading = useAppSelector(selectCardLoading);
  
  // RTK Query for data fetching
  const { 
    data: cardsData, 
    error, 
    isLoading: isQueryLoading 
  } = useGetCardsQuery();
  
  const handleCardSelect = (cardId: string) => {
    // This will trigger event emission via middleware
    dispatch(cardActions.selectCard({ cardId, context: 'list_selection' }));
  };
  
  const handleViewModeChange = (mode: 'list' | 'grid' | 'big') => {
    dispatch(cardActions.setViewMode(mode));
  };
  
  if (isQueryLoading || loading.fetch) {
    return <LoadingSpinner />;
  }
  
  return (
    <View>
      <ViewModeSelector 
        mode={useAppSelector(selectCardViewMode)}
        onChange={handleViewModeChange}
      />
      
      <FlatList
        data={cards}
        renderItem={({ item }) => (
          <CardItem 
            card={item}
            onPress={() => handleCardSelect(item.id)}
          />
        )}
        keyExtractor={(item) => item.id}
      />
    </View>
  );
};
```

### Using Events in Business Logic

```typescript
import { globalEventEmitter } from '../core/events/globalEventEmitter';

export class CardController {
  async acceptCard(cardId: string, location?: any): Promise<Card> {
    try {
      // Business logic
      const result = await CardService.accept(cardId, location);
      
      // Event emission handled by RTK Query onQueryStarted
      // No need to manually emit here
      
      return result;
    } catch (error) {
      // Error events still handled manually for complex error logic
      globalEventEmitter.safeEmit('card.accept_failed', {
        cardId,
        error: error.message,
        location,
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }
  
  // Complex business operations that span multiple domains
  async processCardAcceptance(cardId: string): Promise<void> {
    // This uses pure event-driven coordination
    globalEventEmitter.safeEmit('card.acceptance_started', { cardId });
    
    try {
      // 1. Accept card
      const card = await this.acceptCard(cardId);
      
      // 2. Process related offers (handled by event listeners)
      globalEventEmitter.safeEmit('offers.activate_for_card', { cardId, card });
      
      // 3. Update analytics (handled by AppEvents)
      globalEventEmitter.safeEmit('analytics.card_accepted', { cardId, card });
      
      // 4. Send notifications (handled by notification service)
      globalEventEmitter.safeEmit('notifications.card_acceptance', { cardId, card });
      
      globalEventEmitter.safeEmit('card.acceptance_completed', { cardId, card });
      
    } catch (error) {
      globalEventEmitter.safeEmit('card.acceptance_failed', { cardId, error });
    }
  }
}
```

## Migration Guidelines

### When to Use Redux vs Events

**Use Redux for:**
- UI state (loading, form data, preferences)
- Data caching and normalization
- Derived state calculations
- Component-to-component communication
- Developer tooling and debugging

**Use Events for:**
- Cross-domain business logic
- Complex workflow orchestration
- Analytics and tracking
- Background processing
- Mobile lifecycle management
- Legacy business logic preservation

### Migration Strategy

1. **Start with new features using Redux patterns**
2. **Gradually move UI state to Redux slices**
3. **Preserve complex business logic in events**
4. **Bridge the two systems with middleware**
5. **Optimize performance after migration**

## Error Handling

### Redux Error Patterns

```typescript
// Slice error handling
export const cardSlice = createSlice({
  name: 'cards',
  initialState,
  reducers: {
    setError: (state, action: PayloadAction<{
      type: 'fetch' | 'update' | 'delete' | 'accept';
      message: string;
      cardId?: string;
    }>) => {
      state.error = action.payload.message;
      // Stop relevant loading state
      state.loading[action.payload.type] = false;
    },
    clearError: (state) => {
      state.error = null;
    }
  }
});

// API error handling with events
const cardApi = createApi({
  // ... config
  endpoints: (builder) => ({
    acceptCard: builder.mutation({
      // ... config
      onQueryStarted: async (arg, { dispatch, queryFulfilled }) => {
        try {
          await queryFulfilled;
        } catch (error) {
          // Handle both Redux state and event emission
          dispatch(cardSlice.actions.setError({
            type: 'accept',
            message: error.error?.message || 'Failed to accept card',
            cardId: arg.cardId
          }));
          
          globalEventEmitter.safeEmit('card.accept_failed', {
            cardId: arg.cardId,
            error: error.error?.message,
            context: 'api_call'
          });
        }
      }
    })
  })
});
```

### Event Error Handling

```typescript
// Error recovery in event system
globalEventEmitter.on('card.accept_failed', (data) => {
  // Business logic for error recovery
  if (data.error?.includes('network')) {
    // Queue for retry
    deferredEventProcessor.defer('card.accept_retry', {
      cardId: data.cardId,
      originalError: data.error
    });
  } else {
    // Permanent failure
    globalEventEmitter.safeEmit('card.acceptance_permanently_failed', data);
  }
});
```

## Performance Considerations

### Redux Performance

- **Memoized Selectors**: Use `createSelector` for expensive computations
- **Component Optimization**: Use `React.memo` with Redux-connected components  
- **Batch Updates**: RTK automatically batches actions
- **Normalized State**: Structure state for efficient updates

### Event Performance

- **Selective Listeners**: Remove unused event listeners
- **Deferred Processing**: Use background processing for non-critical events
- **Event Batching**: Group related events for efficient processing
- **Memory Management**: Implement proper cleanup for component lifecycle

## Testing Strategies

### Redux Testing

```typescript
import { store } from '../core/store';
import { cardSlice, cardActions } from '../core/cards/cardSlice';

describe('Card Slice', () => {
  it('should handle card selection', () => {
    const initialState = store.getState().cards;
    
    store.dispatch(cardActions.selectCard({ 
      cardId: 'test-card-123',
      context: 'test'
    }));
    
    const newState = store.getState().cards;
    expect(newState.selectedCardId).toBe('test-card-123');
  });
  
  it('should handle card updates from events', () => {
    const testCard = { id: 'test-card', name: 'Test Card' };
    
    store.dispatch(cardActions.updateCard(testCard));
    
    const state = store.getState().cards;
    expect(state.cards).toContainEqual(testCard);
  });
});
```

### Event Integration Testing

```typescript
import { globalEventEmitter } from '../core/events/globalEventEmitter';
import { store } from '../core/store';

describe('Event-Redux Integration', () => {
  it('should update Redux state when events are emitted', (done) => {
    const testCard = { id: 'test-card', name: 'Updated Card' };
    
    // Listen for Redux state change
    const unsubscribe = store.subscribe(() => {
      const state = store.getState().cards;
      const updatedCard = state.cards.find(c => c.id === 'test-card');
      
      if (updatedCard && updatedCard.name === 'Updated Card') {
        unsubscribe();
        done();
      }
    });
    
    // Emit event
    globalEventEmitter.safeEmit('card.updated', testCard);
  });
});
```

## Deployment Configuration

### Production Optimizations

```typescript
// Store configuration for production
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false, // Disable in production for performance
      immutableCheck: false,    // Disable in production for performance
    }).concat(
      // Only essential middleware in production
      ...apiMiddlewares,
      eventBridgeMiddleware
    ),
  devTools: false, // Disabled in production
});

// Bundle size optimization
import { createApi } from '@reduxjs/toolkit/query/react';

// Lazy load API slices
export const cardApi = createApi({
  reducerPath: 'cardApi',
  // ... config
});

// Only import when needed
const loadCardApi = () => import('./cardApi');
```

### Security Considerations

```typescript
// Sensitive data exclusion from persistence
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['ui', 'cards'], // Only persist non-sensitive data
  blacklist: ['auth', 'sync'], // Never persist sensitive data
  transforms: [
    // Remove sensitive fields before persistence
    createTransform(
      (inboundState: any) => {
        const { tokens, ...safeState } = inboundState;
        return safeState;
      },
      (outboundState: any) => outboundState,
      { whitelist: ['auth'] }
    )
  ]
};

// Secure middleware configuration
const secureEventBridge: Middleware = (store) => (next) => (action) => {
  // Validate action before processing
  if (action.type.includes('auth') && !isValidAuthAction(action)) {
    console.warn('Invalid auth action blocked:', action.type);
    return;
  }
  
  return next(action);
};
```

This completes the Redux architecture implementation specification. The next documents will cover the implementation work plan and any additional considerations needed for the v7.0 architecture.