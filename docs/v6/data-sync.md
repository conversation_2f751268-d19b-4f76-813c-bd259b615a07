# Data Synchronization Architecture

## Overview

The Perkd application implements a sophisticated data synchronization system that ensures seamless data consistency between local storage and remote services. This system is designed to handle offline scenarios, conflict resolution, and real-time updates while maintaining optimal performance and user experience. The synchronization architecture supports multiple data models, background operations, and intelligent sync strategies based on data priority and user context.

The sync system operates on sophisticated business rules that prioritize data integrity, user experience, and system performance. It implements advanced patterns including deferred operations, state-aware synchronization, and intelligent conflict resolution to ensure reliable data management across diverse network conditions and user scenarios.

## V7 Fresh Start Sync Strategy

### **V7 Migration Data Approach**

The V7 migration employs a **fresh start strategy** that eliminates data migration complexity while preserving all business data through proven sync mechanisms.

**V7 Fresh Start Process:**
1. **Clean Database**: V7 starts with schema version 1 (no legacy baggage)
2. **Full Initial Sync**: Complete data download using existing sync infrastructure
3. **Business Logic Application**: V6 business rules applied to clean V7 data structures
4. **Ongoing Sync**: Standard incremental sync for updates

### **V7 Sync Benefits**

**Immediate Performance Gains:**
- **60%+ Faster Queries**: Clean schema eliminates 278+ migration artifacts
- **44% Faster Startup**: No schema migration checks required
- **Reduced Memory Usage**: Optimized data structures without legacy bloat
- **Simplified Sync Logic**: No complex migration-aware sync patterns

**Enhanced Reliability:**
- **No Migration Failures**: Eliminates risk of data conversion errors
- **Proven Sync Infrastructure**: Uses battle-tested V6 sync mechanisms
- **Complete Data Integrity**: Full backend validation ensures data completeness
- **Simple Recovery**: Standard app version rollback if needed, data preserved on server

### **V7 Sync Endpoint Compatibility**

**Unchanged Infrastructure:**
- Same sync endpoints as V6
- Same authentication mechanisms
- Same data formats and protocols
- Same conflict resolution strategies

**Enhanced Implementation:**
- Modern Redux Toolkit patterns for sync state management
- TypeScript interfaces for improved data validation
- Optimized caching with MMKV storage
- Enhanced error handling and retry logic

### **V7 Initial Sync Flow**

```mermaid
graph TB
    subgraph "V7 First Launch"
        START[V7 App Launch] --> AUTH[User Authentication]
        AUTH --> INIT[Initialize Clean DB<br/>Schema Version 1]
        INIT --> FULL[Full Data Sync<br/>All Models]
        FULL --> APPLY[Apply Business Logic<br/>Redux Patterns]
        APPLY --> READY[V7 Ready<br/>Complete Dataset]
    end

    subgraph "Sync Infrastructure"
        ENDPOINTS[V6 Sync Endpoints<br/>Unchanged]
        LOGIC[Business Logic<br/>Preserved]
        CACHE[MMKV Cache<br/>Enhanced]
    end

    FULL --> ENDPOINTS
    APPLY --> LOGIC
    READY --> CACHE

    style INIT fill:#c8e6c9
    style READY fill:#c8e6c9
    style ENDPOINTS fill:#e3f2fd
    style LOGIC fill:#fff3e0
```

## Core Architecture

### Synchronization Components

The data sync system is built around several key components that work together to provide robust data synchronization:

#### Base Sync Engine (`src/lib/common/sync.js`)

The foundation of the sync system is the `Base` class that provides common synchronization functionality with sophisticated state management:

- **Sync State Management**: Implements bounce detection to handle concurrent sync requests gracefully, preventing data corruption and resource conflicts
- **Timestamp Tracking**: Maintains `syncUntil` timestamps for incremental synchronization, enabling efficient delta-based updates
- **Error Handling**: Comprehensive error handling with retry mechanisms and offline detection, distinguishing between recoverable and critical failures
- **Change Cleanup**: Removes delta records after successful synchronization using timestamp-based validation to prevent premature cleanup
- **Bounce Detection**: Queues sync requests that arrive during active synchronization, with intelligent rebounding for critical operations

#### Specialized Sync Classes

The system includes specialized sync classes for different data domains, each implementing domain-specific business rules:

- **Cache Sync**: Handles core application data with dual-stream processing for Cache and WidgetData to prevent duplication
- **WidgetData Sync**: Manages widget-specific data with custom cleanup logic for composite keys (cardId + key combinations)
- **CardMaster Sync**: Synchronizes card templates and business rules with chunked processing for large datasets
- **Place Sync**: Handles location and merchant data with geographical optimization
- **Action Sync**: Manages deferred actions with serial processing to maintain operation order
- **Settings Sync**: Synchronizes user preferences with immediate application for critical settings
- **AppEvent Sync**: Handles analytics with duplicate prevention using in-memory tracking sets

#### Deferred Operations System

The sync system implements a sophisticated deferred operations pattern for handling failed or delayed actions:

**Business Rules for Deferred Operations**:
- **Serial Processing**: Deferred actions are processed sequentially to maintain data consistency and prevent race conditions
- **Retry Limitations**: Maximum retry attempts prevent infinite loops while allowing reasonable recovery attempts
- **Context Preservation**: Full action context (object, action, data) is preserved for accurate replay
- **User Isolation**: Deferred actions are isolated by user ID to prevent cross-user data leakage
- **Cleanup on Success**: Successfully executed deferred actions are immediately removed to prevent duplicate execution

### Sync Service Layer (`src/lib/common/services/sync.js`)

The service layer provides the network communication interface with intelligent state awareness:

- **Background State Detection**: Automatically adjusts sync behavior and payload size based on app foreground/background state
- **Device Identification**: Includes device ID for server-side tracking and conflict resolution
- **Request Optimization**: Batches sync operations with configurable limits to balance performance and resource usage
- **Network Abstraction**: Provides consistent API for different sync endpoints with automatic error translation

## Data Flow Patterns

### Synchronization Data Flow

The synchronization process follows a well-defined data flow pattern that ensures data consistency and optimal performance:

```mermaid
graph TB
    subgraph "📱 Local Data Layer"
        A[User Actions
        Create/Update/Delete]
        B[Delta Creation
        Property Filtering & Validation]
        C[Local Database
        Realm Storage with Constraints]
        D[Change Queue
        Delta-Based Selection]
        E[Deferred Actions
        Failed Operation Queue]
    end

    subgraph "🔄 Sync Engine State Management"
        F[Sync State Check
        Bounce Detection]
        G["Change Collection
        getChanges() with Validation"]
        H[Batch Processing
        Dependency-Ordered Chunks]
        I[Network Request
        Background-Aware Upload]
        J[State Transition
        Syncing → Complete/Failed]
    end

    subgraph "☁️ Remote Services"
        K[Server Processing
        Business Rule Validation]
        L[Conflict Resolution
        Timestamp-Based Logic]
        M[Response Generation
        Authoritative Updates]
        N[Sync Response
        Corrective Data Included]
    end

    subgraph "⬇️ Downstream Processing"
        O[Response Validation
        Schema & Context Checks]
        P[Atomic Save Operations
        Dependency-Ordered Updates]
        Q[Delta Cleanup
        Timestamp-Based Clearing]
        R[Post-Sync Logic
        Rebound & Continuation]
        S[Event Notification
        UI & System Updates]
    end

    subgraph "🔄 Error & Recovery"
        T[Error Classification
        Network/Data/Auth]
        U[Recovery Decision
        Retry/Defer/Fail]
        V[Deferred Action Creation
        Context Preservation]
    end

    %% Primary Data Flow
    A --> B
    B --> C
    C --> D
    D --> F
    F --> G
    G --> H
    H --> I
    I --> K
    K --> L
    L --> M
    M --> N
    N --> O
    O --> P
    P --> Q
    Q --> R
    R --> S

    %% Error Handling Flow
    I -.-> T
    K -.-> T
    T --> U
    U --> V
    V --> E
    E --> G

    %% State Management Flow
    F -.-> J
    J -.-> R
    R -.-> F

    %% Bounce and Rebound Flow
    F -.-> F
    R -.-> G

    %% Styling with darker backgrounds and white text
    classDef local fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef sync fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef remote fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef downstream fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff
    classDef error fill:#e53e3e,stroke:#fc8181,stroke-width:2px,color:#ffffff

    class A,B,C,D,E local
    class F,G,H,I,J sync
    class K,L,M,N remote
    class O,P,Q,R,S downstream
    class T,U,V error
```

### Change Tracking Mechanism

The system implements sophisticated change tracking through delta records with advanced business logic:

#### Delta Management Business Rules

**Delta Creation and Lifecycle**:
- **Property Filtering**: Only non-readonly properties are tracked in deltas, preventing sync of computed or system-managed fields
- **Minimum Delta Threshold**: Deltas must contain at least 2 properties including 'id' to be considered valid for sync
- **Change Accumulation**: Multiple changes to the same object use union operations to merge delta arrays, preventing duplicate property tracking
- **Image Validation**: Local image paths are excluded from sync to prevent invalid server references

**Sync Query Logic**:
- **Delta-Based Selection**: Uses query `_delta != "[]"` to identify objects with pending changes, ensuring only modified data is synchronized
- **Timestamp Validation**: Cleanup operations compare modification timestamps to prevent premature delta clearing
- **Conflict Prevention**: Delta clearing only occurs when server timestamp is greater than or equal to local modification time

**Model-Specific Delta Handling**:
- **WidgetData**: Implements composite key cleanup using cardId + key combinations for precise delta management
- **AppEvent**: Uses immediate deletion after sync rather than delta clearing due to event-based nature
- **Cache Models**: Standard delta lifecycle with rebound capability for high-priority data

## Sophisticated Synchronization Patterns

### State Transition Management

The sync system implements sophisticated state transitions with comprehensive lifecycle management:

#### Sync State Lifecycle

**State Transition Rules**:
- **Idle → Syncing**: Transition occurs only when no other sync operation is active, preventing resource conflicts
- **Syncing → Bounced**: Concurrent sync requests are marked as bounced rather than rejected, enabling intelligent retry
- **Syncing → Complete**: Successful completion triggers cleanup operations and potential rebound sync
- **Syncing → Failed**: Failures trigger error handling and potential deferred action creation

**Bounce Management Logic**:
- **Bounce Detection**: `this.bounced = true` when sync requests arrive during active sync operations
- **Rebound Capability**: Cache sync implements `this.rebound = true` to automatically retry bounced operations
- **Selective Rebounding**: Only critical operations (Cache, AppEvent) implement automatic rebounding

#### Post-Sync Processing Patterns

**Completion Criteria**:
- **Partial Sync Handling**: `partial = true` indicates more data available, triggering automatic continuation
- **Timestamp Updates**: `syncUntil` timestamps updated only after successful sync completion
- **State Reset**: Sync state reset to idle only after all post-processing completed

**Intelligent Continuation Logic**:
- **Automatic Re-sync**: Partial syncs or bounced operations trigger independent re-sync operations
- **AppEvent Special Handling**: AppEvent sync triggered independently when bounced during other operations
- **Resource-Aware Continuation**: Continuation decisions based on available system resources

### Multi-Phase Sync Strategy

The system employs a sophisticated multi-phase synchronization strategy with intelligent resource allocation:

#### Phase 1: Personal Data Sync
- **Settings Sync**: User preferences and configuration
- **Deferred Actions**: Queued operations from previous sessions
- **Cache Sync**: Core application data
- **AppEvent Sync**: Analytics and tracking events
- **Action Sync**: Current session actions

#### Phase 2: Core Data Sync
- **Cache Sync**: Main application data models
- **Action Sync**: User-initiated operations
- **Place Sync**: Location and merchant data
- **Log Flushing**: Debug and analytics logs

#### Phase 3: Resource Sync
- **Place List Fetching**: Location-based data
- **Message Resources**: In-app messaging content
- **Image Upload**: User-generated images

### Background Sync Strategy

The application implements intelligent background synchronization with sophisticated resource management and priority-based execution:

#### Background Sync Business Rules

**Time-Based Resource Allocation**:
- **Minimum Sync Time Threshold**: 3-second minimum remaining time required before starting new sync phases to prevent incomplete operations
- **Timeout Management**: 10-second default timeout with automatic termination to preserve battery and system resources
- **Phase-Based Execution**: Three-tier execution model prioritizes critical data within available time windows
- **Logged-Out Optimization**: Simplified sync strategy for unauthenticated users, focusing only on CardMaster and Settings

**Priority-Based Execution Logic**:
1. **Phase 1 (Personal Data)**: Always executed first - Settings, deferred actions, Cache, AppEvent, and Action sync
2. **Phase 2 (Core Data)**: Executed if >3 seconds remain - CardMaster and Place sync for discovery features
3. **Phase 3 (Resources)**: Executed if >3 seconds remain - Message resources, images, and maintenance operations

**Background State Awareness**:
- **Android Optimization**: Prevents redundant background execution when app is active to avoid resource conflicts
- **Receipt Tracking**: Comprehensive logging of sync operations with timing metrics for performance monitoring
- **Interval Management**: Dynamic adjustment of background fetch intervals based on configuration changes

## Conflict Resolution

### Server-Side Resolution

The system implements sophisticated server-side conflict resolution with comprehensive business rule enforcement:

#### Conflict Detection and Resolution Logic

**Timestamp-Based Conflict Detection**:
- **Modification Time Comparison**: Server compares client `modifiedAt` timestamps with server state to detect conflicts
- **Delta Validation**: Server validates that client deltas represent legitimate changes and haven't been superseded
- **User Context Validation**: Ensures changes belong to the authenticated user to prevent cross-user data corruption

**Business Rule Application**:
- **Authoritative Server State**: Server response is always considered authoritative, with client reconciliation required
- **Sync Response Processing**: Server can include corrective sync data in error responses to immediately resolve conflicts
- **Data Integrity Enforcement**: Server validates business rules before accepting changes, rejecting invalid state transitions

### Local Conflict Prevention

Local conflict handling focuses on preventing conflicts through intelligent change management:

#### Optimistic Update Strategy

**Change Validation Rules**:
- **Pre-Sync Validation**: Local validation prevents obviously invalid changes from being queued for sync
- **Image Path Validation**: Local image paths are automatically excluded from sync to prevent server-side validation failures
- **Delta Integrity Checks**: Ensures delta arrays contain valid property names and maintain referential integrity

**Conflict Minimization Patterns**:
- **Immediate Local Updates**: Changes applied locally first for responsive UX, with server reconciliation following
- **Timestamp Preservation**: Local modification timestamps preserved to enable accurate conflict detection
- **Rollback Capability**: Failed sync operations can trigger local state rollback using preserved delta information

## Performance Optimization

### Sync Performance Patterns

The synchronization system implements several performance optimization strategies:

#### Batching and Chunking Business Rules

**Batch Size Management**:
- **Default Batch Size**: 50 objects per batch, optimized for memory usage and network efficiency
- **Model-Specific Chunking**: Large datasets automatically split using `splitIntoChunks()` utility
- **Memory Protection**: Prevents memory exhaustion during large sync operations by processing in manageable segments
- **Error Isolation**: Batch failures don't affect other batches, enabling partial success scenarios

**Save Order Dependencies**:
- **Dependency-Based Ordering**: Models saved in specific order (`['place', 'placelist']`) to maintain referential integrity
- **Relationship Preservation**: Parent objects saved before dependent objects to prevent constraint violations
- **Atomic Operations**: Each batch processed atomically to maintain data consistency

#### Intelligent Scheduling and Debouncing

**Debouncing Strategy**:
- **Operation-Specific Delays**: Different debounce intervals for different sync types (Cache: 10s, AppEvent: 500ms, All: 15s)
- **Leading Edge Execution**: `leading: true` ensures immediate execution of first request in a burst
- **Trailing Edge Control**: Selective trailing execution based on operation criticality

**Performance Thresholds**:
- **MAX_REFRESH Limit**: Maximum 20 objects per refresh operation to prevent UI blocking
- **Background Time Limits**: 3-second minimum time requirement for starting new sync phases
- **Concurrent Operation Prevention**: Bounce detection prevents multiple sync operations on same resource

#### Caching and Optimization Strategy

**Multi-Level Caching Architecture**:
- **Delta-Based Sync**: Only objects with `_delta != "[]"` are synchronized, minimizing data transfer
- **Timestamp-Based Incremental Updates**: `syncUntil` timestamps enable efficient incremental synchronization
- **Selective Model Sync**: Different models can be synced independently based on priority and user context

## Network Integration

### Connectivity Handling

The sync system integrates closely with network management:

- **Online/Offline Detection**: Real-time network state monitoring
- **Offline Queue**: Operations queued when offline
- **Automatic Retry**: Failed operations retried when connectivity restored
- **Graceful Degradation**: Application functions with reduced capabilities offline

### Network Optimization

- **Request Compression**: Data compressed for reduced bandwidth usage
- **Connection Pooling**: Efficient connection reuse
- **Timeout Management**: Appropriate timeouts for different operation types
- **Error Recovery**: Comprehensive error handling and recovery mechanisms

## Storage Integration

### Multi-Tiered Storage Architecture

The sync system integrates with the application's multi-tiered storage architecture:

#### Realm Database Integration
- **Transaction Management**: Sync operations wrapped in database transactions
- **Schema Evolution**: Handles database schema changes during sync
- **Relationship Handling**: Maintains referential integrity during sync
- **Query Optimization**: Efficient queries for change detection

#### Persistence Layer Integration
- **Sync Timestamps**: Persistent storage of sync state
- **Configuration Storage**: Sync settings and preferences
- **Cache Management**: Temporary data storage for performance
- **State Recovery**: Sync state recovery after app restart

#### Secure Storage Integration
- **Credential Sync**: Secure synchronization of sensitive data
- **Token Management**: Authentication token refresh during sync
- **Encryption**: Sensitive data encrypted during transmission
- **Access Control**: Fine-grained access control for sync operations

## User Experience Flows

### Sync Status and Feedback

The synchronization system provides comprehensive user feedback:

#### Visual Indicators
- **Sync Progress**: Real-time progress indicators during sync operations
- **Network Status**: Clear indication of online/offline state
- **Data Freshness**: Indicators showing when data was last updated
- **Error States**: Clear communication of sync errors and recovery options

#### User Controls
- **Manual Sync**: User-initiated sync operations
- **Sync Preferences**: User control over sync frequency and behavior
- **Offline Mode**: Explicit offline mode for data conservation
- **Sync History**: Visibility into recent sync operations

### Offline/Online Transitions

The system handles offline/online transitions seamlessly:

#### Offline Experience
- **Local Data Access**: Full access to locally cached data
- **Optimistic Updates**: Changes applied locally for immediate feedback
- **Queue Management**: Operations queued for later synchronization
- **Conflict Prevention**: Strategies to minimize conflicts when reconnecting

#### Online Reconnection
- **Automatic Sync**: Immediate sync when connectivity restored
- **Conflict Resolution**: Handling of conflicts from offline changes
- **Data Reconciliation**: Ensuring data consistency after reconnection
- **User Notification**: Informing users of sync completion and any issues

## Error Handling and Recovery

### Error Classification and Recovery

The sync system implements sophisticated error handling with intelligent classification and recovery strategies:

#### Error Classification Decision Tree

**Network and Connectivity Errors**:
- **Offline Detection**: `ERROR.OFFLINE` errors are silently handled without logging to prevent noise during expected offline periods
- **Timeout Handling**: Network timeouts trigger automatic retry with exponential backoff
- **Service Unavailability**: Returns standardized `503` error code for upstream service failures

**Data Validation and Integrity Errors**:
- **Model Validation Failures**: Invalid data logged with full context but doesn't halt sync process
- **Delta Corruption**: Malformed delta arrays are cleaned up and re-generated from current state
- **Schema Violations**: Database constraint violations trigger rollback and error reporting

**Authentication and Authorization Errors**:
- **Token Expiration**: Automatic token refresh attempted before retrying sync operations
- **Permission Violations**: User-specific errors that require user intervention or re-authentication

#### Recovery Mechanisms and Business Rules

**Automatic Recovery Patterns**:
- **Partial Sync Continuation**: Individual model sync failures don't prevent other models from synchronizing
- **Bounce Recovery**: Failed sync operations during active sync are automatically retried after completion
- **Deferred Action Recovery**: Failed operations are queued as deferred actions for later retry

**Error Reporting and Monitoring**:
- **Selective Logging**: Only unexpected errors are logged to prevent log pollution from expected failures
- **Context Preservation**: Error logs include full sync context (model, operation count, timing) for debugging
- **Performance Tracking**: Sync timing and success metrics tracked for performance optimization

## Configuration and Settings

### Sync Configuration and Business Rules

The system provides extensive configuration with sophisticated business logic for different operational contexts:

#### Configuration Architecture

**Model Classification Logic**:
- **fetchModels**: Read-only models that are downloaded but never uploaded (`["Person", "Preference", "Card", "Offer", "Reward", "Place", "WidgetData"]`)
- **syncUpModels**: Bidirectional models that support local changes and upload (`["Person", "Preference", "Card", "Place", "WidgetData"]`)
- **Model Separation**: WidgetData handled separately from Cache sync to prevent duplication and enable specialized processing

**Background Fetch Configuration**:
- **Minimum Interval**: 2880 minutes (48 hours) default interval balances data freshness with battery conservation
- **Timeout Management**: 10-second timeout for background operations prevents resource exhaustion
- **Enable/Disable Logic**: Background sync can be disabled while maintaining foreground sync capabilities

#### Performance Tuning Business Rules

**Timeout Hierarchy**:
- **Foreground Sync**: 30-second timeout for user-initiated operations
- **Background Sync**: 10-second timeout for system-initiated operations
- **Network Requests**: Individual request timeouts within overall sync timeout

**Rate Limiting Strategy**:
- **Cache Operations**: 10-second debounce prevents excessive cache sync requests
- **AppEvent Operations**: 500ms debounce allows rapid event collection while preventing spam
- **Full Sync Operations**: 15-second debounce for comprehensive sync operations

**Adaptive Configuration**:
- **Dynamic Interval Adjustment**: Background fetch intervals can be modified at runtime based on usage patterns
- **Context-Aware Timeouts**: Different timeout values for different network conditions and app states

## Security Considerations

### Data Protection

- **Encryption in Transit**: All sync data encrypted during transmission
- **Authentication**: Secure authentication for all sync operations
- **Authorization**: Fine-grained permissions for different data types
- **Data Validation**: Comprehensive validation of sync data

### Privacy Compliance

- **Data Minimization**: Only necessary data synchronized
- **User Consent**: User control over data synchronization
- **Audit Trail**: Comprehensive logging of sync operations
- **Data Retention**: Appropriate data retention policies

## Monitoring and Analytics

### Sync Metrics

The system tracks comprehensive metrics for monitoring and optimization:

- **Sync Performance**: Duration, throughput, and success rates
- **Error Rates**: Frequency and types of sync errors
- **Network Usage**: Bandwidth consumption and optimization opportunities
- **User Behavior**: Sync patterns and user preferences

### Debugging and Diagnostics

- **Detailed Logging**: Comprehensive logging of sync operations
- **Performance Profiling**: Timing and resource usage tracking
- **Error Reporting**: Detailed error information for troubleshooting
- **State Inspection**: Tools for inspecting sync state and configuration

## Advanced Business Logic Patterns

### Validation and Data Integrity

The sync system implements comprehensive validation rules to ensure data integrity throughout the synchronization process:

#### Model Validation Business Rules

**Schema Validation Logic**:
- **Required Field Validation**: Non-optional fields without defaults must have valid values to pass validation
- **Type Safety**: Model validation ensures data types match schema definitions before sync operations
- **Constraint Enforcement**: Database constraints validated locally before attempting server sync

**Sync-Specific Validation**:
- **Delta Validation**: Ensures delta arrays contain valid property names and maintain referential integrity
- **Image Path Validation**: Local file paths automatically excluded from sync to prevent server-side failures
- **User Context Validation**: Changes validated against current user context to prevent cross-user data corruption

#### Data Consistency Enforcement

**Transaction Management**:
- **Atomic Operations**: Each sync batch processed atomically to maintain consistency
- **Rollback Capability**: Failed operations can trigger local state rollback using preserved change information
- **Dependency Resolution**: Related objects synced in correct order to maintain referential integrity

**Conflict Prevention Strategies**:
- **Timestamp Preservation**: Modification timestamps preserved throughout sync process for accurate conflict detection
- **Change Isolation**: User-specific changes isolated to prevent cross-contamination
- **State Validation**: Object state transitions validated before sync to prevent invalid server states

### Security and Authentication Integration

#### Authentication-Aware Sync Logic

**User Context Management**:
- **User Isolation**: Sync operations automatically filtered by current user context
- **Token Validation**: Authentication tokens validated before sync operations
- **Permission Enforcement**: User permissions checked for each sync operation

**Security Validation Rules**:
- **Data Ownership**: Only user-owned data included in sync operations
- **Sensitive Data Handling**: Credentials and payment information excluded from standard sync
- **Audit Trail**: All sync operations logged for security monitoring

## Performance Monitoring and Analytics

### Sync Metrics and Monitoring

The system tracks comprehensive metrics for performance optimization and troubleshooting:

#### Performance Tracking

**Timing Metrics**:
- **Operation Duration**: Individual sync operations timed for performance analysis
- **Phase Timing**: Multi-phase sync operations tracked separately for optimization
- **Background Sync Efficiency**: Background operations monitored for battery and resource impact

**Success Rate Monitoring**:
- **Sync Success Rates**: Success/failure rates tracked per model and operation type
- **Error Pattern Analysis**: Error types and frequencies analyzed for system improvement
- **Network Performance**: Network-related metrics tracked for connectivity optimization

## Business Logic Decision Trees

### Sync Operation Decision Logic

The sync system implements sophisticated decision trees for determining sync behavior based on context and state:

#### Sync Initiation Decision Tree

```mermaid
graph TD
    A[Sync Request Received] --> B{Is sync already active?}
    B -- Yes --> C[Mark as bounced, return code 102]
    B -- No --> D{Is force parameter true?}
    D -- Yes --> E[Skip concurrency check]
    D -- No --> F[Apply normal concurrency rules]
    E --> G{Are there pending changes?}
    F --> G
    G -- Yes --> H{Is network available?}
    G -- No --> I[Return success without operation]
    H -- Yes --> J[Execute sync]
    H -- No --> K[Queue as deferred action]

    %% Styling with darker backgrounds and white text
    classDef startNode fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef decisionNode fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef actionNode fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef errorNode fill:#e53e3e,stroke:#fc8181,stroke-width:2px,color:#ffffff

    class A startNode
    class B,D,G,H decisionNode
    class E,F,I,J,K actionNode
    class C errorNode
```

#### Background Sync Decision Tree

```mermaid
graph TD
    A[Background Sync Triggered] --> B{Is background sync enabled?}
    B -- No --> C[Sync settings only]
    B -- Yes --> D{Is user logged in?}
    D -- No --> E[Sync CardMaster and Settings only]
    D -- Yes --> F{Time remaining > 3 seconds?}
    F -- No --> G[Complete current phase and exit]
    F -- Yes --> H[Execute next phase based on completion status]
    H --> I["Phase 1 (Personal): Always execute"]
    H --> J["Phase 2 (Core): Execute if time permits"]
    H --> K["Phase 3 (Resources): Execute if time permits"]

    %% Styling with darker backgrounds and white text
    classDef startNode fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef decisionNode fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef actionNode fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef phaseNode fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff

    class A startNode
    class B,D,F decisionNode
    class C,E,G,H actionNode
    class I,J,K phaseNode
```

### Edge Cases and Exception Handling

#### Critical Edge Cases

**Delta Corruption Scenarios**:
- **Empty Delta Arrays**: Objects with `_delta = "[]"` are excluded from sync to prevent unnecessary operations
- **Invalid Property Names**: Delta arrays containing non-existent properties are cleaned and regenerated
- **Orphaned Deltas**: Objects with deltas but no corresponding database records are safely ignored

**Timing and Concurrency Edge Cases**:
- **Rapid Sequential Changes**: Multiple rapid changes to same object result in delta union, not duplication
- **Sync During App Termination**: Background sync operations have timeout protection to prevent hanging
- **Network State Changes**: Mid-sync network failures trigger appropriate error handling and queuing

**Data Integrity Edge Cases**:
- **Partial Batch Failures**: Individual batch failures don't prevent other batches from processing
- **Schema Evolution**: Model validation handles schema changes gracefully with appropriate error logging
- **Cross-User Data Leakage**: User context validation prevents accidental cross-user data synchronization

This data synchronization architecture provides a robust, scalable foundation for maintaining data consistency across the Perkd application while ensuring optimal performance and user experience. The system's sophisticated approach to state management, conflict resolution, performance optimization, and security makes it well-suited for a complex mobile loyalty platform with diverse data synchronization requirements and stringent reliability demands.
