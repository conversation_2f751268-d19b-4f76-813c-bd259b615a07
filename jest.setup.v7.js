/**
 * Perkd v7 Jest Setup
 * 
 * Test setup configuration for v7 data storage testing suite
 * Configures mocks and test environment for clean v7 architecture validation
 */

// =============================================================================
// GLOBAL TEST CONFIGURATION
// =============================================================================

// Increase test timeout for performance tests
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress logs during tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// =============================================================================
// REACT NATIVE MOCKS
// =============================================================================

// Mock React Native modules
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
    select: jest.fn((obj) => obj.ios),
  },
  Dimensions: {
    get: jest.fn(() => ({ width: 375, height: 812 })),
  },
  Alert: {
    alert: jest.fn(),
  },
  AsyncStorage: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  },
}));

// Mock React Native Reanimated
jest.mock('react-native-reanimated', () => ({
  runOnJS: jest.fn((fn) => fn),
  runOnUI: jest.fn((fn) => fn),
  useSharedValue: jest.fn(() => ({ value: 0 })),
  useAnimatedStyle: jest.fn(() => ({})),
  withTiming: jest.fn((value) => value),
  withSpring: jest.fn((value) => value),
}));

// =============================================================================
// REALM MOCKS
// =============================================================================

// Mock Realm for testing
const mockRealmObject = {
  id: 'mock-id',
  isValid: jest.fn(() => true),
  objectSchema: jest.fn(() => ({})),
  linkingObjects: jest.fn(() => []),
};

const mockRealmResults = {
  length: 0,
  filtered: jest.fn(() => mockRealmResults),
  sorted: jest.fn(() => mockRealmResults),
  slice: jest.fn(() => []),
  map: jest.fn(() => []),
  forEach: jest.fn(),
  addListener: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
  [Symbol.iterator]: function* () {
    yield* [];
  },
};

const mockRealm = {
  schemaVersion: 1,
  path: 'mock-test.realm',
  isEmpty: false,
  isInTransaction: false,
  isClosed: false,
  
  objects: jest.fn(() => mockRealmResults),
  objectForPrimaryKey: jest.fn(() => mockRealmObject),
  create: jest.fn(() => mockRealmObject),
  delete: jest.fn(),
  deleteAll: jest.fn(),
  
  write: jest.fn((callback) => {
    if (callback) callback();
  }),
  
  beginTransaction: jest.fn(),
  commitTransaction: jest.fn(),
  cancelTransaction: jest.fn(),
  
  addListener: jest.fn(),
  removeListener: jest.fn(),
  removeAllListeners: jest.fn(),
  
  close: jest.fn(),
  
  // Mock schema property
  schema: [
    { name: 'Person', primaryKey: 'id', properties: {} },
    { name: 'Card', primaryKey: 'id', properties: {} },
    { name: 'Offer', primaryKey: 'id', properties: {} },
  ],
};

// Mock Realm constructor
jest.mock('realm', () => {
  const MockRealm = jest.fn(() => mockRealm);
  
  // Static methods
  MockRealm.open = jest.fn(() => Promise.resolve(mockRealm));
  MockRealm.deleteFile = jest.fn();
  MockRealm.exists = jest.fn(() => true);
  MockRealm.copyBundledRealmFiles = jest.fn();
  
  // Schema version constants
  MockRealm.schemaVersion = jest.fn(() => 1);
  
  return MockRealm;
});

// =============================================================================
// REDUX TOOLKIT MOCKS
// =============================================================================

// Mock Redux Toolkit Query
jest.mock('@reduxjs/toolkit/query', () => ({
  createApi: jest.fn(() => ({
    reducerPath: 'api',
    reducer: jest.fn(),
    middleware: jest.fn(),
  })),
  fetchBaseQuery: jest.fn(),
  setupListeners: jest.fn(),
}));

// =============================================================================
// PERFORMANCE TESTING UTILITIES
// =============================================================================

// Mock performance.now for consistent testing
const mockPerformanceNow = jest.fn(() => Date.now());
global.performance = {
  now: mockPerformanceNow,
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByName: jest.fn(() => []),
  getEntriesByType: jest.fn(() => []),
  clearMarks: jest.fn(),
  clearMeasures: jest.fn(),
};

// Performance testing helpers
global.testPerformance = {
  // Simulate v6 performance baseline
  simulateV6Query: (baselineMs = 180) => {
    const start = performance.now();
    // Simulate processing time
    const end = start + baselineMs;
    mockPerformanceNow.mockReturnValueOnce(start).mockReturnValueOnce(end);
    return { start, end, duration: baselineMs };
  },
  
  // Simulate v7 performance improvement
  simulateV7Query: (targetMs = 70) => {
    const start = performance.now();
    const end = start + targetMs;
    mockPerformanceNow.mockReturnValueOnce(start).mockReturnValueOnce(end);
    return { start, end, duration: targetMs };
  },
  
  // Calculate performance improvement
  calculateImprovement: (v6Time, v7Time) => {
    return ((v6Time - v7Time) / v6Time * 100);
  },
};

// =============================================================================
// TEST DATA FACTORIES
// =============================================================================

// Factory for creating test v7 person data
global.createTestPerson = (overrides = {}) => ({
  id: 'test-person-1',
  familyName: 'Doe',
  givenName: 'John',
  fullName: 'John Doe',
  gender: 'male',
  contacts: {
    id: 'test-contacts-1',
    emails: [],
    phones: [],
    addresses: [],
  },
  preferences: {
    id: 'test-preferences-1',
    notifications: {
      id: 'test-notifications-1',
      pushEnabled: true,
      emailEnabled: true,
      smsEnabled: false,
      offerNotifications: true,
      rewardNotifications: true,
      securityNotifications: true,
    },
    privacy: { id: 'test-privacy-1' },
    display: { id: 'test-display-1' },
    language: 'en',
    currency: 'USD',
    timezone: 'UTC',
  },
  audit: {
    id: 'test-audit-1',
    createdAt: new Date(),
    modifiedAt: new Date(),
    version: 1,
    createdBy: 'test-user',
    modifiedBy: 'test-user',
  },
  ...overrides,
});

// Factory for creating test v7 card data
global.createTestCard = (overrides = {}) => ({
  id: 'test-card-1',
  masterId: 'test-master-1',
  personId: 'test-person-1',
  credentials: {
    id: 'test-credentials-1',
    number: '1234567890',
    barcode: 'TEST123',
    barcodeFormat: 'CODE128',
    pin: 'encrypted-pin',
  },
  display: {
    id: 'test-display-1',
    name: 'Test Card',
    customImage: 'https://example.com/image.png',
    backgroundColor: '#FF0000',
    textColor: '#FFFFFF',
    theme: 'auto',
  },
  lifecycle: {
    id: 'test-lifecycle-1',
    issuedAt: new Date(),
    activatedAt: new Date(),
    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
    state: 'active',
  },
  features: {
    id: 'test-features-1',
    hasStoredValue: true,
    hasLoyaltyProgram: true,
    hasOffers: true,
    isShareable: false,
    supportsNFC: true,
    supportsBarcodeScanning: true,
    hasCustomization: true,
  },
  permissions: {
    id: 'test-permissions-1',
    canShare: true,
    canModify: true,
    canDelete: true,
    shareLevel: 'none',
    accessLevel: 'owner',
  },
  audit: {
    id: 'test-audit-1',
    createdAt: new Date(),
    modifiedAt: new Date(),
    version: 1,
    createdBy: 'test-person-1',
    modifiedBy: 'test-person-1',
  },
  ...overrides,
});

// Factory for creating test v6 data with baggage
global.createTestV6Card = (overrides = {}) => ({
  id: 'v6-card-1',
  masterId: 'v6-master-1',
  personId: 'v6-person-1',
  number: '1234567890',
  barcode: 'V6TEST',
  barcodeType: 'code128',
  displayName: 'V6 Test Card',
  state: 'active',
  createdAt: new Date(),
  startTime: new Date(),
  endTime: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
  
  // v6 JSON string anti-patterns
  forms: JSON.stringify({ loyalty: true, rewards: true }),
  custom: JSON.stringify({ nfc: true, offers: true }),
  flow: JSON.stringify({ activation: 'automatic' }),
  pricing: JSON.stringify({ currency: 'USD' }),
  options: JSON.stringify({ sharable: false }),
  
  // v6 baggage fields
  _delta: JSON.stringify(['id', 'masterId', 'personId']),
  _refresh: false,
  _upload: true,
  _category: 'retail',
  _initials: 'TC',
  _model: 'Card',
  
  ...overrides,
});

// =============================================================================
// TEST VALIDATION HELPERS
// =============================================================================

// Helper to validate v7 schema compliance
global.validateV7Schema = (object, schemaType) => {
  const validations = {
    person: (obj) => {
      expect(obj).toHaveProperty('id');
      expect(obj).toHaveProperty('contacts');
      expect(obj).toHaveProperty('preferences');
      expect(obj).toHaveProperty('audit');
      expect(obj).not.toHaveProperty('_delta');
      expect(obj).not.toHaveProperty('_refresh');
      expect(obj).not.toHaveProperty('_model');
    },
    card: (obj) => {
      expect(obj).toHaveProperty('id');
      expect(obj).toHaveProperty('credentials');
      expect(obj).toHaveProperty('display');
      expect(obj).toHaveProperty('lifecycle');
      expect(obj).toHaveProperty('features');
      expect(obj).toHaveProperty('permissions');
      expect(obj).toHaveProperty('audit');
      expect(obj).not.toHaveProperty('forms');
      expect(obj).not.toHaveProperty('custom');
      expect(obj).not.toHaveProperty('_delta');
      expect(obj).not.toHaveProperty('_refresh');
      expect(obj).not.toHaveProperty('_upload');
    },
  };
  
  if (validations[schemaType]) {
    validations[schemaType](object);
  }
};

// Helper to validate performance improvements
global.validatePerformanceImprovement = (v6Time, v7Time, targetImprovement = 60) => {
  const improvement = ((v6Time - v7Time) / v6Time * 100);
  expect(improvement).toBeGreaterThan(targetImprovement);
  return improvement;
};

// =============================================================================
// CLEANUP
// =============================================================================

// Global test cleanup
afterEach(() => {
  jest.clearAllMocks();
  mockPerformanceNow.mockClear();
});

// Log test environment setup
console.log('✅ Perkd v7 test environment configured');
console.log('🧪 Clean v7 architecture testing enabled');
console.log('📊 Performance benchmarking utilities loaded');
console.log('🗑️ v6 baggage elimination validation ready');
