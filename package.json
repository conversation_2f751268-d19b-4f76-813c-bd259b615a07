{"name": "Perkd", "version": "0.0.1", "private": true, "engines": {"node": ">=22"}, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "pod": "cd ios && (rm -rf Pods/ || true) && (rm Podfile.lock || true) && pod update", "reinstall": "(rm -rf node_modules/ || true) && (rm yarn.lock || true) && yarn cache clean && yarn install && (cd ios && yarn pod)"}, "dependencies": {"@craftzdog/react-native-buffer": "^6.1.0", "@gorhom/bottom-sheet": "^5.1.6", "@notifee/react-native": "^9.1.8", "@pagopa/io-react-native-jwt": "^2.1.0", "@quidone/react-native-wheel-picker": "^1.5.2", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/image-editor": "^4.3.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/messaging": "^22.4.0", "@react-native-picker/picker": "^2.11.1", "@react-native/new-app-screen": "^0.80.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/native-stack": "^7.3.21", "@reduxjs/toolkit": "^2.8.2", "ajv": "^8.17.1", "axios": "^1.10.0", "i18n-js": "^4.5.1", "libphonenumber-js": "^1.12.10", "mustache": "^4.2.0", "react": "19.1.0", "react-native": "0.80.1", "react-native-background-fetch": "^4.2.8", "react-native-biometrics": "^3.0.1", "react-native-contacts": "^8.0.5", "react-native-date-picker": "patch:react-native-date-picker@npm%3A5.0.13#~/.yarn/patches/react-native-date-picker-npm-5.0.13-e35e950566.patch", "react-native-device-info": "^14.0.4", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.27.1", "react-native-haptic-feedback": "^2.3.3", "react-native-image-picker": "^8.2.1", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-keychain": "^10.0.0", "react-native-linear-gradient": "^2.8.3", "react-native-localize": "^3.5.1", "react-native-mmkv": "^3.3.0", "react-native-modal": "^14.0.0-rc.1", "react-native-nfc-manager": "^3.16.2", "react-native-pager-view": "^6.8.1", "react-native-passkey": "^3.1.0", "react-native-permissions": "^5.4.1", "react-native-quick-base64": "^2.2.0", "react-native-quick-crypto": "^0.7.15", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-share": "^12.1.0", "react-native-svg": "^15.12.0", "react-native-ui-datepicker": "^3.1.2", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.7.1", "react-native-webview": "^13.15.0", "react-native-worklets-core": "^1.6.0", "react-redux": "^9.2.0", "readable-stream": "^4.7.0", "realm": "^20.1.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "rfdc": "^1.4.1", "sift": "^17.1.3", "supercluster": "^8.0.1", "validator": "^13.15.15"}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/runtime": "^7.27.6", "@bugsnag/react-native": "^8.4.0", "@react-native-community/cli": "19.1.0", "@react-native-community/cli-platform-android": "19.1.0", "@react-native-community/cli-platform-ios": "19.1.0", "@react-native/babel-preset": "0.80.1", "@react-native/eslint-config": "0.80.1", "@react-native/metro-config": "0.80.1", "@react-native/typescript-config": "0.80.1", "@types/events": "^3.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "@types/react-native": "^0.73.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.1.0", "@types/readable-stream": "^4.0.21", "@types/validator": "^13.15.2", "eslint": "^9.31.0", "jest": "^30.0.4", "prettier": "3.6.2", "react-native-network-logger": "^2.0.1", "react-test-renderer": "19.1.0", "typescript": "5.8.3", "typescript-eslint": "^8.37.0"}, "packageManager": "yarn@4.9.2"}