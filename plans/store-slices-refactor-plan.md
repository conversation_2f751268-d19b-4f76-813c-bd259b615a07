# Store Slices Refactor Plan

## Executive Summary

This plan addresses the critical architectural issues identified in the Redux store slices. The current implementation suffers from mixed paradigms, legacy dependencies, and poor separation of concerns. This refactor will establish clean, maintainable, and testable Redux patterns.

## Current State Assessment

### 🔴 Critical Issues
- **CardSlice**: 489 lines of mixed responsibilities
- **OfferSlice**: 931 lines with legacy dependencies
- **Type System**: Multiple conflicting interfaces
- **Testing**: Heavy mocking, global dependencies

### 🟢 Success Pattern
- **AppSlice**: Clean, focused, proper TypeScript

## Refactor Strategy

### Phase 1: Foundation (Week 1-2)
**Goal**: Establish clean patterns and type system

#### 1.1 Type System Consolidation
- [ ] Create unified type definitions in `src/core/store/types/`
- [ ] Remove duplicate Card/Offer interfaces
- [ ] Eliminate all `any` types
- [ ] Establish consistent naming conventions

#### 1.2 Service Layer Integration
- [ ] Complete CardService implementation
- [ ] Create OfferService (clean version)
- [ ] Remove all legacy global dependencies
- [ ] Implement proper error handling

#### 1.3 Slice Architecture Standards
- [ ] Define slice responsibility boundaries
- [ ] Create slice template based on appSlice
- [ ] Establish state structure patterns
- [ ] Document async thunk patterns

### Phase 2: CardSlice Refactor (Week 3-4)
**Goal**: Transform cardSlice into multiple focused slices

#### 2.1 Split Responsibilities (Simplified)
```
cardSlice.ts (489 lines) →
├── cardDataSlice.ts      (Normalized collection with entity adapter)
├── cardUISlice.ts        (UI state: selections, filters, loading)
└── cardSelectors.ts      (All derived/computed values)
```

#### 2.2 CardDataSlice Implementation
- [ ] **Use createEntityAdapter for normalized state**
- [ ] **Realm-Redux synchronization**
- [ ] **Optimistic updates**
- [ ] Proper error handling
- [ ] Type-safe state management

#### 2.3 CardUISlice Implementation
- [ ] **Selection state only**
- [ ] **Filters and sorting**
- [ ] **Temporary loading states**
- [ ] **No computed values** (use selectors instead)

#### 2.4 CardSelectors Implementation
- [ ] **All metrics as computed selectors**
- [ ] **Memoized filtering and sorting**
- [ ] **Derived state calculations**
- [ ] **Performance-optimized selectors**

### Phase 3: OfferSlice Refactor (Week 5-6)
**Goal**: Clean architecture without legacy dependencies

#### 3.1 Legacy Dependency Removal
- [ ] Remove `require('../../../lib/common/services/offer')`
- [ ] Eliminate global object access
- [ ] Create clean OfferService
- [ ] Implement proper event system

#### 3.2 Split Responsibilities (Simplified)
```
offerSlice.ts (931 lines) →
├── offerDataSlice.ts     (Offers + redemptions with entity adapters)
├── offerUISlice.ts       (Selection, filters, UI state)
└── offerSelectors.ts     (All computed values and metrics)
```

#### 3.3 Clean Implementation
- [ ] **Use createEntityAdapter for offers collection**
- [ ] **Use createEntityAdapter for redemptions tracking**
- [ ] Service-based async thunks
- [ ] Proper TypeScript throughout
- [ ] Testable architecture
- [ ] Performance optimizations

### Phase 4: PersonSlice Completion (Week 7)
**Goal**: Complete implementation with real data layer

#### 4.1 Implementation
- [ ] Real async thunk implementations
- [ ] Data layer integration
- [ ] Proper error handling
- [ ] Type safety

### Phase 5: Testing Overhaul (Week 8)
**Goal**: Comprehensive, realistic test coverage

#### 5.1 Test Strategy
- [ ] Remove excessive mocking
- [ ] Test real behavior
- [ ] Integration test coverage
- [ ] Performance testing

## Implementation Details

### New File Structure (Simplified)
```
src/core/store/
├── types/
│   ├── index.ts          (Re-exports)
│   ├── card.ts           (Card types)
│   ├── offer.ts          (Offer types)
│   ├── person.ts         (Person types)
│   └── common.ts         (Shared types)
├── slices/
│   ├── app/
│   │   └── appSlice.ts
│   ├── card/
│   │   ├── cardDataSlice.ts
│   │   ├── cardUISlice.ts
│   │   ├── cardSelectors.ts
│   │   └── index.ts
│   ├── offer/
│   │   ├── offerDataSlice.ts
│   │   ├── offerUISlice.ts
│   │   ├── offerSelectors.ts
│   │   └── index.ts
│   ├── person/
│   │   └── personSlice.ts
│   └── profile/
│       └── profileSlice.ts
└── middleware/
    ├── realmBridge.ts
    └── index.ts
```

### Slice Template Pattern
```typescript
// Based on appSlice.ts success pattern
export interface XSliceState {
  // Simple, focused state
  data: X[];
  loading: boolean;
  error: string | null;
}

const initialState: XSliceState = {
  data: [],
  loading: false,
  error: null,
};

export const xSlice = createSlice({
  name: 'x',
  initialState,
  reducers: {
    // Simple, focused reducers
  },
  extraReducers: (builder) => {
    // Clean async thunk handling
  },
});
```

### Service Integration Pattern
```typescript
// Clean service integration
export const createCard = createAsyncThunk(
  'cards/create',
  async (data: CreateCardData, { rejectWithValue }) => {
    try {
      return await CardService.createCard(data);
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : 'Failed to create card'
      );
    }
  }
);
```

## 📊 Opportunity Priority Matrix

### 🔴 **Critical (Must Fix)**
1. **Entity Adapters for Collections** - Cards, Offers, Redemptions, Events
2. **Remove Manual CRUD Logic** - Replace with adapter methods
3. **Eliminate Legacy Global Dependencies** - `global._`, `global.$`

### 🟡 **High Impact (Should Fix)**
4. **Realm-Redux Bridge** - Automatic sync from Realm to Redux
5. **Memoized Selectors** - Replace direct state access
6. **Normalized State Structure** - Remove nested data

### 🟢 **Medium Impact (Nice to Have)**
7. **Optimistic Updates** - Immediate UI feedback with Realm
8. **Computed Selectors** - Remove stored computed values
9. **Sync Queue Management** - Background sync with conflict resolution

### 🔵 **Low Impact (Future Enhancement)**
10. **Advanced Realm Queries** - Complex filtering and sorting

## Updated Success Metrics

### Code Quality
- [ ] Reduce cardSlice from 489 to **2 focused slices + selectors**
- [ ] Reduce offerSlice from 931 to **2 focused slices + selectors**
- [ ] **Replace 100% of manual CRUD with entity adapters**
- [ ] **All metrics as computed selectors** (no stored computed state)
- [ ] **Eliminate all manual array operations**
- [ ] Eliminate all `any` types
- [ ] Remove all legacy global dependencies

### Performance
- [ ] **Improve bundle size by 25%** (entity adapters + Realm optimization)
- [ ] **Reduce selector execution time by 80%** (memoization)
- [ ] **Eliminate unnecessary re-renders** (normalized state)
- [ ] **O(1) entity lookups** (entity adapter structure)
- [ ] **Instant UI updates** (optimistic updates with Realm)

### Architecture
- [ ] **100% normalized state structure**
- [ ] **Zero manual CRUD operations**
- [ ] **Realm-first data flow**
- [ ] **All computed values as selectors**
- [ ] **Automatic Realm-Redux synchronization**
- [ ] 100% TypeScript coverage
- [ ] Clear separation of concerns
- [ ] Testable architecture

### Developer Experience
- [ ] **Auto-generated selectors** (entity adapters)
- [ ] **Auto-generated CRUD operations** (entity adapters)
- [ ] **Auto-managed sync states** (Realm bridge)
- [ ] **Optimistic updates** (immediate feedback)
- [ ] **Offline-first architecture**
- [ ] Comprehensive documentation

## Risk Mitigation

### Breaking Changes
- [ ] Maintain backward compatibility during transition
- [ ] Gradual migration strategy
- [ ] Feature flags for new implementations
- [ ] Rollback procedures

### Timeline Risks
- [ ] Parallel development tracks
- [ ] Incremental delivery
- [ ] Regular checkpoint reviews
- [ ] Scope adjustment protocols

## Next Steps

1. **Immediate**: Create type consolidation branch
2. **Week 1**: Begin Phase 1 implementation
3. **Weekly**: Progress reviews and adjustments
4. **Week 8**: Final testing and deployment

## Resources Required

- **Development**: 2 developers, 8 weeks
- **Testing**: QA support for integration testing
- **Review**: Architecture review at each phase
- **Documentation**: Technical writing support

## 🔍 Additional Optimization Opportunities

### 🎯 **Entity Adapter Opportunities**

#### 1. **OfferDataSlice** - Same Issue as CardDataSlice
```typescript
// ❌ CURRENT PROBLEM in plan:
offers: OfferData[];  // Manual array management

// ✅ SOLUTION:
const offersAdapter = createEntityAdapter<Offer>();
```

#### 2. **Redemptions Tracking** - Perfect Entity Adapter Use Case
```typescript
// ❌ CURRENT PROBLEM in existing offerSlice:
redemptions: {
  inProgress: Array<{ offerId: string; cardId: string; timestamp: string; }>;
  completed: Array<{ offerId: string; cardId: string; redeemedAt: string; }>;
  failed: Array<{ offerId: string; error: string; timestamp: string; }>;
};

// ✅ SOLUTION: Separate entity adapters for each
const inProgressRedemptionsAdapter = createEntityAdapter<InProgressRedemption>();
const completedRedemptionsAdapter = createEntityAdapter<CompletedRedemption>();
const failedRedemptionsAdapter = createEntityAdapter<FailedRedemption>();
```

#### 3. **Event Queue Management** - Another Entity Adapter Win
```typescript
// ❌ CURRENT PROBLEM:
events: {
  eventQueue: Array<{ type: string; offerId: string; timestamp: string; processed: boolean; }>;
}

// ✅ SOLUTION:
const eventsAdapter = createEntityAdapter<BusinessEvent>({
  sortComparer: (a, b) => a.timestamp.localeCompare(b.timestamp)
});
```

### � **Realm-Redux Bridge Opportunities**

#### 4. **Sync Service Integration**
```typescript
// ❌ CURRENT PROBLEM: Direct API calls in async thunks
export const loadCards = createAsyncThunk('cardData/load', async (options) => {
  return await CardService.getCards(options); // Direct API call
});

// ✅ SOLUTION: Realm-first with sync service
export const SyncService = {
  async createCard(data: CreateCardData): Promise<void> {
    // 1. Optimistic update to Realm
    const tempCard = await RealmService.createCard(data);

    // 2. Queue for background sync
    await SyncQueue.add('CREATE_CARD', { tempCard, data });

    // 3. Realm listener updates Redux automatically
  }
};
```

#### 5. **Realm Change Listeners**
```typescript
// ❌ CURRENT PROBLEM: Manual state synchronization
lastSync: string | null;
syncInProgress: boolean;

// ✅ SOLUTION: Automatic Realm-to-Redux sync
RealmService.addListener('Card', (cards, changes) => {
  store.dispatch(cardDataSlice.actions.syncFromRealm(cards));
});
```

### 🎨 **Selector Optimization Opportunities**

#### 6. **Filtered Selectors Should Use createSelector**
```typescript
// ❌ CURRENT PROBLEM in plan:
export const selectFilteredOffers = (state: { offers: OfferState }) => {
  const { offers, filters } = state.offers;
  let filtered = offers; // This runs on every render!
  // ... filtering logic
};

// ✅ SOLUTION: Memoized selector with entity adapter
export const selectFilteredOffers = createSelector(
  [offersAdapter.getSelectors().selectAll, selectOfferFilters],
  (offers, filters) => {
    // Only recalculates when offers or filters change
    return offers.filter(/* filtering logic */);
  }
);
```

### 🏗️ **State Structure Opportunities**

#### 7. **Normalized References Instead of Nested Data**
```typescript
// ❌ CURRENT PROBLEM: Nested data structures
interface Card {
  id: string;
  offers: Offer[];  // Denormalized - causes update issues
}

// ✅ SOLUTION: Normalized references
interface Card {
  id: string;
  offerIds: string[];  // Reference to offers by ID
}
```

#### 8. **Computed State Should Be Selectors**
```typescript
// ❌ CURRENT PROBLEM: Storing computed values in state
metrics: {
  totalCards: number;     // Computed from cards.length
  activeCards: number;    // Computed from filtering
}

// ✅ SOLUTION: Computed selectors
export const selectTotalCards = createSelector(
  [selectAllCards],
  (cards) => cards.length
);

export const selectActiveCards = createSelector(
  [selectAllCards],
  (cards) => cards.filter(card => card.lifecycle.state === 'active').length
);
```

### 🔧 **Middleware Opportunities**

#### 9. **Realm-Redux Bridge Middleware**
```typescript
// ❌ CURRENT PROBLEM: Manual Realm-Redux synchronization
export const acceptCard = createAsyncThunk('cards/accept', async (params) => {
  // Direct service call without Realm integration
  const result = await CardService.acceptCard(params.cardId);
  return result;
});

// ✅ SOLUTION: Realm-first with bridge middleware
export const realmBridgeMiddleware = createListenerMiddleware();

realmBridgeMiddleware.startListening({
  actionCreator: acceptCardOptimistic,
  effect: async (action, listenerApi) => {
    // 1. Update Realm immediately
    await RealmService.acceptCard(action.payload.cardId);

    // 2. Queue for sync
    await SyncQueue.add('ACCEPT_CARD', action.payload);

    // 3. Realm listener will update Redux automatically
  },
});
```

### 📊 **Performance Opportunities**

#### 10. **Optimistic Updates with Realm**
```typescript
// ❌ CURRENT PROBLEM: Waiting for server responses
export const createCard = createAsyncThunk('cards/create', async (data) => {
  const result = await CardService.createCard(data); // Wait for server
  return result;
});

// ✅ SOLUTION: Optimistic updates with Realm
export const cardDataSlice = createSlice({
  name: 'cardData',
  initialState: cardsAdapter.getInitialState({
    pendingOperations: [],
  }),
  reducers: {
    createCardOptimistic: (state, action: PayloadAction<CreateCardData>) => {
      const tempCard = { ...action.payload, id: generateTempId(), isPending: true };
      cardsAdapter.addOne(state, tempCard);
      state.pendingOperations.push({ type: 'CREATE', tempId: tempCard.id });
    },
    confirmCardCreation: (state, action: PayloadAction<{ tempId: string; realCard: Card }>) => {
      cardsAdapter.removeOne(state, action.payload.tempId);
      cardsAdapter.addOne(state, action.payload.realCard);
      state.pendingOperations = state.pendingOperations.filter(op => op.tempId !== action.payload.tempId);
    },
  },
});
```

## Detailed Implementation Guide

### Phase 1 Detailed Tasks

#### 1.1 Type System Consolidation (Using Actual Realm Models)

**Update `src/core/store/types/card.ts`**
```typescript
// ✅ CORRECTED: Import actual Realm models
import { Card as RealmCard, CardOptions, CardFormData } from '../../../data/models/Card';
import { StoredValue } from '../../../data/models/embedded/StoredValue';

// ✅ Use actual Realm interface as base
export type Card = RealmCard;

// ✅ Create UI-friendly derived types from Realm fields
export interface CardDisplayInfo {
  id: string;
  name: string;                    // Mapped from displayName
  number?: string;                 // Direct from Realm
  barcode?: string;                // Direct from Realm
  barcodeType?: string;            // Direct from Realm
  state: string;                   // Direct from Realm
  isVisible: boolean;              // Mapped from visible
  hasStoredValue: boolean;         // Derived from storedValue existence
}

export interface CardCredentials {
  number?: string;                 // From card.number
  barcode?: string;                // From card.barcode
  barcodeFormat?: string;          // From card.barcodeType
}

export interface CardTimestamps {
  createdAt: Date;                 // Direct from Realm
  modifiedAt?: Date;               // Direct from Realm
  deletedAt?: Date;                // Direct from Realm
  startTime?: Date;                // Direct from Realm
  endTime?: Date;                  // Direct from Realm
  activeTime?: Date;               // Direct from Realm
}

// ✅ Handle Realm.Mixed types properly
export interface CardOptionsTyped extends CardOptions {
  overrideMaster?: any;
  notification?: any;
  noDelete?: boolean;
  hideZeroBalance?: boolean;
  hideBalance?: boolean;
}

export interface CardFormDataTyped extends CardFormData {
  [key: string]: any;
}

// ✅ Create selectors for complex Realm relationships
export interface CardRelationships {
  storedValue?: StoredValue;       // Embedded object
  sharer?: any;                    // Relationship object
  when?: any;                      // Relationship object
  cardImage?: any;                 // Relationship object
  image?: any;                     // CustomImage relationship
  sharings?: any[];                // Sharing[] relationship
}
```

**Update `src/core/store/types/offer.ts`**
```typescript
// ✅ CORRECTED: Import actual Realm models
import { Offer as RealmOffer, OfferOptions, OfferPlace, OfferFields } from '../../../data/models/Offer';

// ✅ Use actual Realm interface as base
export type Offer = RealmOffer;

// ✅ Create UI-friendly derived types from Realm fields
export interface OfferDisplayInfo {
  id: string;
  masterId: string;
  name?: string;                   // Direct from Realm
  shortName?: string;              // Direct from Realm
  title?: string;                  // Direct from Realm
  description?: string;            // Direct from Realm
  terms?: string;                  // Direct from Realm
  kind?: string;                   // Direct from Realm (discount, voucher, ticket)
  state?: string;                  // Direct from Realm
  isVisible: boolean;              // Mapped from visible
  hasBarcode: boolean;             // Derived from barcode existence
}

export interface OfferTiming {
  startTime?: Date;                // Direct from Realm
  endTime?: Date;                  // Direct from Realm
  activeTime?: Date;               // Direct from Realm
  purgeTime?: Date;                // Direct from Realm
  createdAt: Date;                 // Direct from Realm
  modifiedAt?: Date;               // Direct from Realm
  deletedAt?: Date;                // Direct from Realm
}

export interface OfferCredentials {
  barcode?: string;                // Direct from Realm
  barcodeType?: string;            // Direct from Realm
  orderId?: string;                // Direct from Realm
}

// ✅ Handle Realm.Mixed types properly
export interface OfferOptionsTyped extends OfferOptions {
  appOnly?: boolean;
  redeemOnline?: boolean;
  payment?: any;
  hideButton?: boolean;
  buttonLabel?: string;
  buttonLink?: string;
  showVenueName?: boolean;
}

export interface OfferPlaceTyped extends OfferPlace {
  [key: string]: any;
}

export interface OfferFieldsTyped extends OfferFields {
  [key: string]: any;
}

// ✅ Create selectors for complex Realm relationships
export interface OfferRelationships {
  code?: any;                      // OfferCode relationship
  images?: any[];                  // OfferImage[] relationship
  discount?: any;                  // OfferDiscount relationship
  redemption?: any;                // Redemption relationship
  sharer?: any;                    // OfferSharer relationship
  globalize?: any;                 // OfferGlobalize relationship
  when?: any;                      // OfferWhen relationship
  applet?: any;                    // AppletWebview relationship
}
```

#### 1.2 Service Layer Standards

**Create `src/core/services/BaseService.ts`**
```typescript
export abstract class BaseService {
  protected handleError(error: unknown): never {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('An unknown error occurred');
  }

  protected validateId(id: string, entityName: string): void {
    if (!id || typeof id !== 'string') {
      throw new Error(`Invalid ${entityName} ID: ${id}`);
    }
  }
}

export interface ServiceResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> extends ServiceResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
```

### Phase 2 Detailed Implementation

#### 2.1 CardDataSlice Structure

**File: `src/core/store/slices/card/cardDataSlice.ts`**
```typescript
import { createEntityAdapter, createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { Card } from '../../../../data/models/Card';
import { RootState } from '../../types';

// ✅ CORRECTED: Use actual Realm Card model with entity adapter
const cardsAdapter = createEntityAdapter<Card>({
  // Use Realm's primary key
  selectId: (card) => card.id,
  // Sort by creation date (newest first)
  sortComparer: (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
});

export interface CardDataState {
  loading: boolean;
  error: string | null;
  lastSync: string | null;
  realmConnected: boolean;
}

// ✅ CORRECTED: Initialize with adapter's initial state
const initialState = cardsAdapter.getInitialState<CardDataState>({
  loading: false,
  error: null,
  lastSync: null,
  realmConnected: false,
});

// ✅ UPDATED: Realm-first operations
export const createCardOptimistic = createAsyncThunk(
  'cardData/createOptimistic',
  async (data: CreateCardData) => {
    // 1. Immediate Realm update
    const tempCard = await RealmService.createCard(data);

    // 2. Queue for sync
    await SyncQueue.add('CREATE_CARD', { tempCard, data });

    return tempCard;
  }
);

export const acceptCardOptimistic = createAsyncThunk(
  'cardData/acceptOptimistic',
  async (cardId: string) => {
    // 1. Immediate Realm update
    await RealmService.acceptCard(cardId);

    // 2. Queue for sync
    await SyncQueue.add('ACCEPT_CARD', { cardId });

    return cardId;
  }
);

// ✅ CORRECTED: Simplified slice with entity adapter
export const cardDataSlice = createSlice({
  name: 'cardData',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },

    // ✅ REALM INTEGRATION: Sync from Realm to Redux
    syncFromRealm: (state, action: PayloadAction<Card[]>) => {
      cardsAdapter.setAll(state, action.payload);
      state.lastSync = new Date().toISOString();
      state.realmConnected = true;
    },

    // ✅ REALM INTEGRATION: Handle individual Realm changes
    addCardFromRealm: (state, action: PayloadAction<Card>) => {
      cardsAdapter.addOne(state, action.payload);
    },

    updateCardFromRealm: (state, action: PayloadAction<Card>) => {
      cardsAdapter.updateOne(state, {
        id: action.payload.id,
        changes: action.payload,
      });
    },

    removeCardFromRealm: (state, action: PayloadAction<string>) => {
      cardsAdapter.removeOne(state, action.payload);
    },

    setRealmConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.realmConnected = action.payload;
    },
  },
  extraReducers: (builder) => {
    // ✅ UPDATED: Optimistic operations that update Realm first
    builder
      .addCase(createCardOptimistic.fulfilled, (state, action) => {
        // Realm listener will handle the actual Redux update
        // This is just for loading state management
        state.loading = false;
      })
      .addCase(acceptCardOptimistic.fulfilled, (state, action) => {
        // Realm listener will handle the actual Redux update
        state.loading = false;
      })
      .addCase(createCardOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createCardOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create card';
      });
  },
});

// ✅ FREE SELECTORS: No manual implementation needed
export const {
  selectAll: selectAllCards,
  selectById: selectCardById,
  selectIds: selectCardIds,
  selectEntities: selectCardEntities,
  selectTotal: selectTotalCards,
} = cardsAdapter.getSelectors((state: RootState) => state.cardData);

// ✅ BONUS: Additional computed selectors
export const selectActiveCards = createSelector(
  [selectAllCards],
  (cards) => cards.filter(card => card.lifecycle.state === 'active')
);

export const selectCardsByMaster = (masterId: string) => createSelector(
  [selectAllCards],
  (cards) => cards.filter(card => card.masterId === masterId)
);
```

#### 2.2 CardUISlice Structure (Simplified)

**File: `src/core/store/slices/card/cardUISlice.ts`**
```typescript
export interface CardUIState {
  selectedCardId: string | null;
  filters: {
    status?: CardLifecycle['state'];
    masterId?: string;
    searchTerm?: string;
  };
  sortBy: 'name' | 'created' | 'lastUsed';
  sortOrder: 'asc' | 'desc';
}

const initialState: CardUIState = {
  selectedCardId: null,
  filters: {},
  sortBy: 'created',
  sortOrder: 'desc',
};

export const cardUISlice = createSlice({
  name: 'cardUI',
  initialState,
  reducers: {
    selectCard: (state, action: PayloadAction<string | null>) => {
      state.selectedCardId = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<CardUIState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setSorting: (state, action: PayloadAction<{
      sortBy: CardUIState['sortBy'];
      sortOrder: CardUIState['sortOrder'];
    }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
  },
});
```

#### 2.3 CardSelectors Implementation

**File: `src/core/store/slices/card/cardSelectors.ts`**
```typescript
import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../types';
import { Card } from '../../../../data/models/Card';
import { CardDisplayInfo, CardCredentials, CardTimestamps } from '../../types/card';

// ✅ CORRECTED: Import the actual adapter from cardDataSlice
import { cardsAdapter } from './cardDataSlice';

// Base selectors from entity adapter (using actual Realm Card model)
export const cardDataSelectors = cardsAdapter.getSelectors(
  (state: RootState) => state.cardData
);

// UI selectors
export const selectCardUI = (state: RootState) => state.cardUI;
export const selectSelectedCardId = (state: RootState) => state.cardUI.selectedCardId;
export const selectCardFilters = (state: RootState) => state.cardUI.filters;

// ✅ CORRECTED: Computed selectors using actual Realm fields
export const selectCardMetrics = createSelector(
  [cardDataSelectors.selectAll],
  (cards) => ({
    totalCards: cards.length,
    activeCards: cards.filter(c => c.state === 'active').length,
    expiredCards: cards.filter(c => c.endTime && c.endTime < new Date()).length,
    visibleCards: cards.filter(c => c.visible !== false).length,
    cardsWithStoredValue: cards.filter(c => c.storedValue).length,
  })
);

// ✅ CORRECTED: UI-friendly card display info
export const selectCardDisplayInfo = createSelector(
  [cardDataSelectors.selectAll],
  (cards): CardDisplayInfo[] => cards.map(card => ({
    id: card.id,
    name: card.displayName || 'Unnamed Card',
    number: card.number,
    barcode: card.barcode,
    barcodeType: card.barcodeType,
    state: card.state,
    isVisible: card.visible ?? true,
    hasStoredValue: !!card.storedValue,
  }))
);

// ✅ CORRECTED: Filtered cards using actual Realm fields
export const selectFilteredCards = createSelector(
  [cardDataSelectors.selectAll, selectCardFilters, selectCardUI],
  (cards, filters, ui) => {
    let filtered = cards;

    // Apply filters using actual Realm fields
    if (filters.status) {
      filtered = filtered.filter(card => card.state === filters.status);
    }
    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(card =>
        (card.displayName || '').toLowerCase().includes(term) ||
        (card.number || '').toLowerCase().includes(term)
      );
    }
    if (filters.masterId) {
      filtered = filtered.filter(card => card.masterId === filters.masterId);
    }

    // Apply sorting using actual Realm fields
    return filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (ui.sortBy) {
        case 'name':
          aValue = a.displayName || '';
          bValue = b.displayName || '';
          break;
        case 'created':
          aValue = a.createdAt;
          bValue = b.createdAt;
          break;
        case 'lastUsed':
          aValue = a.modifiedAt || a.createdAt;
          bValue = b.modifiedAt || b.createdAt;
          break;
        default:
          return 0;
      }

      const order = ui.sortOrder === 'asc' ? 1 : -1;
      return aValue > bValue ? order : -order;
    });
  }
);

// ✅ CORRECTED: Selected card with proper typing
export const selectSelectedCard = createSelector(
  [cardDataSelectors.selectAll, selectSelectedCardId],
  (cards, selectedId): Card | null =>
    selectedId ? cards.find(card => card.id === selectedId) || null : null
);

// ✅ NEW: Selectors for Realm.Mixed fields
export const selectCardOptions = (cardId: string) => createSelector(
  [cardDataSelectors.selectById],
  (card) => card ? card.options as any : null
);

export const selectCardFormData = (cardId: string) => createSelector(
  [cardDataSelectors.selectById],
  (card) => card ? card.formData as any : null
);

// ✅ NEW: Selectors for embedded objects
export const selectCardStoredValue = (cardId: string) => createSelector(
  [cardDataSelectors.selectById],
  (card) => card?.storedValue || null
);
```

## Migration Strategy

### Backward Compatibility Approach

#### Phase-by-Phase Migration
1. **Phase 1**: New types alongside old types
2. **Phase 2**: New card slices alongside old cardSlice
3. **Phase 3**: New offer slices alongside old offerSlice
4. **Phase 4**: Component migration to new slices
5. **Phase 5**: Remove old implementations

#### Feature Flags
```typescript
// src/core/store/config.ts
export const STORE_CONFIG = {
  useNewCardSlices: process.env.NODE_ENV === 'development',
  useNewOfferSlices: false,
  enableMetrics: true,
  enableEventBridge: false,
};
```

#### Gradual Component Migration
```typescript
// Example: Migrate components one by one
import { STORE_CONFIG } from '../store/config';
import { useSelector } from 'react-redux';

// Old import
import { selectCards as selectCardsOld } from '../store/slices/cardSlice';
// New import
import { selectCards as selectCardsNew } from '../store/slices/card/selectors';

export const CardList = () => {
  const selectCards = STORE_CONFIG.useNewCardSlices ? selectCardsNew : selectCardsOld;
  const cards = useSelector(selectCards);

  // Component logic remains the same
};
```

### Testing Strategy

#### Unit Testing Pattern
```typescript
// src/core/store/slices/card/__tests__/cardDataSlice.test.ts
import { configureStore } from '@reduxjs/toolkit';
import { cardDataSlice, loadCards, createCard } from '../cardDataSlice';
import { CardService } from '../../../services/CardService';

// Mock service instead of global objects
jest.mock('../../../services/CardService');
const mockCardService = CardService as jest.Mocked<typeof CardService>;

describe('CardDataSlice', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        cardData: cardDataSlice.reducer,
      },
    });
    jest.clearAllMocks();
  });

  describe('loadCards', () => {
    it('should load cards successfully', async () => {
      const mockCards = [
        { id: 'card-1', masterId: 'master-1', personId: 'person-1' },
        { id: 'card-2', masterId: 'master-2', personId: 'person-1' },
      ];

      mockCardService.getCards.mockResolvedValue(mockCards);

      await store.dispatch(loadCards({ personId: 'person-1' }));

      const state = store.getState().cardData;
      expect(state.loading).toBe(false);
      expect(state.cards).toEqual(mockCards);
      expect(state.error).toBe(null);
      expect(state.lastSync).toBeDefined();
    });

    it('should handle load errors', async () => {
      const errorMessage = 'Network error';
      mockCardService.getCards.mockRejectedValue(new Error(errorMessage));

      await store.dispatch(loadCards());

      const state = store.getState().cardData;
      expect(state.loading).toBe(false);
      expect(state.cards).toEqual([]);
      expect(state.error).toBe(errorMessage);
    });
  });

  describe('createCard', () => {
    it('should create card and add to state', async () => {
      const newCard = { id: 'new-card', masterId: 'master-1', personId: 'person-1' };
      const createData = { masterId: 'master-1', personId: 'person-1' };

      mockCardService.createCard.mockResolvedValue(newCard);

      await store.dispatch(createCard(createData));

      const state = store.getState().cardData;
      expect(state.cards).toContainEqual(newCard);
    });
  });
});
```

#### Integration Testing
```typescript
// src/core/store/__tests__/integration/cardFlow.test.ts
import { configureStore } from '@reduxjs/toolkit';
import { cardDataSlice } from '../../slices/card/cardDataSlice';
import { cardUISlice } from '../../slices/card/cardUISlice';
import { cardMetricsSlice } from '../../slices/card/cardMetricsSlice';

describe('Card Flow Integration', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        cardData: cardDataSlice.reducer,
        cardUI: cardUISlice.reducer,
        cardMetrics: cardMetricsSlice.reducer,
      },
    });
  });

  it('should handle complete card creation flow', async () => {
    // 1. Create card
    await store.dispatch(createCard({ masterId: 'master-1', personId: 'person-1' }));

    // 2. Select the new card
    const cards = store.getState().cardData.cards;
    const newCard = cards[0];
    store.dispatch(selectCard(newCard.id));

    // 3. Verify metrics updated
    const metrics = store.getState().cardMetrics;
    expect(metrics.totalCards).toBe(1);

    // 4. Verify UI state
    const ui = store.getState().cardUI;
    expect(ui.selectedCardId).toBe(newCard.id);
  });
});
```

### Performance Optimization

#### Selector Memoization
```typescript
// src/core/store/selectors/cardSelectors.ts
import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../types';

// Basic selectors
export const selectCardData = (state: RootState) => state.cardData;
export const selectCardUI = (state: RootState) => state.cardUI;

// Memoized selectors
export const selectCards = createSelector(
  [selectCardData],
  (cardData) => cardData.cards
);

export const selectFilteredCards = createSelector(
  [selectCards, selectCardUI],
  (cards, ui) => {
    let filtered = cards;

    if (ui.filters.status) {
      filtered = filtered.filter(card => card.lifecycle.state === ui.filters.status);
    }

    if (ui.filters.searchTerm) {
      const term = ui.filters.searchTerm.toLowerCase();
      filtered = filtered.filter(card =>
        card.display.name.toLowerCase().includes(term)
      );
    }

    // Apply sorting
    return filtered.sort((a, b) => {
      const aValue = a[ui.sortBy];
      const bValue = b[ui.sortBy];
      const order = ui.sortOrder === 'asc' ? 1 : -1;
      return aValue > bValue ? order : -order;
    });
  }
);

export const selectSelectedCard = createSelector(
  [selectCards, selectCardUI],
  (cards, ui) => cards.find(card => card.id === ui.selectedCardId) || null
);

export const selectCardById = (cardId: string) => createSelector(
  [selectCards],
  (cards) => cards.find(card => card.id === cardId) || null
);
```

#### Bundle Size Optimization
```typescript
// src/core/store/slices/card/index.ts
// Re-export only what's needed
export { cardDataSlice } from './cardDataSlice';
export { cardUISlice } from './cardUISlice';
export { cardMetricsSlice } from './cardMetricsSlice';

// Export specific actions to avoid importing entire slices
export {
  loadCards,
  createCard,
  updateCard,
  deleteCard,
} from './cardDataSlice';

export {
  selectCard,
  setFilters,
  clearFilters,
} from './cardUISlice';
```

## Quality Assurance

### Code Review Checklist
- [ ] No `any` types used
- [ ] All async thunks have proper error handling
- [ ] State updates are immutable
- [ ] Selectors are memoized where appropriate
- [ ] No direct service calls in components
- [ ] Proper TypeScript interfaces
- [ ] Test coverage > 90%
- [ ] No legacy global dependencies

### Performance Benchmarks
- [ ] Bundle size reduction: Target 20% smaller
- [ ] Render performance: <16ms per update
- [ ] Memory usage: No memory leaks
- [ ] Selector performance: <1ms execution time

### Documentation Requirements
- [ ] API documentation for all slices
- [ ] Migration guide for developers
- [ ] Architecture decision records
- [ ] Performance optimization guide

## 🎯 **Corrected Implementation Examples**

### **OfferDataSlice with Entity Adapter**
```typescript
import { createEntityAdapter, createSlice } from '@reduxjs/toolkit';

const offersAdapter = createEntityAdapter<Offer>({
  sortComparer: (a, b) => b.temporal.startsAt.getTime() - a.temporal.startsAt.getTime()
});

const redemptionsAdapter = createEntityAdapter<Redemption>({
  sortComparer: (a, b) => b.timestamp.localeCompare(a.timestamp)
});

export interface OfferDataState {
  loading: boolean;
  error: string | null;
  lastSync: string | null;
}

const initialState = offersAdapter.getInitialState(
  redemptionsAdapter.getInitialState<OfferDataState>({
    loading: false,
    error: null,
    lastSync: null,
  }, 'redemptions')
);

export const offerDataSlice = createSlice({
  name: 'offerData',
  initialState,
  reducers: {
    clearError: (state) => { state.error = null; },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loadOffers.fulfilled, (state, action) => {
        offersAdapter.setAll(state, action.payload);
      })
      .addCase(redeemOffer.fulfilled, (state, action) => {
        redemptionsAdapter.addOne(state.redemptions, action.payload);
        offersAdapter.updateOne(state, {
          id: action.payload.offerId,
          changes: { temporal: { ...offer.temporal, activationCount: offer.temporal.activationCount + 1 } }
        });
      });
  },
});

// Free selectors for both entities
export const offerSelectors = offersAdapter.getSelectors((state: RootState) => state.offerData);
export const redemptionSelectors = redemptionsAdapter.getSelectors((state: RootState) => state.offerData.redemptions);
```

### **Realm-Redux Bridge Service (Updated for Actual Models)**
```typescript
import { Card } from '../../../data/models/Card';
import { Offer } from '../../../data/models/Offer';
import { Person } from '../../../data/models/Person';
import { cardDataSlice } from '../slices/card/cardDataSlice';
import { offerDataSlice } from '../slices/offer/offerDataSlice';

export class RealmReduxBridge {
  private static instance: RealmReduxBridge;
  private store: any;
  private realmListeners: Map<string, any> = new Map();

  static initialize(store: any) {
    this.instance = new RealmReduxBridge(store);
    return this.instance;
  }

  constructor(store: any) {
    this.store = store;
    this.setupRealmListeners();
  }

  private setupRealmListeners() {
    // ✅ CORRECTED: Listen to actual Realm model changes

    // Card changes - using actual Card model
    const cardListener = (cards: Realm.Results<Card>, changes: Realm.CollectionChangeSet) => {
      // ✅ CORRECTED: Use Realm's toJSON() for efficient conversion
      const plainCards = Array.from(cards).map(card => card.toJSON());

      if (changes.insertions.length > 0) {
        changes.insertions.forEach(index => {
          this.store.dispatch(cardDataSlice.actions.addCardFromRealm(plainCards[index]));
        });
      }

      if (changes.modifications.length > 0) {
        changes.modifications.forEach(index => {
          this.store.dispatch(cardDataSlice.actions.updateCardFromRealm(plainCards[index]));
        });
      }

      if (changes.deletions.length > 0) {
        // Handle deletions - we need to track IDs before deletion
        changes.deletions.forEach(index => {
          // This requires storing previous state or using soft deletes
          console.warn('Card deletion detected, implement soft delete tracking');
        });
      }
    };

    this.realmListeners.set('Card', cardListener);

    // Offer changes - using actual Offer model
    const offerListener = (offers: Realm.Results<Offer>, changes: Realm.CollectionChangeSet) => {
      // ✅ CORRECTED: Use Realm's toJSON() for efficient conversion
      const plainOffers = Array.from(offers).map(offer => offer.toJSON());

      if (changes.insertions.length > 0) {
        changes.insertions.forEach(index => {
          this.store.dispatch(offerDataSlice.actions.addOfferFromRealm(plainOffers[index]));
        });
      }

      if (changes.modifications.length > 0) {
        changes.modifications.forEach(index => {
          this.store.dispatch(offerDataSlice.actions.updateOfferFromRealm(plainOffers[index]));
        });
      }
    };

    this.realmListeners.set('Offer', offerListener);
  }

  // ✅ CORRECTED: Use Realm's optimized toJSON() method
  private realmToPlain<T extends Realm.Object>(realmObject: T): any {
    // Realm's toJSON() is optimized for this exact use case
    // Handles Realm.Mixed fields, embedded objects, and relationships properly
    return realmObject.toJSON();
  }

  // ✅ UPDATED: Sync operations using actual Realm models
  static async processSync() {
    const pendingOperations = await SyncQueue.getPending();

    for (const operation of pendingOperations) {
      try {
        await this.syncOperation(operation);
        await SyncQueue.markComplete(operation.id);
      } catch (error) {
        await SyncQueue.markFailed(operation.id, error);
        console.error('Sync operation failed:', operation, error);
      }
    }
  }

  private static async syncOperation(operation: SyncOperation) {
    switch (operation.type) {
      case 'CREATE_CARD':
        // Use actual Realm Card model
        return await CardService.createCard(operation.data);
      case 'UPDATE_CARD':
        return await CardService.updateCard(operation.data.id, operation.data.changes);
      case 'ACCEPT_CARD':
        return await CardService.acceptCard(operation.data.cardId);
      case 'CREATE_OFFER':
        return await OfferService.createOffer(operation.data);
      case 'REDEEM_OFFER':
        return await OfferService.redeemOffer(operation.data);
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  }

  // ✅ NEW: Handle Realm.Mixed field updates
  static updateMixedField(modelName: string, id: string, fieldName: string, value: any) {
    // Special handling for Realm.Mixed fields like options, formData, etc.
    const realm = DatabaseManager.getInstance();

    realm.write(() => {
      const object = realm.objectForPrimaryKey(modelName, id);
      if (object) {
        (object as any)[fieldName] = value;
      }
    });
  }

  // ✅ NEW: Cleanup method
  destroy() {
    this.realmListeners.clear();
  }
}

// ✅ NEW: Type definitions for sync operations
export interface SyncOperation {
  id: string;
  type: 'CREATE_CARD' | 'UPDATE_CARD' | 'ACCEPT_CARD' | 'CREATE_OFFER' | 'REDEEM_OFFER';
  data: any;
  timestamp: string;
  retryCount: number;
}
```

### **Memoized Selector Examples**
```typescript
// Complex filtering with memoization
export const selectFilteredOffers = createSelector(
  [offerSelectors.selectAll, selectOfferFilters, selectCurrentTime],
  (offers, filters, currentTime) => {
    return offers.filter(offer => {
      // Active filter
      if (!filters.showExpired && offer.temporal.endsAt < currentTime) return false;

      // Category filter
      if (filters.category && offer.content.category !== filters.category) return false;

      // Card filter
      if (filters.cardId && offer.cardId !== filters.cardId) return false;

      return true;
    });
  }
);

// Computed metrics as selectors
export const selectOfferMetrics = createSelector(
  [offerSelectors.selectAll, redemptionSelectors.selectAll],
  (offers, redemptions) => ({
    totalOffers: offers.length,
    activeOffers: offers.filter(o => o.temporal.isActive).length,
    expiredOffers: offers.filter(o => o.temporal.endsAt < new Date()).length,
    totalRedemptions: redemptions.length,
    redemptionRate: redemptions.length / offers.length,
  })
);
```

## 🔧 **Critical Realm Integration Patterns**

### **Handling Realm.Mixed Fields**
```typescript
// ✅ PATTERN: Type-safe access to Realm.Mixed fields
export const selectCardOptions = (cardId: string) => createSelector(
  [cardDataSelectors.selectById],
  (card): CardOptionsTyped | null => {
    if (!card?.options) return null;

    // Realm.Mixed fields need type assertion
    return card.options as CardOptionsTyped;
  }
);

// ✅ PATTERN: Updating Realm.Mixed fields
export const updateCardOptions = createAsyncThunk(
  'cards/updateOptions',
  async ({ cardId, options }: { cardId: string; options: Partial<CardOptionsTyped> }) => {
    const realm = DatabaseManager.getInstance();

    return realm.write(() => {
      const card = realm.objectForPrimaryKey('Card', cardId);
      if (card) {
        // Merge with existing options
        const currentOptions = (card as any).options || {};
        (card as any).options = { ...currentOptions, ...options };
      }
      return cardId;
    });
  }
);
```

### **Handling Embedded Objects**
```typescript
// ✅ PATTERN: Working with embedded objects like StoredValue
export const selectCardStoredValue = (cardId: string) => createSelector(
  [cardDataSelectors.selectById],
  (card): StoredValue | null => {
    // Embedded objects are directly accessible
    return card?.storedValue || null;
  }
);

export const updateStoredValue = createAsyncThunk(
  'cards/updateStoredValue',
  async ({ cardId, storedValue }: { cardId: string; storedValue: Partial<StoredValue> }) => {
    const realm = DatabaseManager.getInstance();

    return realm.write(() => {
      const card = realm.objectForPrimaryKey('Card', cardId);
      if (card && (card as any).storedValue) {
        // Update embedded object properties
        Object.assign((card as any).storedValue, storedValue);
      }
      return cardId;
    });
  }
);
```

### **Handling Relationship Objects**
```typescript
// ✅ PATTERN: Working with relationship objects (stored in injectId)
export const selectCardSharer = (cardId: string) => createSelector(
  [cardDataSelectors.selectById],
  (card): any => {
    // Relationship objects need special handling
    return card?.sharer || null;
  }
);

// ✅ PATTERN: Lazy loading relationship data
export const loadCardRelationships = createAsyncThunk(
  'cards/loadRelationships',
  async (cardId: string) => {
    const realm = DatabaseManager.getInstance();
    const card = realm.objectForPrimaryKey('Card', cardId);

    if (!card) throw new Error('Card not found');

    // Load related data that might not be in Redux
    return {
      cardId,
      sharer: (card as any).sharer,
      when: (card as any).when,
      cardImage: (card as any).cardImage,
      sharings: Array.from((card as any).sharings || []),
    };
  }
);
```

### **Data Conversion Utilities**
```typescript
// ✅ CORRECTED: Use Realm's toJSON() for efficient conversion
export const realmToRedux = {
  card: (realmCard: Card): Card => {
    // Use Realm's optimized toJSON() method
    // This properly handles Realm.Mixed fields, embedded objects, and dates
    return realmCard.toJSON();
  },

  offer: (realmOffer: Offer): Offer => {
    // Use Realm's optimized toJSON() method
    return realmOffer.toJSON();
  },

  person: (realmPerson: Person): Person => {
    // Use Realm's optimized toJSON() method
    return realmPerson.toJSON();
  },
};
```

## 🏆 **Final Assessment**

The corrected plan now includes **10 major optimization opportunities** for a Realm-first architecture:

1. **Entity Adapters** for all collections using actual Realm models
2. **Realm-Redux Bridge** with proper type conversion
3. **Memoized Selectors** for computed values and Realm.Mixed fields
4. **Normalized State** structure with Realm primary keys
5. **Zero Manual CRUD** operations (entity adapter + Realm)
6. **Optimistic Updates** with Realm-first approach
7. **Proper Realm.Mixed handling** with type safety
8. **Embedded object support** for complex data structures
9. **Relationship object patterns** for linked data
10. **Offline-first** architecture with sync queue

**Impact**: This creates a **world-class offline-first Redux implementation** that properly integrates with Realm's actual data models, handles all Realm-specific features, and maintains type safety throughout the application.

---

*This comprehensive plan now provides the correct path from architectural chaos to a clean, performant, and maintainable Redux implementation using all of Redux Toolkit's powerful features.*
