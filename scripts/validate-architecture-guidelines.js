#!/usr/bin/env node

/**
 * Architecture Guidelines Validation Script
 * 
 * Validates that our codebase follows the clean architecture guidelines
 * defined in .perkdrules to prevent regression to version-proliferation patterns
 */

const fs = require('fs');
const path = require('path');

console.log('🏗️ Validating Architecture Guidelines Compliance');
console.log('='.repeat(60));

let violations = [];
let checks = 0;

// =============================================================================
// VALIDATION FUNCTIONS
// =============================================================================

function checkVersionNumbersInPaths(dir = 'src') {
  const items = fs.readdirSync(dir, { withFileTypes: true });
  
  for (const item of items) {
    const fullPath = path.join(dir, item.name);
    
    if (item.isDirectory()) {
      // Check for version numbers in directory names
      if (/v\d+/.test(item.name) && !fullPath.includes('migration')) {
        violations.push({
          type: 'VERSION_IN_PATH',
          file: fullPath,
          message: 'Directory name contains version number'
        });
      }
      
      // Recursively check subdirectories
      if (!item.name.startsWith('.') && !item.name.includes('node_modules')) {
        checkVersionNumbersInPaths(fullPath);
      }
    }
  }
  checks++;
}

function checkSuperfluousNaming(dir = 'src') {
  const superfluousWords = ['Optimized', 'Enhanced', 'New', 'Improved', 'Advanced'];
  
  function checkDirectory(currentDir) {
    const items = fs.readdirSync(currentDir, { withFileTypes: true });
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item.name);
      
      if (item.isFile() && item.name.endsWith('.ts')) {
        // Check filename for superfluous words
        for (const word of superfluousWords) {
          if (item.name.includes(word) && !fullPath.includes('migration')) {
            violations.push({
              type: 'SUPERFLUOUS_NAMING',
              file: fullPath,
              message: `Filename contains superfluous word: ${word}`
            });
          }
        }
        
        // Check file content for superfluous class names
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          for (const word of superfluousWords) {
            const regex = new RegExp(`class\\s+\\w*${word}\\w*`, 'g');
            if (regex.test(content) && !fullPath.includes('migration')) {
              violations.push({
                type: 'SUPERFLUOUS_CLASS_NAME',
                file: fullPath,
                message: `Class name contains superfluous word: ${word}`
              });
            }
          }
        } catch (error) {
          // Skip files that can't be read
        }
      } else if (item.isDirectory() && !item.name.startsWith('.') && !item.name.includes('node_modules')) {
        checkDirectory(fullPath);
      }
    }
  }
  
  checkDirectory(dir);
  checks++;
}

function checkVersionedImports(dir = 'src') {
  function checkDirectory(currentDir) {
    const items = fs.readdirSync(currentDir, { withFileTypes: true });
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item.name);
      
      if (item.isFile() && (item.name.endsWith('.ts') || item.name.endsWith('.tsx'))) {
        try {
          const content = fs.readFileSync(fullPath, 'utf8');
          
          // Check for versioned imports
          if (content.includes('/v7/') || content.includes('/v8/') || content.includes('/v9/')) {
            if (!fullPath.includes('migration') && !fullPath.includes('test')) {
              violations.push({
                type: 'VERSIONED_IMPORT',
                file: fullPath,
                message: 'Contains versioned import paths'
              });
            }
          }
          
          // Check for versioned export names
          const versionedExports = content.match(/V\d+_[A-Z_]+/g);
          if (versionedExports && !fullPath.includes('migration')) {
            violations.push({
              type: 'VERSIONED_EXPORT',
              file: fullPath,
              message: `Contains versioned exports: ${versionedExports.join(', ')}`
            });
          }
        } catch (error) {
          // Skip files that can't be read
        }
      } else if (item.isDirectory() && !item.name.startsWith('.') && !item.name.includes('node_modules')) {
        checkDirectory(fullPath);
      }
    }
  }
  
  checkDirectory(dir);
  checks++;
}

function checkConsistentNaming() {
  const expectedPatterns = {
    'src/data/database/RealmConfig.ts': ['DatabaseManager', 'REALM_CONFIG'],
    'src/data/schemas/index.ts': ['CLEAN_SCHEMAS', 'CURRENT_SCHEMA_VERSION'],
    'src/data/services/DataService.ts': ['DataService'],
  };
  
  for (const [filePath, expectedNames] of Object.entries(expectedPatterns)) {
    if (fs.existsSync(filePath)) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        for (const expectedName of expectedNames) {
          if (!content.includes(expectedName)) {
            violations.push({
              type: 'INCONSISTENT_NAMING',
              file: filePath,
              message: `Missing expected clean name: ${expectedName}`
            });
          }
        }
      } catch (error) {
        // Skip files that can't be read
      }
    }
  }
  checks++;
}

// =============================================================================
// RUN VALIDATIONS
// =============================================================================

console.log('🔍 Checking for version numbers in paths...');
checkVersionNumbersInPaths();

console.log('🔍 Checking for superfluous naming...');
checkSuperfluousNaming();

console.log('🔍 Checking for versioned imports...');
checkVersionedImports();

console.log('🔍 Checking for consistent naming...');
checkConsistentNaming();

// =============================================================================
// REPORT RESULTS
// =============================================================================

console.log('\n' + '='.repeat(60));
console.log('📊 Architecture Guidelines Validation Results');
console.log('='.repeat(60));

console.log(`Total Checks: ${checks}`);
console.log(`Violations Found: ${violations.length}`);

if (violations.length === 0) {
  console.log('\n🎉 All architecture guidelines are being followed!');
  console.log('✅ Clean architecture integrity maintained');
  console.log('✅ No version proliferation detected');
  console.log('✅ Consistent naming patterns in use');
  console.log('✅ No superfluous qualifiers found');
  process.exit(0);
} else {
  console.log('\n⚠️ Architecture guideline violations detected:');
  console.log('');
  
  const violationsByType = violations.reduce((acc, violation) => {
    if (!acc[violation.type]) acc[violation.type] = [];
    acc[violation.type].push(violation);
    return acc;
  }, {});
  
  for (const [type, typeViolations] of Object.entries(violationsByType)) {
    console.log(`\n${type}:`);
    for (const violation of typeViolations) {
      console.log(`  ❌ ${violation.file}: ${violation.message}`);
    }
  }
  
  console.log('\n🔧 Please fix these violations to maintain clean architecture.');
  console.log('📖 Refer to .perkdrules for detailed guidelines.');
  process.exit(1);
}
