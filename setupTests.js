/**
 * Test Setup Configuration
 *
 * Global test setup and mocks
 */

// Mock React Native Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
    select: jest.fn((obj) => obj.ios || obj.default),
  },
  SafeAreaView: 'SafeAreaView',
  Text: 'Text',
  View: 'View',
  StyleSheet: {
    create: jest.fn((styles) => styles),
  },
  AppState: {
    currentState: 'active',
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
  },
  Linking: {
    openURL: jest.fn(),
    canOpenURL: jest.fn(() => Promise.resolve(true)),
  },
  Alert: {
    alert: jest.fn(),
  },
  Dimensions: {
    get: jest.fn(() => ({ width: 375, height: 812 })),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
  },
}));

// Mock React Native MMKV
jest.mock('react-native-mmkv', () => ({
  MMKV: jest.fn().mockImplementation(() => ({
    set: jest.fn(),
    getString: jest.fn(),
    getNumber: jest.fn(),
    getBoolean: jest.fn(),
    contains: jest.fn(),
    delete: jest.fn(),
    clearAll: jest.fn(),
    getAllKeys: jest.fn(() => []),
  })),
}));

// Note: We use MMKV for storage, not AsyncStorage

// Mock React Native Permissions
jest.mock('react-native-permissions', () => ({
  PERMISSIONS: {
    IOS: {
      CAMERA: 'ios.permission.CAMERA',
      MICROPHONE: 'ios.permission.MICROPHONE',
      LOCATION_WHEN_IN_USE: 'ios.permission.LOCATION_WHEN_IN_USE',
      NOTIFICATIONS: 'ios.permission.NOTIFICATIONS',
      PHOTO_LIBRARY: 'ios.permission.PHOTO_LIBRARY',
      CONTACTS: 'ios.permission.CONTACTS',
      FACE_ID: 'ios.permission.FACE_ID',
      BLUETOOTH_PERIPHERAL: 'ios.permission.BLUETOOTH_PERIPHERAL',
    },
    ANDROID: {
      CAMERA: 'android.permission.CAMERA',
      RECORD_AUDIO: 'android.permission.RECORD_AUDIO',
      ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
      POST_NOTIFICATIONS: 'android.permission.POST_NOTIFICATIONS',
      READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE',
      READ_CONTACTS: 'android.permission.READ_CONTACTS',
      USE_FINGERPRINT: 'android.permission.USE_FINGERPRINT',
      BLUETOOTH: 'android.permission.BLUETOOTH',
      BLUETOOTH_CONNECT: 'android.permission.BLUETOOTH_CONNECT',
      ACCESS_BACKGROUND_LOCATION: 'android.permission.ACCESS_BACKGROUND_LOCATION',
    },
  },
  RESULTS: {
    GRANTED: 'granted',
    DENIED: 'denied',
    BLOCKED: 'blocked',
    UNAVAILABLE: 'unavailable',
    LIMITED: 'limited',
  },
  check: jest.fn(() => Promise.resolve('granted')),
  request: jest.fn(() => Promise.resolve('granted')),
  checkMultiple: jest.fn(() => Promise.resolve({})),
  requestMultiple: jest.fn(() => Promise.resolve({})),
  openSettings: jest.fn(() => Promise.resolve()),
}));

// Mock React Native Device Info
jest.mock('react-native-device-info', () => ({
  getUniqueId: jest.fn(() => Promise.resolve('test-device-id')),
  getSystemVersion: jest.fn(() => '15.0'),
  getVersion: jest.fn(() => '1.0.0'),
  getBuildNumber: jest.fn(() => '1'),
  getDeviceId: jest.fn(() => 'iPhone13,2'),
  getSystemName: jest.fn(() => 'iOS'),
  isEmulator: jest.fn(() => Promise.resolve(false)),
  hasNotch: jest.fn(() => true),
  getDeviceType: jest.fn(() => 'Handset'),
}));

// Mock react-redux to avoid ES module issues
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn(),
  Provider: ({ children }) => children,
}));

// Mock redux-persist
jest.mock('redux-persist/integration/react', () => ({
  PersistGate: ({ children }) => children,
}));

// Mock global objects that might be used in legacy code
global._ = {
  Card: {
    findById: jest.fn((id) => ({ id, name: 'Test Card', personId: 'person-1', deletedAt: null, endTime: null })),
    findAll: jest.fn(() => {
      const mockResults = [
        { id: 'card-1', name: 'Card 1', personId: 'person-1', deletedAt: null, endTime: null },
        { id: 'card-2', name: 'Card 2', personId: 'person-1', deletedAt: null, endTime: null },
      ];
      mockResults.filtered = jest.fn((query, ...params) => {
        const filteredResults = [...mockResults];
        // Recursively add filtered method to support chaining
        filteredResults.filtered = jest.fn((query, ...params) => {
          const chainedResults = [...filteredResults];
          chainedResults.filtered = jest.fn((query, ...params) => chainedResults);
          return chainedResults;
        });
        return filteredResults;
      });
      return mockResults;
    }),
    all: jest.fn(() => {
      const mockResults = [
        { id: 'card-1', name: 'Card 1', personId: 'person-1', deletedAt: null, endTime: null },
        { id: 'card-2', name: 'Card 2', personId: 'person-1', deletedAt: null, endTime: null },
      ];
      mockResults.filtered = jest.fn((query, ...params) => {
        const filteredResults = [...mockResults];
        // Recursively add filtered method to support chaining
        filteredResults.filtered = jest.fn((query, ...params) => {
          const chainedResults = [...filteredResults];
          chainedResults.filtered = jest.fn((query, ...params) => chainedResults);
          return chainedResults;
        });
        return filteredResults;
      });
      return mockResults;
    }),
  },
  Offer: {
    findById: jest.fn((id) => ({
      id,
      title: 'Test Offer',
      cardId: 'card-1',
      masterId: 'master-1',
      endTime: null,
      when: { redeemed: null }
    })),
    findAll: jest.fn(() => {
      const mockResults = [
        { id: 'offer-1', title: 'Offer 1', cardId: 'card-1', masterId: 'master-1', endTime: null, when: { redeemed: null } },
        { id: 'offer-2', title: 'Offer 2', cardId: 'card-2', masterId: 'master-2', endTime: null, when: { redeemed: null } },
      ];
      // Add Realm-like filtered method with chaining support
      mockResults.filtered = jest.fn((query, ...params) => {
        const filteredResults = [...mockResults];
        // Recursively add filtered method to support chaining
        filteredResults.filtered = jest.fn((query, ...params) => {
          const chainedResults = [...filteredResults];
          chainedResults.filtered = jest.fn((query, ...params) => chainedResults);
          return chainedResults;
        });
        return filteredResults;
      });
      return mockResults;
    }),
    all: jest.fn(() => {
      const mockResults = [
        { id: 'offer-1', title: 'Offer 1', cardId: 'card-1', masterId: 'master-1', endTime: null, when: { redeemed: null } },
        { id: 'offer-2', title: 'Offer 2', cardId: 'card-2', masterId: 'master-2', endTime: null, when: { redeemed: null } },
      ];
      // Add Realm-like filtered method with chaining support
      mockResults.filtered = jest.fn((query, ...params) => {
        const filteredResults = [...mockResults];
        // Recursively add filtered method to support chaining
        filteredResults.filtered = jest.fn((query, ...params) => {
          const chainedResults = [...filteredResults];
          chainedResults.filtered = jest.fn((query, ...params) => chainedResults);
          return chainedResults;
        });
        return filteredResults;
      });
      return mockResults;
    }),
  },
};

global.$ = {
  Event: {
    emit: jest.fn(),
    on: jest.fn(),
  },
};

// Mock Notifee
jest.mock('@notifee/react-native', () => ({
  requestPermission: jest.fn(() => Promise.resolve({ authorizationStatus: 1 })),
  getInitialNotification: jest.fn(() => Promise.resolve(null)),
  onForegroundEvent: jest.fn(() => () => {}),
  onBackgroundEvent: jest.fn(() => () => {}),
  displayNotification: jest.fn(() => Promise.resolve()),
  createChannel: jest.fn(() => Promise.resolve()),
  createChannelGroup: jest.fn(() => Promise.resolve()),
  cancelNotification: jest.fn(() => Promise.resolve()),
  cancelAllNotifications: jest.fn(() => Promise.resolve()),
  setBadgeCount: jest.fn(() => Promise.resolve()),
  getBadgeCount: jest.fn(() => Promise.resolve(0)),
  AuthorizationStatus: {
    NOT_DETERMINED: 0,
    DENIED: 1,
    AUTHORIZED: 2,
    PROVISIONAL: 3,
  },
  EventType: {
    DISMISSED: 0,
    PRESS: 1,
    ACTION_PRESS: 2,
    DELIVERED: 3,
    APP_BLOCKED: 4,
    CHANNEL_BLOCKED: 5,
    CHANNEL_GROUP_BLOCKED: 6,
    TRIGGER_NOTIFICATION_CREATED: 7,
    UNKNOWN: 8,
  },
}));

// Mock React Native Background Fetch
jest.mock('react-native-background-fetch', () => ({
  configure: jest.fn(),
  start: jest.fn(),
  stop: jest.fn(),
  finish: jest.fn(),
  status: jest.fn(() => Promise.resolve(1)),
  STATUS_RESTRICTED: 0,
  STATUS_DENIED: 1,
  STATUS_AVAILABLE: 2,
  FETCH_RESULT_NEW_DATA: 0,
  FETCH_RESULT_NO_DATA: 1,
  FETCH_RESULT_FAILED: 2,
}));

// Mock React Native Biometrics
jest.mock('react-native-biometrics', () => ({
  isSensorAvailable: jest.fn(() => Promise.resolve({ available: true, biometryType: 'FaceID' })),
  createKeys: jest.fn(() => Promise.resolve({ publicKey: 'test-public-key' })),
  deleteKeys: jest.fn(() => Promise.resolve({ keysDeleted: true })),
  createSignature: jest.fn(() => Promise.resolve({ success: true, signature: 'test-signature' })),
  simplePrompt: jest.fn(() => Promise.resolve({ success: true })),
  BiometryTypes: {
    TouchID: 'TouchID',
    FaceID: 'FaceID',
    Biometrics: 'Biometrics',
  },
}));

// Mock React Native Keychain
jest.mock('react-native-keychain', () => ({
  setInternetCredentials: jest.fn(() => Promise.resolve()),
  getInternetCredentials: jest.fn(() => Promise.resolve({ username: 'test', password: 'test' })),
  resetInternetCredentials: jest.fn(() => Promise.resolve()),
  canImplyAuthentication: jest.fn(() => Promise.resolve(true)),
  getSupportedBiometryType: jest.fn(() => Promise.resolve('FaceID')),
  SECURITY_LEVEL: {
    SECURE_SOFTWARE: 'SECURE_SOFTWARE',
    SECURE_HARDWARE: 'SECURE_HARDWARE',
    ANY: 'ANY',
  },
  ACCESSIBLE: {
    WHEN_UNLOCKED: 'WHEN_UNLOCKED',
    AFTER_FIRST_UNLOCK: 'AFTER_FIRST_UNLOCK',
    ALWAYS: 'ALWAYS',
    WHEN_PASSCODE_SET_THIS_DEVICE_ONLY: 'WHEN_PASSCODE_SET_THIS_DEVICE_ONLY',
    WHEN_UNLOCKED_THIS_DEVICE_ONLY: 'WHEN_UNLOCKED_THIS_DEVICE_ONLY',
    AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY: 'AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY',
    ALWAYS_THIS_DEVICE_ONLY: 'ALWAYS_THIS_DEVICE_ONLY',
  },
  ACCESS_CONTROL: {
    BIOMETRY_ANY: 'BIOMETRY_ANY',
    BIOMETRY_CURRENT_SET: 'BIOMETRY_CURRENT_SET',
    DEVICE_PASSCODE: 'DEVICE_PASSCODE',
    APPLICATION_PASSWORD: 'APPLICATION_PASSWORD',
    BIOMETRY_ANY_OR_DEVICE_PASSCODE: 'BIOMETRY_ANY_OR_DEVICE_PASSCODE',
    BIOMETRY_CURRENT_SET_OR_DEVICE_PASSCODE: 'BIOMETRY_CURRENT_SET_OR_DEVICE_PASSCODE',
  },
  AUTHENTICATION_TYPE: {
    DEVICE_PASSCODE_OR_BIOMETRICS: 'DEVICE_PASSCODE_OR_BIOMETRICS',
    BIOMETRICS: 'BIOMETRICS',
  },
  BIOMETRY_TYPE: {
    TOUCH_ID: 'TouchID',
    FACE_ID: 'FaceID',
    FINGERPRINT: 'Fingerprint',
    FACE: 'Face',
    IRIS: 'Iris',
  },
}));

// Mock Firebase
jest.mock('@react-native-firebase/app', () => ({
  default: () => ({
    utils: () => ({
      FilePath: {
        PICTURES_DIRECTORY: '/pictures',
        MOVIES_DIRECTORY: '/movies',
      },
    }),
  }),
}));

jest.mock('@react-native-firebase/messaging', () => ({
  default: () => ({
    hasPermission: jest.fn(() => Promise.resolve(1)),
    subscribeToTopic: jest.fn(),
    unsubscribeFromTopic: jest.fn(),
    requestPermission: jest.fn(() => Promise.resolve(1)),
    getToken: jest.fn(() => Promise.resolve('test-fcm-token')),
    onMessage: jest.fn(() => () => {}),
    onNotificationOpenedApp: jest.fn(() => () => {}),
    getInitialNotification: jest.fn(() => Promise.resolve(null)),
    setBackgroundMessageHandler: jest.fn(),
  }),
  AuthorizationStatus: {
    NOT_DETERMINED: -1,
    DENIED: 0,
    AUTHORIZED: 1,
    PROVISIONAL: 2,
  },
}));

// Mock __DEV__ global
global.__DEV__ = true;



// Mock global functions that tests expect
global.checkAndRequestPermission = jest.fn(() => Promise.resolve({ status: 'granted' }));

// Mock require calls for legacy services
const originalRequire = require;
require = jest.fn((modulePath) => {
  if (modulePath === '../../lib/common/services/offer') {
    return {
      default: {
        request: jest.fn(() => Promise.resolve({ success: true, offerId: 'offer-123' })),
        redeem: jest.fn(() => Promise.resolve({ success: true, redeemed: true })),
        share: jest.fn(() => Promise.resolve({ success: true, shared: true })),
        fetch: jest.fn(() => Promise.resolve({ success: true, offers: [] })),
        notified: jest.fn(() => Promise.resolve({ success: true })),
        shareToMany: jest.fn(() => Promise.resolve({ success: true })),
        shareCancel: jest.fn(() => Promise.resolve({ success: true })),
      },
    };
  }
  if (modulePath === '../../lib/common/services/card') {
    return {
      default: {
        register: jest.fn(() => Promise.resolve({ cardId: 'card-123', success: true })),
        accept: jest.fn(() => Promise.resolve({ success: true })),
        decline: jest.fn(() => Promise.resolve({ success: true })),
        registerIssued: jest.fn(() => Promise.resolve({ id: 'card-123', success: true })),
        update: jest.fn(() => Promise.resolve({ success: true })),
        delete: jest.fn(() => Promise.resolve({ success: true })),
      },
    };
  }
  if (modulePath === '../../lib/cards') {
    return {
      Cards: {
        register: jest.fn(() => Promise.resolve({ id: 'card-123', success: true })),
        accept: jest.fn(() => Promise.resolve({ success: true })),
        decline: jest.fn(() => Promise.resolve({ success: true })),
        registerIssued: jest.fn(() => Promise.resolve({ id: 'card-123', success: true })),
        pay: jest.fn(() => Promise.resolve({ success: true })),
        qualify: jest.fn(() => Promise.resolve({ success: true })),
        notified: jest.fn(() => Promise.resolve({ success: true })),
        cleanup: jest.fn(() => Promise.resolve({ success: true })),
      },
    };
  }
  if (modulePath === '../../lib/Events.json') {
    return {
      Card: {
        register: 'card.register',
        updated: 'card.updated',
        deleted: 'card.deleted',
      },
      Offer: {
        received: 'offer.received',
        redeemed: 'offer.redeemed',
        shared: 'offer.shared',
        created: 'offer.created',
        updated: 'offer.updated',
        deleted: 'offer.deleted',
        expired: 'offer.expired',
      },
    };
  }
  return originalRequire(modulePath);
});
global.checkPermissionStatus = jest.fn(() => Promise.resolve('granted'));

// Mock permission checking functions
global.checkPermissionStatus = jest.fn(() => Promise.resolve({ status: 'allowed', options: [] }));
global.requestNativePermission = jest.fn(() => Promise.resolve(true));

// Mock installation functions
global.initializeDevice = jest.fn(() => Promise.resolve({ id: 'test-device', platform: 'ios' }));
global.initializeApp = jest.fn(() => Promise.resolve({ version: '1.0.0', build: '1' }));
global.initializeCarrier = jest.fn(() => Promise.resolve({ name: 'Test Carrier', mcc: '310', mnc: '260' }));
global.initializeLocale = jest.fn(() => Promise.resolve({ language: 'en', region: 'US' }));
global.initializeCapabilities = jest.fn(() => Promise.resolve({ biometrics: true, notifications: true }));
global.initializeTokens = jest.fn(() => Promise.resolve({}));
global.initializePermissions = jest.fn(() => Promise.resolve([]));
global.initializePayments = jest.fn(() => Promise.resolve({}));
global.restoreCachedState = jest.fn(() => Promise.resolve({}));
global.persistInstallationState = jest.fn(() => Promise.resolve());
global.selectNotificationProvider = jest.fn((platform, region) => {
  if (platform === 'ios') {
    return region === 'CN' ? 'apns-cn' : 'apns';
  }
  return region === 'CN' ? 'fcm-cn' : 'fcm';
});
global.handleNotificationSync = jest.fn();
global.handleNotificationEngagement = jest.fn();

// Mock provider instances for notifications
global.loadNotificationProvider = jest.fn(() => Promise.resolve({
  init: jest.fn(),
  register: jest.fn(() => Promise.resolve({ alert: true })),
  getToken: jest.fn(() => Promise.resolve('test-token')),
  setToken: jest.fn(),
}));

// Mock Realm and database functions
global.DatabaseManager = {
  getInstance: jest.fn(() => ({
    write: jest.fn(),
    create: jest.fn(),
    delete: jest.fn(),
    objects: jest.fn(() => []),
  })),
};



// Mock additional React Native modules
jest.mock('react-native-localize', () => ({
  getCountry: jest.fn(() => 'US'),
  getLocales: jest.fn(() => [{ countryCode: 'US', languageTag: 'en-US' }]),
  getTimeZone: jest.fn(() => 'America/New_York'),
  uses24HourClock: jest.fn(() => false),
  usesMetricSystem: jest.fn(() => false),
}));

jest.mock('react-native-haptic-feedback', () => ({
  trigger: jest.fn(),
  HapticFeedbackTypes: {
    selection: 'selection',
    impactLight: 'impactLight',
    impactMedium: 'impactMedium',
    impactHeavy: 'impactHeavy',
    notificationSuccess: 'notificationSuccess',
    notificationWarning: 'notificationWarning',
    notificationError: 'notificationError',
  },
}));
