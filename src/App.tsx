/**
 * Perkd App Component
 * Simple, robust app entry point with Redux integration and navigation
 */

import React from 'react';
import { SafeAreaView, Text, StyleSheet } from 'react-native';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from './core/store';
import { TabNavigator } from './navigation/TabNavigator';

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <PersistGate loading={<LoadingScreen />} persistor={persistor}>
        <TabNavigator />
      </PersistGate>
    </Provider>
  );
};

const LoadingScreen: React.FC = () => (
  <SafeAreaView style={styles.container}>
    <Text style={styles.loading}>Loading...</Text>
  </SafeAreaView>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loading: {
    fontSize: 18,
    color: '#666',
  },
});

export default App;
