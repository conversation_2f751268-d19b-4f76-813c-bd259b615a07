/**
 * Legacy Event Bridge
 * Simple bridge to gradually migrate from v6 event system to Redux
 */

import { store } from '../store';

// Event types from v6 system (simplified)
export type LegacyEventType = 
  | 'CARD_UPDATED'
  | 'OFFER_RECEIVED'
  | 'USER_AUTHENTICATED'
  | 'PAYMENT_COMPLETED';

export interface LegacyEvent {
  type: LegacyEventType;
  payload: any;
  timestamp: number;
}

// Simple event emitter for legacy compatibility
class EventBridge {
  private listeners: Map<LegacyEventType, Array<(event: LegacyEvent) => void>> = new Map();

  // Subscribe to legacy events
  on(eventType: LegacyEventType, callback: (event: LegacyEvent) => void) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType)?.push(callback);
  }

  // Emit legacy events (for gradual migration)
  emit(eventType: LegacyEventType, payload: any) {
    const event: LegacyEvent = {
      type: eventType,
      payload,
      timestamp: Date.now(),
    };

    // Notify legacy listeners
    const callbacks = this.listeners.get(eventType) || [];
    callbacks.forEach(callback => callback(event));

    // TODO: Convert to Redux actions as we migrate features
    this.convertToReduxAction(event);
  }

  private convertToReduxAction(event: LegacyEvent) {
    // Simple conversion - expand as we add Redux slices
    console.log('Legacy event:', event.type, event.payload);
    
    // Example: store.dispatch(someAction(event.payload));
  }
}

export const eventBridge = new EventBridge();
