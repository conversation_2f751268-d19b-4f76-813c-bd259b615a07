/**
 * Core Database Module
 * 
 * Centralized database configuration and management for Perkd v7.0
 * Follows v7 architecture patterns with clean separation of concerns
 */

// Re-export database components for clean API
export { DatabaseManager, REALM_CONFIG, CURRENT_SCHEMA_VERSION } from './config';
export { allSchemas, schemaDefinitions, getSchema, hasSchema, getSchemaNames } from './schemas';
export { runMigrations } from './migrations';

// Export types
export type { DatabaseStatistics } from './types';
