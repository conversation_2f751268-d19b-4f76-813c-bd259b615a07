/**
 * Core Database Schemas
 * 
 * Centralized schema definitions for Perkd v7.0
 * Organized by dependency order and category for optimal performance
 */

import { ObjectSchema } from 'realm';

// Core Model Schemas (Phase 1 - Main entities)
import { CardSchema } from '../../data/models/Card';
import { CardMasterSchema } from '../../data/models/CardMaster';
import { PersonSchema } from '../../data/models/Person';

// Content Model Schemas (Phase 2 - Content entities)
import { OfferSchema } from '../../data/models/Offer';
import { MessageSchema } from '../../data/models/Message';
import { RewardSchema } from '../../data/models/Reward';

// System Model Schemas (Phase 3 - System entities)
import { SettingsSchema } from '../../data/models/Settings';
import { ActionSchema } from '../../data/models/Action';
import { AppEventSchema } from '../../data/models/AppEvent';
import { WatchdogSchema } from '../../data/models/Watchdog';
import { PlaceSchema } from '../../data/models/Place';
import { PreferenceSchema } from '../../data/models/Preference';
import { WidgetDataSchema } from '../../data/models/WidgetData';
import { MerchantSchema } from '../../data/models/Merchant';

// Embedded Schemas (Phase 1 - Embedded objects)
import { StoredValueSchema } from '../../data/models/embedded/StoredValue';
import { PersonNameSchema } from '../../data/models/embedded/PersonName';
import { CardMasterBrandSchema } from '../../data/models/embedded/CardMasterBrand';
import { CardMasterFeaturesSchema } from '../../data/models/embedded/CardMasterFeatures';
import { CardMasterPoliciesSchema } from '../../data/models/embedded/CardMasterPolicies';
import { CardMasterDiscoverSchema } from '../../data/models/embedded/CardMasterDiscover';
import { CardMasterRemindersSchema } from '../../data/models/embedded/CardMasterReminders';
import { CardMasterStoredValueSchema } from '../../data/models/embedded/CardMasterStoredValue';
import { CardMasterImagesSchema } from '../../data/models/embedded/CardMasterImages';

// Relationship Schemas (Phase 2 - Relationship objects)
// Card ecosystem relationships
import {
  CardSharerSchema,
  CardWhenSchema,
  CardWidgetSchema,
  CustomImageSchema,
  SharingSchema,
  SharingWhenSchema,
  SharingRecipientSchema,
} from '../../data/models/relationships/card';

// CardMaster ecosystem relationships
import {
  CardMasterTagSchema,
  CardMasterOptionsSchema,
  CardMasterGlobalizeSchema,
  WidgetSchema,
  WidgetGlobalizeSchema,
} from '../../data/models/relationships/cardmaster';

// Person ecosystem relationships
import {
  PersonDateSchema,
  PersonPhoneSchema,
  PersonEmailSchema,
  PersonAddressSchema,
  PersonAddressGeometrySchema,
  ProfileImageSchema,
  PersonBrandsSchema,
  PersonProductsSchema,
  PersonProductSchema,
} from '../../data/models/relationships/person';

// Offer ecosystem relationships
import {
  OfferCodeSchema,
  OfferImageSchema,
  OfferDiscountSchema,
  RedemptionSchema,
  OfferSharerSchema,
  OfferGlobalizeSchema,
  OfferWhenSchema,
  AppletWebviewSchema,
} from '../../data/models/relationships/offer';

// Shared cross-domain relationships
import {
  WidgetDataBadgeSchema,
  WidgetDataWhenSchema,
  MessageSenderSchema,
  MessageCoverWebviewSchema,
  MessageBodyWebviewSchema,
  MessageGlobalizeSchema,
  MessageWhenSchema,
  RewardLogoSchema,
  RewardLevelSchema,
  RewardOptionsSchema,
  RewardGlobalizeSchema,
  RewardWhenSchema,
  RewardTransactionSchema,
} from '../../data/models/relationships/shared';

// =============================================================================
// SCHEMA ORGANIZATION
// =============================================================================

/**
 * All schemas organized by dependency order
 * 
 * Order is critical for Realm initialization:
 * 1. Main entities (no dependencies)
 * 2. Embedded objects (depend on main entities)
 * 3. Relationship objects (depend on main and embedded)
 */
export const allSchemas: ObjectSchema[] = [
  // Phase 1: Main entities (no dependencies)
  SettingsSchema,
  PersonSchema,
  CardSchema,
  CardMasterSchema,
  OfferSchema,
  RewardSchema,
  MessageSchema,
  PlaceSchema,
  ActionSchema,
  WidgetDataSchema,
  MerchantSchema,
  AppEventSchema,
  WatchdogSchema,
  PreferenceSchema,

  // Phase 2: Embedded objects (dependency order matters!)
  StoredValueSchema,
  PersonNameSchema,
  CardMasterBrandSchema,
  CardMasterFeaturesSchema,
  CardMasterPoliciesSchema,
  CardMasterDiscoverSchema,
  CardMasterRemindersSchema,
  CardMasterStoredValueSchema,
  CardMasterImagesSchema,

  // Phase 3: Relationship objects (complex dependencies)
  // Card ecosystem relationships
  CardSharerSchema,
  CardWhenSchema,
  CardWidgetSchema,
  CustomImageSchema,
  SharingSchema,
  SharingWhenSchema,
  SharingRecipientSchema,

  // CardMaster ecosystem relationships
  CardMasterTagSchema,
  CardMasterOptionsSchema,
  CardMasterGlobalizeSchema,
  WidgetSchema,
  WidgetGlobalizeSchema,

  // Person ecosystem relationships
  PersonDateSchema,
  PersonPhoneSchema,
  PersonEmailSchema,
  PersonAddressSchema,
  PersonAddressGeometrySchema,
  ProfileImageSchema,
  PersonBrandsSchema,
  PersonProductsSchema,
  PersonProductSchema,

  // Offer ecosystem relationships
  OfferCodeSchema,
  OfferImageSchema,
  OfferDiscountSchema,
  RedemptionSchema,
  OfferSharerSchema,
  OfferGlobalizeSchema,
  OfferWhenSchema,
  AppletWebviewSchema,

  // Shared cross-domain relationships
  WidgetDataBadgeSchema,
  WidgetDataWhenSchema,
  MessageSenderSchema,
  MessageCoverWebviewSchema,
  MessageBodyWebviewSchema,
  MessageGlobalizeSchema,
  MessageWhenSchema,
  RewardLogoSchema,
  RewardLevelSchema,
  RewardOptionsSchema,
  RewardGlobalizeSchema,
  RewardWhenSchema,
  RewardTransactionSchema,
];

/**
 * Schema definitions by name for easy lookup
 */
export const schemaDefinitions: Record<string, ObjectSchema> = {};

// Populate schema definitions map
allSchemas.forEach((schema, index) => {
  if (!schema || !schema.name) {
    console.error(`Invalid schema at index ${index}:`, schema);
    throw new Error(`Schema at index ${index} is undefined or missing name property`);
  }
  schemaDefinitions[schema.name] = schema;
});

// =============================================================================
// SCHEMA UTILITIES
// =============================================================================

/**
 * Get schema by name
 */
export function getSchema(name: string): ObjectSchema | undefined {
  return schemaDefinitions[name];
}

/**
 * Check if schema exists
 */
export function hasSchema(name: string): boolean {
  return name in schemaDefinitions;
}

/**
 * Get all schema names
 */
export function getSchemaNames(): string[] {
  return Object.keys(schemaDefinitions);
}

/**
 * Get schemas by category
 */
export function getSchemasByCategory(category: 'core' | 'embedded' | 'relationship'): ObjectSchema[] {
  // This is a simplified categorization - could be enhanced with metadata
  const coreSchemas = allSchemas.slice(0, 14); // Main entities
  const embeddedSchemas = allSchemas.slice(14, 23); // Embedded objects
  const relationshipSchemas = allSchemas.slice(23); // Relationship objects

  switch (category) {
    case 'core':
      return coreSchemas;
    case 'embedded':
      return embeddedSchemas;
    case 'relationship':
      return relationshipSchemas;
    default:
      return [];
  }
}

/**
 * Validate schema dependencies
 */
export function validateSchemaDependencies(): { isValid: boolean; issues: string[] } {
  const issues: string[] = [];
  
  // Basic validation - could be enhanced with more sophisticated dependency checking
  if (allSchemas.length === 0) {
    issues.push('No schemas defined');
  }

  // Check for duplicate schema names
  const names = new Set<string>();
  allSchemas.forEach(schema => {
    if (names.has(schema.name)) {
      issues.push(`Duplicate schema name: ${schema.name}`);
    }
    names.add(schema.name);
  });

  return {
    isValid: issues.length === 0,
    issues
  };
}
