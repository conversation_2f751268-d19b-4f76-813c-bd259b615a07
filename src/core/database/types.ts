/**
 * Database Types
 *
 * TypeScript definitions for V7 fresh start database operations
 */

import { ObjectSchema } from 'realm';

/**
 * Schema information interface
 */
export interface SchemaInfo {
  name: string;
  schema: ObjectSchema;
  dependencies: string[];
  category: 'core' | 'embedded' | 'relationship';
}

/**
 * Database statistics interface
 */
export interface DatabaseStatistics {
  schemaVersion: number;
  objectCounts: Record<string, number>;
  databaseSize: number;
  lastModified: Date;
}

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  schemaVersion: number;
  objectCounts: Record<string, number>;
  indexCount: number;
  performanceImprovements: Record<string, string>;
  technicalDebtEliminated: string;
}
