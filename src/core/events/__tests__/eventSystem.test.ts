/**
 * Enhanced Event System Tests
 * 
 * Tests for the enhanced event system with TypeScript support
 */

import { PerkdEventEmitter, globalEventEmitter, legacyEventWrapper } from '../globalEventEmitter';
import { EventSystemManager, eventSystemManager } from '../integrationHelpers';
import { EVENT_TYPES } from '../eventDefinitions';

describe('Enhanced Event System', () => {
  beforeEach(() => {
    // Reset event system before each test
    globalEventEmitter.removeAllListeners();
    globalEventEmitter.resetMetrics();
    eventSystemManager.reset();
  });

  describe('PerkdEventEmitter', () => {
    it('should create enhanced event emitter with proper configuration', () => {
      const emitter = new PerkdEventEmitter();
      
      expect(emitter).toBeDefined();
      expect(typeof emitter.emit).toBe('function');
      expect(typeof emitter.safeEmit).toBe('function');
      expect(typeof emitter.on).toBe('function');
      expect(typeof emitter.getMetrics).toBe('function');
    });

    it('should emit and listen to events with type safety', () => {
      const emitter = new PerkdEventEmitter();
      const mockListener = jest.fn();
      
      // Add listener
      emitter.on('card.updated', mockListener);
      
      // Emit event
      const eventData = {
        cardId: 'test-card-123',
        changes: { name: 'Updated Card' },
        timestamp: new Date().toISOString(),
      };
      
      const result = emitter.safeEmit('card.updated', eventData);
      
      expect(result).toBe(true);
      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          cardId: 'test-card-123',
          changes: { name: 'Updated Card' },
          _enhanced: true,
        })
      );
    });

    it('should track performance metrics', () => {
      const emitter = new PerkdEventEmitter();
      
      // Emit some events
      emitter.safeEmit('card.updated', { cardId: '1' });
      emitter.safeEmit('offer.received', { offerId: '2' });
      emitter.safeEmit('card.view', { cardId: '3' });
      
      const metrics = emitter.getMetrics();
      
      expect(metrics.totalEvents).toBe(3);
      expect(metrics.eventsByType['card.updated']).toBe(1);
      expect(metrics.eventsByType['offer.received']).toBe(1);
      expect(metrics.eventsByType['card.view']).toBe(1);
      expect(metrics.errorCount).toBe(0);
    });

    it('should handle errors gracefully', () => {
      const emitter = new PerkdEventEmitter();
      
      // Add listener that throws error
      emitter.on('test.error', () => {
        throw new Error('Test error');
      });
      
      // Emit event - should not throw
      expect(() => {
        emitter.safeEmit('test.error', {});
      }).not.toThrow();
      
      const metrics = emitter.getMetrics();
      expect(metrics.errorCount).toBeGreaterThan(0);
    });

    it('should support Redux bridge integration', () => {
      const emitter = new PerkdEventEmitter();
      const mockStore = { dispatch: jest.fn() };
      
      // Enable Redux bridge
      emitter.enableReduxBridge(mockStore);
      
      expect(emitter.isReduxBridgeEnabled()).toBe(true);
      
      // Disable Redux bridge
      emitter.disableReduxBridge();
      
      expect(emitter.isReduxBridgeEnabled()).toBe(false);
    });
  });

  describe('Legacy Compatibility', () => {
    it('should provide legacy-compatible interface', () => {
      const mockListener = jest.fn();
      
      // Use legacy interface
      legacyEventWrapper.on('test.event', mockListener);
      legacyEventWrapper.emit('test.event', { data: 'test' });
      
      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          data: 'test',
          _enhanced: true,
        })
      );
    });

    it('should support legacy method signatures', () => {
      const mockListener = jest.fn();
      const mockContext = { name: 'test-context' };
      
      // Test with context
      legacyEventWrapper.on('test.context', mockListener, mockContext);
      legacyEventWrapper.emit('test.context', { data: 'test' });
      
      expect(mockListener).toHaveBeenCalled();
      
      // Test removeListener
      legacyEventWrapper.removeListener('test.context', mockListener, mockContext);
      legacyEventWrapper.emit('test.context', { data: 'test2' });
      
      // Should only be called once (from before removal)
      expect(mockListener).toHaveBeenCalledTimes(1);
    });
  });

  describe('Event System Manager', () => {
    it('should initialize event system manager', async () => {
      const manager = EventSystemManager.getInstance();
      
      expect(manager).toBeDefined();
      expect(manager.isSystemInitialized()).toBe(false);
      
      await manager.initialize();
      
      expect(manager.isSystemInitialized()).toBe(true);
    });

    it('should provide system health monitoring', async () => {
      await eventSystemManager.initialize();
      
      const health = eventSystemManager.getSystemHealth();
      
      expect(health).toHaveProperty('isInitialized');
      expect(health).toHaveProperty('legacyMode');
      expect(health).toHaveProperty('reduxIntegration');
      expect(health).toHaveProperty('metrics');
      expect(health).toHaveProperty('issues');
      expect(health).toHaveProperty('recommendations');
    });

    it('should provide system statistics', async () => {
      await eventSystemManager.initialize();
      
      // Emit some events
      globalEventEmitter.safeEmit('card.updated', { cardId: '1' });
      globalEventEmitter.safeEmit('offer.received', { offerId: '2' });
      
      const stats = eventSystemManager.getSystemStatistics();
      
      expect(stats).toHaveProperty('totalEvents');
      expect(stats).toHaveProperty('activeListeners');
      expect(stats).toHaveProperty('memoryUsage');
      expect(stats).toHaveProperty('errorRate');
      expect(stats).toHaveProperty('topEvents');
      
      expect(stats.totalEvents).toBeGreaterThan(0);
      expect(Array.isArray(stats.topEvents)).toBe(true);
    });

    it('should support mode switching', async () => {
      await eventSystemManager.initialize();
      
      expect(eventSystemManager.isLegacyMode()).toBe(true);
      
      eventSystemManager.switchToEnhancedMode();
      expect(eventSystemManager.isLegacyMode()).toBe(false);
      
      eventSystemManager.switchToLegacyMode();
      expect(eventSystemManager.isLegacyMode()).toBe(true);
    });

    it('should handle Redux integration', async () => {
      const mockStore = { dispatch: jest.fn() };
      
      await eventSystemManager.initialize({
        enableReduxIntegration: true,
        store: mockStore,
      });
      
      const health = eventSystemManager.getSystemHealth();
      expect(health.reduxIntegration).toBe(true);
      
      eventSystemManager.disableReduxIntegration();
      
      const healthAfter = eventSystemManager.getSystemHealth();
      expect(healthAfter.reduxIntegration).toBe(false);
    });
  });

  describe('Event Definitions', () => {
    it('should have proper event type definitions', () => {
      expect(EVENT_TYPES).toBeDefined();
      expect(EVENT_TYPES.Card).toBeDefined();
      expect(EVENT_TYPES.Offer).toBeDefined();
      expect(EVENT_TYPES.App).toBeDefined();
      
      // Check specific events
      expect(EVENT_TYPES.Card.updated).toBe('updated');
      expect(EVENT_TYPES.Card.view).toBe('card.view');
      expect(EVENT_TYPES.App.launched).toBe('app.launch.offline');
    });
  });

  describe('Performance', () => {
    it('should handle high event volume efficiently', () => {
      const emitter = new PerkdEventEmitter();
      const startTime = performance.now();
      
      // Emit many events
      for (let i = 0; i < 1000; i++) {
        emitter.safeEmit('performance.test', { index: i });
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(1000); // 1 second
      
      const metrics = emitter.getMetrics();
      expect(metrics.totalEvents).toBe(1000);
    });

    it('should track processing time metrics', () => {
      const emitter = new PerkdEventEmitter();
      
      // Add slow listener
      emitter.on('slow.event', () => {
        // Simulate slow processing
        const start = Date.now();
        while (Date.now() - start < 10) {
          // Busy wait for 10ms
        }
      });
      
      emitter.safeEmit('slow.event', {});
      
      const metrics = emitter.getMetrics();
      expect(metrics.averageProcessingTime).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should isolate listener errors', () => {
      const emitter = new PerkdEventEmitter();
      const goodListener = jest.fn();
      const badListener = jest.fn(() => {
        throw new Error('Bad listener');
      });
      
      emitter.on('test.mixed', goodListener);
      emitter.on('test.mixed', badListener);
      
      // Should not throw despite bad listener
      expect(() => {
        emitter.safeEmit('test.mixed', { data: 'test' });
      }).not.toThrow();
      
      // Good listener should still be called
      expect(goodListener).toHaveBeenCalled();
    });
  });
});
