/**
 * Perkd v7.0 Enhanced Event Definitions
 * 
 * TypeScript definitions for the existing event system
 * Preserves compatibility with Events.json while adding type safety
 */

// Import events from JSON file
import * as Events from '../../lib/Events.json';

// Legacy compatibility - same as main events
const LEGACY_EVENTS = Events;

// =============================================================================
// EVENT TYPE DEFINITIONS
// =============================================================================

/**
 * Core event data structure
 */
export interface BaseEventData {
  timestamp?: string;
  source?: 'user' | 'system' | 'api' | 'bridge' | 'realm';
  context?: Record<string, any>;
  metadata?: Record<string, any>;
  // Allow additional properties for flexibility
  [key: string]: any;
}

/**
 * Card-related event data
 */
export interface CardEventData extends BaseEventData {
  cardId?: string;
  masterId?: string;
  personId?: string;
  changes?: Record<string, any>;
  card?: any;
}

/**
 * Offer-related event data
 */
export interface OfferEventData extends BaseEventData {
  offerId?: string;
  cardId?: string;
  masterId?: string;
  personId?: string;
  changes?: Record<string, any>;
  offer?: any;
}

/**
 * Person-related event data
 */
export interface PersonEventData extends BaseEventData {
  personId?: string;
  changes?: Record<string, any>;
  person?: any;
}

/**
 * App-related event data
 */
export interface AppEventData extends BaseEventData {
  version?: string;
  from?: string;
  to?: string;
  duration?: number;
  syncType?: string;
  success?: boolean;
}

/**
 * Sync-related event data
 */
export interface SyncEventData extends BaseEventData {
  syncType: 'full' | 'incremental' | 'background';
  entities?: string[];
  success?: boolean;
  error?: string;
  duration?: number;
}

// =============================================================================
// TYPED EVENT DEFINITIONS
// =============================================================================

/**
 * Strongly typed event definitions based on Events.json
 */
export interface TypedEvents {
  // Card events
  [Events.Card.new]: CardEventData;
  [Events.Card.updated]: CardEventData;
  [Events.Card.deleted]: CardEventData;
  [Events.Card.view]: CardEventData;
  [Events.Card.accept]: CardEventData;
  [Events.Card.decline]: CardEventData;
  [Events.Card.share]: CardEventData;
  
  // Offer events
  [Events.Offer.new]: OfferEventData;
  [Events.Offer.updated]: OfferEventData;
  [Events.Offer.deleted]: OfferEventData;
  [Events.Offer.received]: OfferEventData;
  [Events.Offer.redeem]: OfferEventData;
  
  // Person events
  [Events.Account.updatedPerson]: PersonEventData;
  
  // App events
  [Events.App.upgraded]: AppEventData;
  [Events.App.launched]: AppEventData;
  [Events.App.resume]: AppEventData;
  [Events.App.pause]: AppEventData;
  [Events.App.login]: AppEventData;
  [Events.App.logout]: AppEventData;
  
  // Sync events
  [Events.Sync.all]: SyncEventData;
  [Events.Sync.cache]: SyncEventData;
  [Events.Sync.appEvent]: SyncEventData;
  
  // Generic events (for extensibility)
  [key: string]: BaseEventData;
}

// =============================================================================
// EVENT EMITTER INTERFACE
// =============================================================================

/**
 * Enhanced event emitter interface with type safety
 */
export interface TypedEventEmitter {
  // Type-safe event emission
  emit<K extends keyof TypedEvents>(event: K, data: TypedEvents[K]): boolean;
  safeEmit<K extends keyof TypedEvents>(event: K, data: TypedEvents[K]): boolean;
  
  // Type-safe event listening
  on<K extends keyof TypedEvents>(
    event: K, 
    listener: (data: TypedEvents[K]) => void,
    context?: any
  ): this;
  
  once<K extends keyof TypedEvents>(
    event: K, 
    listener: (data: TypedEvents[K]) => void,
    context?: any
  ): this;
  
  // Event removal
  removeListener<K extends keyof TypedEvents>(
    event: K, 
    listener: (data: TypedEvents[K]) => void,
    context?: any
  ): this;
  
  removeAllListeners(event?: keyof TypedEvents): this;
  
  // Performance monitoring
  getMetrics(): EventMetrics;
  
  // Redux bridge integration
  enableReduxBridge(): void;
  disableReduxBridge(): void;
}

// =============================================================================
// PERFORMANCE METRICS
// =============================================================================

export interface EventMetrics {
  totalEvents: number;
  eventsPerSecond: number;
  listenerCount: number;
  memoryUsage: number;
  averageProcessingTime: number;
  errorCount: number;
  lastActivity: number;
  
  // Event type breakdown
  eventsByType: Record<string, number>;
  
  // Performance thresholds
  thresholds: {
    maxListeners: number;
    maxProcessingTime: number;
    maxEventsPerSecond: number;
  };
}

// =============================================================================
// EVENT VALIDATION
// =============================================================================

/**
 * Event validation schema
 */
export interface EventValidationSchema {
  required?: string[];
  optional?: string[];
  types?: Record<string, string>;
  validators?: Record<string, (value: any) => boolean>;
}

/**
 * Event validation result
 */
export interface EventValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// =============================================================================
// BRIDGE INTEGRATION TYPES
// =============================================================================

/**
 * Event-Redux bridge configuration
 */
export interface BridgeEventConfig {
  // Events that should trigger Redux actions
  toRedux: {
    [K in keyof TypedEvents]?: {
      action: string;
      transform?: (data: TypedEvents[K]) => any;
      debounce?: number;
      condition?: (data: TypedEvents[K]) => boolean;
    };
  };
  
  // Redux actions that should trigger events
  fromRedux: {
    [actionType: string]: {
      event: keyof TypedEvents;
      transform?: (payload: any) => any;
      condition?: (payload: any) => boolean;
    };
  };
}

// =============================================================================
// LEGACY COMPATIBILITY
// =============================================================================

/**
 * Legacy event interface for backward compatibility
 */
export interface LegacyEventInterface {
  emit(event: string, data?: any): boolean;
  on(event: string, listener: Function, context?: any): this;
  once(event: string, listener: Function, context?: any): this;
  removeListener(event: string, listener: Function, context?: any): this;
  removeAllListeners(event?: string): this;
}

// =============================================================================
// EXPORTED CONSTANTS
// =============================================================================

/**
 * Event constants from Events.json with type safety
 */
export const EVENT_TYPES = LEGACY_EVENTS;

/**
 * New TypeScript event definitions (preferred)
 */
export const EVENT = Events;

/**
 * Event categories for organization
 */
export const EVENT_CATEGORIES = {
  BUSINESS: ['Card', 'Offer', 'Person', 'Account'],
  APPLICATION: ['App', 'Sync', 'Track'],
  UI: ['Widget', 'Message', 'Messaging'],
  COMMERCE: ['Bag', 'Shop', 'Payment'],
  LOCATION: ['Location', 'Geofence'],
  COMMUNICATION: ['Notification', 'Share'],
} as const;

/**
 * Default event configuration
 */
export const DEFAULT_EVENT_CONFIG = {
  maxListeners: 50,
  maxProcessingTime: 100, // ms
  maxEventsPerSecond: 1000,
  enableMetrics: __DEV__,
  enableValidation: __DEV__,
  enableReduxBridge: true,
} as const;

// =============================================================================
// UTILITY TYPES
// =============================================================================

/**
 * Extract event names by category
 */
export type EventsByCategory<T extends keyof typeof EVENT_CATEGORIES> = 
  typeof EVENT_CATEGORIES[T][number];

/**
 * Event listener function type
 */
export type EventListener<T extends BaseEventData = BaseEventData> = (data: T) => void;

/**
 * Event emission result
 */
export interface EventEmissionResult {
  success: boolean;
  listenerCount: number;
  processingTime: number;
  errors?: Error[];
}
