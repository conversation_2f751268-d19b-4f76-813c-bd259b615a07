/**
 * Perkd v7.0 Enhanced Global Event Emitter
 * 
 * Enhanced version of the existing event system with TypeScript support
 * Preserves full backward compatibility with $.Event global interface
 */

// Use Node.js EventEmitter for now (will integrate with existing EventEmitter3 later)
import { EventEmitter } from 'events';
import type {
  TypedEventEmitter,
  TypedEvents,
  EventMetrics,
  EventValidationResult,
  EventEmissionResult,
  LegacyEventInterface,
} from './eventDefinitions';
import { DEFAULT_EVENT_CONFIG } from './eventDefinitions';
import { eventStorage } from '../store/storage';

// =============================================================================
// ENHANCED PERKD EVENT EMITTER
// =============================================================================

/**
 * Enhanced event emitter that extends the existing functionality
 * while maintaining full backward compatibility
 */
export class PerkdEventEmitter extends EventEmitter implements TypedEventEmitter, LegacyEventInterface {
  private metrics: EventMetrics = {
    totalEvents: 0,
    eventsPerSecond: 0,
    listenerCount: 0,
    memoryUsage: 0,
    averageProcessingTime: 0,
    errorCount: 0,
    lastActivity: 0,
    eventsByType: {},
    thresholds: {
      maxListeners: 50,
      maxProcessingTime: 100,
      maxEventsPerSecond: 1000
    }
  };
  private reduxBridgeEnabled: boolean = false;
  private store?: any; // Redux store reference
  private config: typeof DEFAULT_EVENT_CONFIG;
  private startTime: number;

  constructor(config: Partial<typeof DEFAULT_EVENT_CONFIG> = {}) {
    super();

    // Set max listeners (Node.js EventEmitter method)
    this.setMaxListeners(config.maxListeners || 50);

    this.config = { ...DEFAULT_EVENT_CONFIG, ...config };
    this.startTime = Date.now();
    this.initializeMetrics();
    this.setupErrorHandling();
    this.setupPerformanceMonitoring();

    if (__DEV__) {
      console.log('🎯 Enhanced PerkdEventEmitter initialized');
    }
  }

  // =============================================================================
  // ENHANCED EVENT METHODS
  // =============================================================================

  /**
   * Type-safe event emission with performance monitoring
   */
  emit<K extends keyof TypedEvents>(event: K, data: TypedEvents[K]): boolean {
    return this.safeEmit(event, data);
  }

  /**
   * Safe event emission with error handling and metrics
   */
  safeEmit<K extends keyof TypedEvents>(event: K, data: TypedEvents[K]): boolean {
    const startTime = performance.now();
    
    try {
      // Validate event data if enabled
      if (this.config.enableValidation) {
        const validation = this.validateEventData(event as string, data);
        if (!validation.isValid && __DEV__) {
          console.warn(`🔶 Event validation warnings for ${String(event)}:`, validation.warnings);
        }
      }

      // Enhance event data with metadata
      const enhancedData = this.enhanceEventData(data);

      // Emit the event
      const result = super.emit(event as string, enhancedData);

      // Update metrics
      this.updateMetrics(event as string, startTime, true);

      // Log in development
      if (__DEV__ && this.config.enableMetrics) {
        const processingTime = performance.now() - startTime;
        if (processingTime > this.config.maxProcessingTime) {
          console.warn(`🐌 Slow event processing: ${String(event)} (${processingTime.toFixed(2)}ms)`);
        }
      }

      return result;

    } catch (error) {
      this.updateMetrics(event as string, startTime, false, error as Error);
      console.error(`❌ Event emission error for ${String(event)}:`, error);
      return false;
    }
  }

  /**
   * Type-safe event listening
   */
  on<K extends keyof TypedEvents>(
    event: K,
    listener: (data: TypedEvents[K]) => void,
    context?: any
  ): this {
    // Node.js EventEmitter doesn't support context parameter
    // If context is needed, bind the listener manually
    const boundListener = context ? listener.bind(context) : listener;
    super.on(event as string, boundListener);

    this.metrics.listenerCount++;
    return this;
  }

  /**
   * Type-safe one-time event listening
   */
  once<K extends keyof TypedEvents>(
    event: K,
    listener: (data: TypedEvents[K]) => void,
    context?: any
  ): this {
    // Node.js EventEmitter doesn't support context parameter
    // If context is needed, bind the listener manually
    const boundListener = context ? listener.bind(context) : listener;
    super.once(event as string, boundListener);

    this.metrics.listenerCount++;
    return this;
  }

  /**
   * Remove event listener
   */
  removeListener<K extends keyof TypedEvents>(
    event: K,
    listener: (data: TypedEvents[K]) => void,
    context?: any
  ): this {
    // Node.js EventEmitter doesn't support context parameter
    // If context was used, the bound listener should be passed here
    const boundListener = context ? listener.bind(context) : listener;
    super.removeListener(event as string, boundListener);

    this.metrics.listenerCount = Math.max(0, this.metrics.listenerCount - 1);
    return this;
  }

  /**
   * Remove all listeners for an event
   */
  removeAllListeners(event?: keyof TypedEvents): this {
    if (event) {
      const currentListenerCount = super.listenerCount(event as string);
      super.removeAllListeners(event as string);
      this.metrics.listenerCount = Math.max(0, this.metrics.listenerCount - currentListenerCount);
    } else {
      super.removeAllListeners();
      this.metrics.listenerCount = 0;
    }

    return this;
  }

  // =============================================================================
  // PERFORMANCE MONITORING
  // =============================================================================

  /**
   * Get comprehensive event metrics
   */
  getMetrics(): EventMetrics {
    const now = Date.now();
    const uptime = (now - this.startTime) / 1000; // seconds
    
    return {
      ...this.metrics,
      eventsPerSecond: this.metrics.totalEvents / uptime,
      memoryUsage: this.getMemoryUsage(),
      thresholds: {
        maxListeners: this.config.maxListeners,
        maxProcessingTime: this.config.maxProcessingTime,
        maxEventsPerSecond: this.config.maxEventsPerSecond,
      },
    };
  }

  /**
   * Reset metrics
   */
  resetMetrics(): void {
    this.initializeMetrics();
    this.startTime = Date.now();
  }

  // =============================================================================
  // REDUX BRIDGE INTEGRATION
  // =============================================================================

  /**
   * Enable Redux bridge integration
   */
  enableReduxBridge(store?: any): void {
    this.reduxBridgeEnabled = true;
    if (store) {
      this.store = store;
    }
    
    if (__DEV__) {
      console.log('🔗 Redux bridge enabled for global event emitter');
    }
  }

  /**
   * Disable Redux bridge integration
   */
  disableReduxBridge(): void {
    this.reduxBridgeEnabled = false;
    this.store = undefined;
    
    if (__DEV__) {
      console.log('🔗 Redux bridge disabled for global event emitter');
    }
  }

  /**
   * Check if Redux bridge is enabled
   */
  isReduxBridgeEnabled(): boolean {
    return this.reduxBridgeEnabled && !!this.store;
  }

  // =============================================================================
  // PRIVATE METHODS
  // =============================================================================

  private initializeMetrics(): void {
    this.metrics = {
      totalEvents: 0,
      eventsPerSecond: 0,
      listenerCount: 0,
      memoryUsage: 0,
      averageProcessingTime: 0,
      errorCount: 0,
      lastActivity: 0,
      eventsByType: {},
      thresholds: {
        maxListeners: this.config.maxListeners,
        maxProcessingTime: this.config.maxProcessingTime,
        maxEventsPerSecond: this.config.maxEventsPerSecond,
      },
    };
  }

  private setupErrorHandling(): void {
    this.on('error', (error) => {
      console.error('🚨 Global event emitter error:', error);
      this.metrics.errorCount++;
    });
  }

  private setupPerformanceMonitoring(): void {
    if (!this.config.enableMetrics) return;

    // Monitor listener count
    this.on('newListener', () => {
      if (this.metrics.listenerCount > this.config.maxListeners * 0.8) {
        console.warn(`🔶 High listener count: ${this.metrics.listenerCount}/${this.config.maxListeners}`);
      }
    });

    // Periodic metrics persistence
    if (typeof setInterval !== 'undefined') {
      setInterval(() => {
        this.persistMetrics();
      }, 60000); // Every minute
    }
  }

  private enhanceEventData(data: any): any {
    return {
      ...data,
      timestamp: data.timestamp || new Date().toISOString(),
      source: data.source || 'system',
      _enhanced: true,
      _emittedAt: Date.now(),
    };
  }

  private validateEventData(event: string, data: any): EventValidationResult {
    const result: EventValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    // Basic validation
    if (!event || typeof event !== 'string') {
      result.isValid = false;
      result.errors.push('Event name must be a non-empty string');
    }

    if (data && typeof data !== 'object') {
      result.warnings.push('Event data should be an object');
    }

    return result;
  }

  private updateMetrics(event: string, startTime: number, success: boolean, error?: Error): void {
    const processingTime = performance.now() - startTime;
    
    this.metrics.totalEvents++;
    this.metrics.lastActivity = Date.now();
    
    // Update average processing time
    this.metrics.averageProcessingTime = 
      (this.metrics.averageProcessingTime + processingTime) / 2;
    
    // Update event type counts
    this.metrics.eventsByType[event] = (this.metrics.eventsByType[event] || 0) + 1;
    
    if (!success) {
      this.metrics.errorCount++;
    }
  }

  private getMemoryUsage(): number {
    // Estimate memory usage based on listener count and event history
    const baseMemory = 1024; // Base memory in bytes
    const listenerMemory = this.metrics.listenerCount * 100; // Estimate per listener
    const eventHistoryMemory = Object.keys(this.metrics.eventsByType).length * 50;
    
    return baseMemory + listenerMemory + eventHistoryMemory;
  }

  private persistMetrics(): void {
    try {
      eventStorage.set('global-emitter-metrics', JSON.stringify(this.getMetrics()));
    } catch (error) {
      if (__DEV__) {
        console.warn('Failed to persist event metrics:', error);
      }
    }
  }
}

// =============================================================================
// GLOBAL INSTANCE
// =============================================================================

/**
 * Global enhanced event emitter instance
 * This will replace the existing $.Event when integrated
 */
export const globalEventEmitter = new PerkdEventEmitter();

// =============================================================================
// LEGACY COMPATIBILITY WRAPPER
// =============================================================================

/**
 * Legacy compatibility wrapper for existing $.Event interface
 * Ensures all existing code continues to work without changes
 */
export class LegacyEventWrapper {
  private emitter: PerkdEventEmitter;

  constructor(emitter: PerkdEventEmitter) {
    this.emitter = emitter;
  }

  // Legacy methods that match the existing Emitter class interface
  emit(event: string, data?: any): boolean {
    return this.emitter.safeEmit(event, data || {});
  }

  on(event: string, listener: Function, context?: any): this {
    this.emitter.on(event, listener as any, context);
    return this;
  }

  once(event: string, listener: Function, context?: any): this {
    this.emitter.once(event, listener as any, context);
    return this;
  }

  removeListener(event: string, listener: Function, context?: any): this {
    this.emitter.removeListener(event, listener as any, context);
    return this;
  }

  removeAllListeners(event?: string): this {
    this.emitter.removeAllListeners(event);
    return this;
  }
}

/**
 * Create legacy-compatible wrapper for global use
 */
export const legacyEventWrapper = new LegacyEventWrapper(globalEventEmitter);
