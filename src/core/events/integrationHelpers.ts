/**
 * Perkd v7.0 Event System Integration Helpers
 * 
 * Manages the transition from legacy event system to enhanced system
 * Provides utilities for gradual migration and compatibility
 */

import { globalEventEmitter, legacyEventWrapper, PerkdEventEmitter } from './globalEventEmitter';
import type { EventMetrics, BridgeEventConfig } from './eventDefinitions';
import { eventBridgeStorage } from '../store/storage';

// =============================================================================
// EVENT SYSTEM MANAGER
// =============================================================================

/**
 * Manages the enhanced event system integration
 */
export class EventSystemManager {
  private static instance: EventSystemManager;
  private isInitialized = false;
  private reduxStore?: any;
  private legacyMode = true; // Start in legacy mode for compatibility

  private constructor() {}

  static getInstance(): EventSystemManager {
    if (!EventSystemManager.instance) {
      EventSystemManager.instance = new EventSystemManager();
    }
    return EventSystemManager.instance;
  }

  // =============================================================================
  // INITIALIZATION
  // =============================================================================

  /**
   * Initialize the enhanced event system
   */
  async initialize(options: {
    preserveLegacy?: boolean;
    enableReduxIntegration?: boolean;
    store?: any;
  } = {}): Promise<void> {
    if (this.isInitialized) {
      console.log('🎯 Event system already initialized');
      return;
    }

    try {
      console.log('🎯 Initializing enhanced event system...');

      const { preserveLegacy = true, enableReduxIntegration = true, store } = options;

      // Store Redux reference if provided
      if (store) {
        this.reduxStore = store;
      }

      // Enable Redux integration if requested
      if (enableReduxIntegration && store) {
        this.enableReduxIntegration(store);
      }

      // Set up legacy compatibility if requested
      if (preserveLegacy) {
        this.setupLegacyCompatibility();
      }

      // Load persisted configuration
      await this.loadPersistedConfig();

      this.isInitialized = true;
      console.log('✅ Enhanced event system initialized');
      console.log(`🔗 Legacy mode: ${this.legacyMode ? 'Enabled' : 'Disabled'}`);
      console.log(`🔗 Redux integration: ${globalEventEmitter.isReduxBridgeEnabled() ? 'Enabled' : 'Disabled'}`);

    } catch (error) {
      console.error('❌ Failed to initialize enhanced event system:', error);
      throw error;
    }
  }

  /**
   * Enable Redux integration
   */
  enableReduxIntegration(store: any): void {
    this.reduxStore = store;
    globalEventEmitter.enableReduxBridge(store);
    
    console.log('🔗 Redux integration enabled for event system');
  }

  /**
   * Disable Redux integration
   */
  disableReduxIntegration(): void {
    globalEventEmitter.disableReduxBridge();
    this.reduxStore = undefined;
    
    console.log('🔗 Redux integration disabled for event system');
  }

  // =============================================================================
  // LEGACY COMPATIBILITY
  // =============================================================================

  /**
   * Set up legacy compatibility mode
   * This ensures existing $.Event usage continues to work
   */
  private setupLegacyCompatibility(): void {
    // Check if global $.Event already exists
    if (typeof global !== 'undefined' && global.$ && global.$.Event) {
      console.log('🔄 Enhancing existing $.Event with new capabilities...');
      
      // Preserve existing event listeners by migrating them
      this.migrateLegacyListeners(global.$.Event);
      
      // Replace with enhanced wrapper while preserving interface
      global.$.Event = legacyEventWrapper;
      
    } else {
      console.log('🆕 Setting up new $.Event interface...');
      
      // Set up new global interface
      if (typeof global !== 'undefined') {
        global.$ = global.$ || {};
        global.$.Event = legacyEventWrapper;
      }
    }

    this.legacyMode = true;
  }

  /**
   * Migrate existing event listeners to enhanced system
   */
  private migrateLegacyListeners(legacyEmitter: any): void {
    try {
      // This is a best-effort migration
      // The exact implementation depends on the legacy emitter structure
      if (legacyEmitter && legacyEmitter.listeners) {
        console.log('🔄 Migrating legacy event listeners...');
        
        // Migrate listeners if possible
        // Note: This is a simplified migration - actual implementation
        // would need to handle the specific legacy emitter structure
        
        console.log('✅ Legacy listeners migration completed');
      }
    } catch (error) {
      console.warn('⚠️ Could not migrate all legacy listeners:', error);
    }
  }

  /**
   * Switch to enhanced mode (disable legacy compatibility)
   */
  switchToEnhancedMode(): void {
    this.legacyMode = false;
    
    // Replace global interface with direct enhanced emitter
    if (typeof global !== 'undefined' && global.$) {
      global.$.Event = globalEventEmitter;
    }
    
    console.log('🚀 Switched to enhanced event mode');
  }

  /**
   * Switch back to legacy compatibility mode
   */
  switchToLegacyMode(): void {
    this.legacyMode = true;
    
    // Replace global interface with legacy wrapper
    if (typeof global !== 'undefined' && global.$) {
      global.$.Event = legacyEventWrapper;
    }
    
    console.log('🔄 Switched to legacy compatibility mode');
  }

  // =============================================================================
  // CONFIGURATION MANAGEMENT
  // =============================================================================

  /**
   * Load persisted configuration
   */
  private async loadPersistedConfig(): Promise<void> {
    try {
      const config = eventBridgeStorage.getBridgeConfig();
      if (config) {
        console.log('📋 Loaded persisted event system configuration');
      }
    } catch (error) {
      console.warn('⚠️ Could not load persisted configuration:', error);
    }
  }

  /**
   * Save current configuration
   */
  saveConfiguration(): void {
    try {
      const config = {
        legacyMode: this.legacyMode,
        reduxIntegrationEnabled: globalEventEmitter.isReduxBridgeEnabled(),
        timestamp: Date.now(),
      };
      
      eventBridgeStorage.setBridgeConfig(config);
      console.log('💾 Event system configuration saved');
      
    } catch (error) {
      console.warn('⚠️ Could not save configuration:', error);
    }
  }

  // =============================================================================
  // MONITORING AND HEALTH
  // =============================================================================

  /**
   * Get comprehensive system health status
   */
  getSystemHealth(): {
    isInitialized: boolean;
    legacyMode: boolean;
    reduxIntegration: boolean;
    metrics: EventMetrics;
    issues: string[];
    recommendations: string[];
  } {
    const metrics = globalEventEmitter.getMetrics();
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for potential issues
    if (metrics.errorCount > 10) {
      issues.push('High error count detected');
      recommendations.push('Check event emission patterns and error handling');
    }

    if (metrics.listenerCount > metrics.thresholds.maxListeners * 0.8) {
      issues.push('High listener count approaching limit');
      recommendations.push('Review event listener cleanup and memory management');
    }

    if (metrics.averageProcessingTime > metrics.thresholds.maxProcessingTime) {
      issues.push('Slow event processing detected');
      recommendations.push('Optimize event handlers and consider debouncing');
    }

    return {
      isInitialized: this.isInitialized,
      legacyMode: this.legacyMode,
      reduxIntegration: globalEventEmitter.isReduxBridgeEnabled(),
      metrics,
      issues,
      recommendations,
    };
  }

  /**
   * Get system statistics
   */
  getSystemStatistics(): {
    uptime: number;
    totalEvents: number;
    activeListeners: number;
    memoryUsage: number;
    errorRate: number;
    topEvents: Array<{ event: string; count: number }>;
  } {
    const metrics = globalEventEmitter.getMetrics();
    const now = Date.now();
    
    // Calculate top events
    const topEvents = Object.entries(metrics.eventsByType)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([event, count]) => ({ event, count }));

    return {
      uptime: now - (metrics.lastActivity || now),
      totalEvents: metrics.totalEvents,
      activeListeners: metrics.listenerCount,
      memoryUsage: metrics.memoryUsage,
      errorRate: metrics.totalEvents > 0 ? metrics.errorCount / metrics.totalEvents : 0,
      topEvents,
    };
  }

  // =============================================================================
  // UTILITIES
  // =============================================================================

  /**
   * Get the current event emitter instance
   */
  getEventEmitter(): PerkdEventEmitter {
    return globalEventEmitter;
  }

  /**
   * Get the legacy wrapper instance
   */
  getLegacyWrapper(): any {
    return legacyEventWrapper;
  }

  /**
   * Check if system is initialized
   */
  isSystemInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Check if legacy mode is enabled
   */
  isLegacyMode(): boolean {
    return this.legacyMode;
  }

  /**
   * Reset the entire event system
   */
  reset(): void {
    globalEventEmitter.removeAllListeners();
    globalEventEmitter.resetMetrics();
    this.isInitialized = false;
    this.legacyMode = true;
    
    console.log('🔄 Event system reset completed');
  }

  /**
   * Cleanup and shutdown
   */
  shutdown(): void {
    this.saveConfiguration();
    globalEventEmitter.removeAllListeners();
    this.disableReduxIntegration();
    this.isInitialized = false;
    
    console.log('🛑 Event system shutdown completed');
  }
}

// =============================================================================
// CONVENIENCE EXPORTS
// =============================================================================

/**
 * Get the singleton event system manager
 */
export const eventSystemManager = EventSystemManager.getInstance();

/**
 * Quick access to the global event emitter
 */
export { globalEventEmitter };

/**
 * Quick access to the legacy wrapper
 */
export { legacyEventWrapper };
