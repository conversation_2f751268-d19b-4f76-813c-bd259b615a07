import { ServiceResponse, SyncOperation } from '../store/types/common';

export abstract class BaseService {
  protected handleError(error: unknown): never {
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('An unknown error occurred');
  }

  protected validateId(id: string, entityName: string): void {
    if (!id || typeof id !== 'string') {
      throw new Error(`Invalid ${entityName} ID: ${id}`);
    }
  }

  protected createResponse<T>(data: T, success: boolean = true, message?: string): ServiceResponse<T> {
    return {
      data,
      success,
      message,
      timestamp: new Date().toISOString(),
    };
  }
}

// Sync Queue Management (placeholder for now)
export class SyncQueue {
  private static operations: SyncOperation[] = [];

  static async add(type: SyncOperation['type'], data: any): Promise<void> {
    const operation: SyncOperation = {
      id: this.generateId(),
      type,
      data,
      timestamp: new Date().toISOString(),
      retryCount: 0,
      status: 'pending',
    };

    this.operations.push(operation);
    console.log('Added to sync queue:', operation);
  }

  static async getPending(): Promise<SyncOperation[]> {
    return this.operations.filter(op => op.status === 'pending');
  }

  static async markComplete(id: string): Promise<void> {
    const operation = this.operations.find(op => op.id === id);
    if (operation) {
      operation.status = 'completed';
    }
  }

  static async markFailed(id: string, error: any): Promise<void> {
    const operation = this.operations.find(op => op.id === id);
    if (operation) {
      operation.status = 'failed';
      operation.retryCount++;
      console.error('Sync operation failed:', operation, error);
    }
  }

  private static generateId(): string {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export default BaseService;
