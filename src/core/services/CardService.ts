/**
 * Card Service - Business Logic Layer
 *
 * Handles all card-related business operations, validation, and data access
 * Updated to work with Realm-first architecture and new Redux patterns
 */

import { DatabaseManager } from '../database/config';
import { Card } from '../../data/models/Card';
import { BaseService, SyncQueue } from './BaseService';
import {
  CreateCardData,
  UpdateCardData,
  CardQueryOptions,
  CardDisplayInfo
} from '../store/types/card';

// Temporary: Remove event imports for testing
// import * as EVENT from '../../lib/Events.json';

// Legacy imports (will be removed in future phases)
// const { Cards } = require('../../../lib/cards');
// const LegacyCardService = require('../../../lib/common/services/card').default;

// Temporary event constants for testing
const EVENT = {
  Card: {
    register: 'card.register',
    accept: 'card.accept',
    decline: 'card.decline',
    update: 'card.update',
    delete: 'card.delete',
  }
};

// =============================================================================
// LEGACY COMPATIBILITY
// =============================================================================

// Note: We're using the actual Realm Card model imported above
// These legacy interfaces are kept for backward compatibility during migration

export interface UpdateCardData {
  display?: Partial<Card['display']>;
  credentials?: Partial<Card['credentials']>;
  features?: Partial<Card['features']>;
}

export interface CardQueryOptions {
  cardId?: string;
  personId?: string;
  masterId?: string;
  state?: string;
  limit?: number;
  offset?: number;
}

// =============================================================================
// CARD SERVICE
// =============================================================================

export class CardService extends BaseService {
  /**
   * Create a new card (Realm-first with optimistic updates)
   */
  static async createCard(data: CreateCardData): Promise<Card> {
    try {
      // Validation
      if (!data.masterId || !data.personId) {
        throw new Error('masterId and personId are required');
      }

      // 1. Create in Realm immediately (optimistic)
      const realm = DatabaseManager.getInstance();
      let realmCard: Card;

      await realm.write(() => {
        realmCard = realm.create('Card', {
          id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          masterId: data.masterId,
          personId: data.personId,
          displayName: data.displayName || 'New Card',
          state: 'pending',
          visible: true,
          createdAt: new Date(),
          options: data.options || {},
          formData: data.formData || {},
        });
      });

      // 2. Queue for background sync with server
      await SyncQueue.add('CREATE_CARD', {
        tempId: realmCard!.id,
        masterId: data.masterId,
        personId: data.personId,
        formData: data.formData,
        options: data.options,
      });

      // 3. Emit business event (for analytics, etc.)
      CardService.emitCardEvent(EVENT.Card.register, {
        id: realmCard!.id,
        masterId: data.masterId,
        source: 'service',
        timestamp: new Date().toISOString()
      });

      // 4. Return Realm object as plain object
      return realmCard!.toJSON();
    } catch (error) {
      console.error('CardService.createCard failed:', error);
      throw error;
    }
  }

  /**
   * Accept a pre-issued card (Realm-first with optimistic updates)
   */
  static async acceptCard(cardId: string, at?: Date, manual?: boolean): Promise<{ cardId: string; acceptedAt: Date }> {
    try {
      // 1. Find card in Realm
      const realm = DatabaseManager.getInstance();
      const card = realm.objectForPrimaryKey('Card', cardId);

      if (!card) {
        throw new Error(`Card with id ${cardId} not found`);
      }

      const acceptedAt = at || new Date();

      // 2. Update Realm immediately (optimistic)
      await realm.write(() => {
        (card as any).state = 'active';
        (card as any).activeTime = acceptedAt;
        (card as any).modifiedAt = new Date();
      });

      // 3. Queue for background sync with server
      await SyncQueue.add('ACCEPT_CARD', {
        cardId,
        acceptedAt: acceptedAt.toISOString(),
        manual: !!manual,
      });

      // 4. Emit business event
      CardService.emitCardEvent(EVENT.Card.accept, {
        cardId,
        acceptedAt: acceptedAt.toISOString(),
        manual: !!manual,
        source: 'service'
      });

      return { cardId, acceptedAt };
    } catch (error) {
      console.error('CardService.acceptCard failed:', error);
      throw error;
    }
  }

  /**
   * Decline a pre-issued card (Realm-first with optimistic updates)
   */
  static async declineCard(cardId: string, at?: Date): Promise<{ cardId: string; declinedAt: Date }> {
    try {
      // 1. Find card in Realm
      const realm = DatabaseManager.getInstance();
      const card = realm.objectForPrimaryKey('Card', cardId);

      if (!card) {
        throw new Error(`Card with id ${cardId} not found`);
      }

      const declinedAt = at || new Date();

      // 2. Update Realm immediately (optimistic)
      await realm.write(() => {
        (card as any).state = 'declined';
        (card as any).modifiedAt = declinedAt;
      });

      // 3. Queue for background sync with server
      await SyncQueue.add('DECLINE_CARD', {
        cardId,
        declinedAt: declinedAt.toISOString(),
      });

      // 4. Emit business event
      CardService.emitCardEvent(EVENT.Card.decline, {
        cardId,
        declinedAt: declinedAt.toISOString(),
        source: 'service'
      });

      return { cardId, declinedAt };
    } catch (error) {
      console.error('CardService.declineCard failed:', error);
      throw error;
    }
  }

  /**
   * Update card information
   */
  static async updateCard(cardId: string, changes: UpdateCardData): Promise<Card> {
    try {
      // Find card
      const card = global._ ? global._.Card.findById(cardId) : null;
      if (!card) {
        throw new Error(`Card with id ${cardId} not found`);
      }

      // Apply business rules and validation
      if (changes.display) {
        Object.assign(card.display, changes.display);
      }
      if (changes.credentials) {
        Object.assign(card.credentials, changes.credentials);
      }
      if (changes.features) {
        Object.assign(card.features, changes.features);
      }

      // Save changes
      await card.save();

      // Emit business event
      CardService.emitCardEvent(EVENT.Card.update, {
        cardId,
        changes,
        source: 'service',
        timestamp: new Date().toISOString()
      });

      return card;
    } catch (error) {
      console.error('CardService.updateCard failed:', error);
      throw error;
    }
  }

  /**
   * Delete a card
   */
  static async deleteCard(cardId: string): Promise<void> {
    try {
      // Find card
      const card = global._ ? global._.Card.findById(cardId) : null;
      if (!card) {
        throw new Error(`Card with id ${cardId} not found`);
      }

      // Business logic: Delete the card
      await card.delete();

      // Emit business event
      CardService.emitCardEvent(EVENT.Card.delete, {
        cardId,
        source: 'service',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('CardService.deleteCard failed:', error);
      throw error;
    }
  }

  /**
   * Get cards with filtering
   */
  static async getCards(options: CardQueryOptions = {}): Promise<Card[]> {
    try {
      // Use legacy system for data access
      let cardsResult = global._ ? global._.Card.findAll() : [];

      // Handle mock object vs real array
      let cards: Card[] = Array.isArray(cardsResult) ? cardsResult : [];

      // Apply filters
      if (options.cardId) {
        cards = cards.filter((card: Card) => card.id === options.cardId);
      }
      if (options.personId) {
        cards = cards.filter((card: Card) => card.personId === options.personId);
      }
      if (options.masterId) {
        cards = cards.filter((card: Card) => card.masterId === options.masterId);
      }
      if (options.state) {
        cards = cards.filter((card: Card) => card.lifecycle.state === options.state);
      }

      // Apply pagination
      if (options.offset) {
        cards = cards.slice(options.offset);
      }
      if (options.limit) {
        cards = cards.slice(0, options.limit);
      }

      return cards;
    } catch (error) {
      console.error('CardService.getCards failed:', error);
      throw error;
    }
  }

  /**
   * Get a single card by ID
   */
  static async getCard(cardId: string): Promise<Card | null> {
    try {
      const card = global._ ? global._.Card.findById(cardId) : null;
      return card;
    } catch (error) {
      console.error('CardService.getCard failed:', error);
      throw error;
    }
  }

  /**
   * Get active cards for a person
   */
  static getActiveCards(personId: string, limit?: number): Card[] {
    try {
      let cardsResult = global._ ? global._.Card.findAll() : [];

      // Handle mock object vs real array
      let cards: Card[] = Array.isArray(cardsResult) ? cardsResult : [];

      // Filter for active cards for this person
      cards = cards.filter((card: Card) =>
        card.personId === personId &&
        card.lifecycle.state === 'active'
      );

      // Apply limit
      if (limit) {
        cards = cards.slice(0, limit);
      }

      return cards;
    } catch (error) {
      console.error('CardService.getActiveCards failed:', error);
      return [];
    }
  }

  // =============================================================================
  // PRIVATE UTILITIES
  // =============================================================================

  /**
   * Emit card-related business events
   */
  private static emitCardEvent(eventType: string, eventData: any): void {
    try {
      if (typeof global !== 'undefined' && global.$ && global.$.Event) {
        global.$.Event.emit(eventType, eventData);
      }
    } catch (error) {
      console.error('Failed to emit card event:', error);
    }
  }
}
