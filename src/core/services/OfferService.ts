/**
 * Offer Service - Business Logic Layer
 *
 * Handles all offer-related business operations, validation, and data access
 * Updated to work with Realm-first architecture and new Redux patterns
 */

import { DatabaseManager } from '../database/config';
import { Offer } from '../../data/models/Offer';
import { BaseService, SyncQueue } from './BaseService';
import {
  CreateOfferData,
  UpdateOfferData,
  RedeemOfferData,
  OfferDisplayInfo
} from '../store/types/offer';

// Temporary event constants for testing
const EVENT = {
  Offer: {
    create: 'offer.create',
    update: 'offer.update',
    redeem: 'offer.redeem',
    expire: 'offer.expire',
    delete: 'offer.delete',
  }
};

// =============================================================================
// TYPES
// =============================================================================

export interface Offer {
  id: string;
  masterId: string;
  personId?: string;
  cardId?: string;
  
  content: {
    title: string;
    description: string;
    terms?: string;
    imageUrl?: string;
    category: string;
  };
  
  value: {
    type: 'percentage' | 'fixed' | 'points' | 'freebie';
    amount?: number;
    currency?: string;
    originalPrice?: number;
    discountedPrice?: number;
  };
  
  validity: {
    startDate: Date;
    endDate: Date;
    timezone: string;
    usageLimit?: number;
    usageCount: number;
  };
  
  targeting: {
    isPersonalized: boolean;
    audienceSegments: string[];
    geofencing?: {
      latitude: number;
      longitude: number;
      radius: number;
    };
  };
  
  lifecycle: {
    state: 'draft' | 'active' | 'paused' | 'expired' | 'redeemed';
    createdAt: Date;
    publishedAt?: Date;
    redeemedAt?: Date;
    lastModified: Date;
  };
  
  metadata: {
    source: string;
    version: number;
    tags: string[];
  };
}

export interface CreateOfferData {
  masterId: string;
  personId?: string;
  cardId?: string;
  content: Offer['content'];
  value: Offer['value'];
  validity: Omit<Offer['validity'], 'usageCount'>;
  targeting?: Partial<Offer['targeting']>;
}

export interface UpdateOfferData {
  content?: Partial<Offer['content']>;
  value?: Partial<Offer['value']>;
  validity?: Partial<Offer['validity']>;
  targeting?: Partial<Offer['targeting']>;
}

export interface OfferQueryOptions {
  personId?: string;
  cardId?: string;
  masterId?: string;
  state?: Offer['lifecycle']['state'];
  category?: string;
  isActive?: boolean;
  limit?: number;
  offset?: number;
}

// =============================================================================
// OFFER SERVICE
// =============================================================================

export class OfferService {
  /**
   * Create a new offer
   */
  static async createOffer(data: CreateOfferData): Promise<Offer> {
    try {
      // Validation
      if (!data.masterId) {
        throw new Error('masterId is required');
      }
      if (!data.content.title || !data.content.description) {
        throw new Error('title and description are required');
      }
      if (data.validity.endDate <= data.validity.startDate) {
        throw new Error('endDate must be after startDate');
      }

      // Create offer object
      const offer: Offer = {
        id: OfferService.generateId(),
        masterId: data.masterId,
        personId: data.personId,
        cardId: data.cardId,
        content: data.content,
        value: data.value,
        validity: {
          ...data.validity,
          usageCount: 0,
        },
        targeting: {
          isPersonalized: !!data.personId,
          audienceSegments: [],
          ...data.targeting,
        },
        lifecycle: {
          state: 'draft',
          createdAt: new Date(),
          lastModified: new Date(),
        },
        metadata: {
          source: 'service',
          version: 1,
          tags: [],
        },
      };

      // Save to database (using legacy system for now)
      await OfferService.saveToDatabase(offer);

      // Emit business event
      OfferService.emitOfferEvent(EVENT.Offer.create, {
        id: offer.id,
        masterId: data.masterId,
        source: 'service',
        timestamp: new Date().toISOString()
      });

      return offer;
    } catch (error) {
      console.error('OfferService.createOffer failed:', error);
      throw error;
    }
  }

  /**
   * Update an existing offer
   */
  static async updateOffer(offerId: string, changes: UpdateOfferData): Promise<Offer> {
    try {
      // Find existing offer
      const offer = await OfferService.getOffer(offerId);
      if (!offer) {
        throw new Error(`Offer with id ${offerId} not found`);
      }

      // Apply changes
      if (changes.content) {
        Object.assign(offer.content, changes.content);
      }
      if (changes.value) {
        Object.assign(offer.value, changes.value);
      }
      if (changes.validity) {
        Object.assign(offer.validity, changes.validity);
      }
      if (changes.targeting) {
        Object.assign(offer.targeting, changes.targeting);
      }

      // Update metadata
      offer.lifecycle.lastModified = new Date();
      offer.metadata.version += 1;

      // Save changes
      await OfferService.saveToDatabase(offer);

      // Emit business event
      OfferService.emitOfferEvent(EVENT.Offer.update, {
        offerId,
        changes,
        source: 'service',
        timestamp: new Date().toISOString()
      });

      return offer;
    } catch (error) {
      console.error('OfferService.updateOffer failed:', error);
      throw error;
    }
  }

  /**
   * Redeem an offer
   */
  static async redeemOffer(offerId: string, personId: string): Promise<{ offerId: string; redeemedAt: Date }> {
    try {
      const offer = await OfferService.getOffer(offerId);
      if (!offer) {
        throw new Error(`Offer with id ${offerId} not found`);
      }

      // Business rules validation
      if (offer.lifecycle.state !== 'active') {
        throw new Error('Offer is not active');
      }
      if (offer.validity.endDate < new Date()) {
        throw new Error('Offer has expired');
      }
      if (offer.validity.usageLimit && offer.validity.usageCount >= offer.validity.usageLimit) {
        throw new Error('Offer usage limit reached');
      }
      if (offer.targeting.isPersonalized && offer.personId !== personId) {
        throw new Error('Offer is not available for this person');
      }

      // Redeem the offer
      offer.validity.usageCount += 1;
      offer.lifecycle.redeemedAt = new Date();
      offer.lifecycle.lastModified = new Date();
      
      // Check if offer should be marked as redeemed
      if (offer.validity.usageLimit && offer.validity.usageCount >= offer.validity.usageLimit) {
        offer.lifecycle.state = 'redeemed';
      }

      // Save changes
      await OfferService.saveToDatabase(offer);

      const redeemedAt = offer.lifecycle.redeemedAt;

      // Emit business event
      OfferService.emitOfferEvent(EVENT.Offer.redeem, {
        offerId,
        personId,
        redeemedAt: redeemedAt.toISOString(),
        source: 'service'
      });

      return { offerId, redeemedAt };
    } catch (error) {
      console.error('OfferService.redeemOffer failed:', error);
      throw error;
    }
  }

  /**
   * Delete an offer
   */
  static async deleteOffer(offerId: string): Promise<void> {
    try {
      const offer = await OfferService.getOffer(offerId);
      if (!offer) {
        throw new Error(`Offer with id ${offerId} not found`);
      }

      // Business logic: Delete the offer
      await OfferService.deleteFromDatabase(offerId);

      // Emit business event
      OfferService.emitOfferEvent(EVENT.Offer.delete, {
        offerId,
        source: 'service',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('OfferService.deleteOffer failed:', error);
      throw error;
    }
  }

  /**
   * Get offers with filtering
   */
  static async getOffers(options: OfferQueryOptions = {}): Promise<Offer[]> {
    try {
      // Use legacy system for data access (placeholder)
      let offers = await OfferService.loadFromDatabase();

      // Apply filters
      if (options.personId) {
        offers = offers.filter(offer => 
          !offer.targeting.isPersonalized || offer.personId === options.personId
        );
      }
      if (options.cardId) {
        offers = offers.filter(offer => offer.cardId === options.cardId);
      }
      if (options.masterId) {
        offers = offers.filter(offer => offer.masterId === options.masterId);
      }
      if (options.state) {
        offers = offers.filter(offer => offer.lifecycle.state === options.state);
      }
      if (options.category) {
        offers = offers.filter(offer => offer.content.category === options.category);
      }
      if (options.isActive) {
        const now = new Date();
        offers = offers.filter(offer => 
          offer.lifecycle.state === 'active' &&
          offer.validity.startDate <= now &&
          offer.validity.endDate > now
        );
      }

      // Apply pagination
      if (options.offset) {
        offers = offers.slice(options.offset);
      }
      if (options.limit) {
        offers = offers.slice(0, options.limit);
      }

      return offers;
    } catch (error) {
      console.error('OfferService.getOffers failed:', error);
      throw error;
    }
  }

  /**
   * Get a single offer by ID
   */
  static async getOffer(offerId: string): Promise<Offer | null> {
    try {
      // Placeholder for database access
      const offers = await OfferService.loadFromDatabase();
      return offers.find(offer => offer.id === offerId) || null;
    } catch (error) {
      console.error('OfferService.getOffer failed:', error);
      throw error;
    }
  }

  // =============================================================================
  // PRIVATE UTILITIES
  // =============================================================================

  private static generateId(): string {
    return `offer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static async saveToDatabase(offer: Offer): Promise<void> {
    // Placeholder for database save operation
    console.log('Saving offer to database:', offer.id);
  }

  private static async deleteFromDatabase(offerId: string): Promise<void> {
    // Placeholder for database delete operation
    console.log('Deleting offer from database:', offerId);
  }

  private static async loadFromDatabase(): Promise<Offer[]> {
    // Placeholder for database load operation
    return [];
  }

  private static emitOfferEvent(eventType: string, eventData: any): void {
    try {
      if (typeof global !== 'undefined' && global.$ && global.$.Event) {
        global.$.Event.emit(eventType, eventData);
      }
    } catch (error) {
      console.error('Failed to emit offer event:', error);
    }
  }
}
