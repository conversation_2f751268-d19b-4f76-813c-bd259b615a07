/**
 * Person Service - Business Logic Layer
 * 
 * Handles all person-related business operations, validation, and data access
 * Separated from Redux state management for better testability and reusability
 */

import { DatabaseManager } from '../database/config';

// Import events from JSON file
import * as EVENT from '../../lib/Events.json';

// =============================================================================
// TYPES
// =============================================================================

export interface Person {
  id: string;
  profile: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    dateOfBirth?: Date;
    avatar?: string;
    gender?: string;
    fullName?: string;
    profileImage?: {
      uri?: string;
      url?: string;
      type?: 'camera' | 'library';
      width?: number;
      height?: number;
      size?: number;
    };
  };
  
  preferences: {
    language: string;
    timezone: string;
    currency: string;
    theme: 'light' | 'dark' | 'auto';
    biometrics: boolean;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
    privacy: {
      shareData: boolean;
      analytics: boolean;
      marketing: boolean;
    };
  };
  
  authentication: {
    isAuthenticated: boolean;
    isVerified: boolean;
    verificationMethod: 'email' | 'phone' | 'both' | 'none';
    lastLogin?: Date;
    loginCount: number;
  };
  
  lifecycle: {
    state: 'active' | 'inactive' | 'suspended' | 'deleted';
    createdAt: Date;
    lastModified: Date;
    lastActivity?: Date;
  };
  
  metadata: {
    source: string;
    version: number;
    tags: string[];
  };
}

export interface CreatePersonData {
  profile: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    dateOfBirth?: Date;
  };
  preferences?: Partial<Person['preferences']>;
}

export interface UpdatePersonData {
  profile?: Partial<Person['profile']>;
  preferences?: Partial<Person['preferences']>;
}

export interface PersonQueryOptions {
  email?: string;
  phone?: string;
  state?: Person['lifecycle']['state'];
  isVerified?: boolean;
  limit?: number;
  offset?: number;
}

// =============================================================================
// PERSON SERVICE
// =============================================================================

export class PersonService {
  /**
   * Create a new person
   */
  static async createPerson(data: CreatePersonData): Promise<Person> {
    try {
      // Validation
      if (!data.profile.firstName || !data.profile.lastName) {
        throw new Error('firstName and lastName are required');
      }
      if (!data.profile.email) {
        throw new Error('email is required');
      }
      if (!PersonService.isValidEmail(data.profile.email)) {
        throw new Error('Invalid email format');
      }

      // Check for existing person with same email
      const existingPerson = await PersonService.getPersonByEmail(data.profile.email);
      if (existingPerson) {
        throw new Error('Person with this email already exists');
      }

      // Create person object
      const person: Person = {
        id: PersonService.generateId(),
        profile: data.profile,
        preferences: {
          language: 'en',
          timezone: 'UTC',
          currency: 'USD',
          theme: 'auto',
          biometrics: false,
          notifications: {
            email: true,
            push: true,
            sms: false,
          },
          privacy: {
            shareData: false,
            analytics: true,
            marketing: false,
          },
          ...data.preferences,
        },
        authentication: {
          isAuthenticated: false,
          isVerified: false,
          verificationMethod: 'none',
          loginCount: 0,
        },
        lifecycle: {
          state: 'active',
          createdAt: new Date(),
          lastModified: new Date(),
        },
        metadata: {
          source: 'service',
          version: 1,
          tags: [],
        },
      };

      // Save to database
      await PersonService.saveToDatabase(person);

      // Emit business event
      PersonService.emitPersonEvent(EVENT.Person.create, {
        id: person.id,
        email: person.profile.email,
        source: 'service',
        timestamp: new Date().toISOString()
      });

      return person;
    } catch (error) {
      console.error('PersonService.createPerson failed:', error);
      throw error;
    }
  }

  /**
   * Update an existing person
   */
  static async updatePerson(personId: string, changes: UpdatePersonData): Promise<Person> {
    try {
      // Find existing person
      const person = await PersonService.getPerson(personId);
      if (!person) {
        throw new Error(`Person with id ${personId} not found`);
      }

      // Validate email if being changed
      if (changes.profile?.email && changes.profile.email !== person.profile.email) {
        if (!PersonService.isValidEmail(changes.profile.email)) {
          throw new Error('Invalid email format');
        }
        const existingPerson = await PersonService.getPersonByEmail(changes.profile.email);
        if (existingPerson && existingPerson.id !== personId) {
          throw new Error('Person with this email already exists');
        }
      }

      // Apply changes
      if (changes.profile) {
        Object.assign(person.profile, changes.profile);
      }
      if (changes.preferences) {
        Object.assign(person.preferences, changes.preferences);
      }

      // Update metadata
      person.lifecycle.lastModified = new Date();
      person.metadata.version += 1;

      // Save changes
      await PersonService.saveToDatabase(person);

      // Emit business event
      PersonService.emitPersonEvent(EVENT.Person.update, {
        personId,
        changes,
        source: 'service',
        timestamp: new Date().toISOString()
      });

      return person;
    } catch (error) {
      console.error('PersonService.updatePerson failed:', error);
      throw error;
    }
  }

  /**
   * Verify a person's identity
   */
  static async verifyPerson(personId: string, method: 'email' | 'phone' | 'both'): Promise<Person> {
    try {
      const person = await PersonService.getPerson(personId);
      if (!person) {
        throw new Error(`Person with id ${personId} not found`);
      }

      // Update verification status
      person.authentication.isVerified = true;
      person.authentication.verificationMethod = method;
      person.lifecycle.lastModified = new Date();
      person.metadata.version += 1;

      // Save changes
      await PersonService.saveToDatabase(person);

      // Emit business event
      PersonService.emitPersonEvent(EVENT.Person.verify, {
        personId,
        method,
        source: 'service',
        timestamp: new Date().toISOString()
      });

      return person;
    } catch (error) {
      console.error('PersonService.verifyPerson failed:', error);
      throw error;
    }
  }

  /**
   * Record a login for a person
   */
  static async recordLogin(personId: string): Promise<Person> {
    try {
      const person = await PersonService.getPerson(personId);
      if (!person) {
        throw new Error(`Person with id ${personId} not found`);
      }

      // Update login information
      person.authentication.lastLogin = new Date();
      person.authentication.loginCount += 1;
      person.lifecycle.lastActivity = new Date();
      person.lifecycle.lastModified = new Date();

      // Save changes
      await PersonService.saveToDatabase(person);

      // Emit business event
      PersonService.emitPersonEvent(EVENT.Person.login, {
        personId,
        timestamp: new Date().toISOString(),
        source: 'service'
      });

      return person;
    } catch (error) {
      console.error('PersonService.recordLogin failed:', error);
      throw error;
    }
  }

  /**
   * Get persons with filtering
   */
  static async getPersons(options: PersonQueryOptions = {}): Promise<Person[]> {
    try {
      // Use legacy system for data access (placeholder)
      let persons = await PersonService.loadFromDatabase();

      // Apply filters
      if (options.email) {
        persons = persons.filter(person => person.profile.email === options.email);
      }
      if (options.phone) {
        persons = persons.filter(person => person.profile.phone === options.phone);
      }
      if (options.state) {
        persons = persons.filter(person => person.lifecycle.state === options.state);
      }
      if (options.isVerified !== undefined) {
        persons = persons.filter(person => person.authentication.isVerified === options.isVerified);
      }

      // Apply pagination
      if (options.offset) {
        persons = persons.slice(options.offset);
      }
      if (options.limit) {
        persons = persons.slice(0, options.limit);
      }

      return persons;
    } catch (error) {
      console.error('PersonService.getPersons failed:', error);
      throw error;
    }
  }

  /**
   * Get a single person by ID
   */
  static async getPerson(personId: string): Promise<Person | null> {
    try {
      // Placeholder for database access
      const persons = await PersonService.loadFromDatabase();
      return persons.find(person => person.id === personId) || null;
    } catch (error) {
      console.error('PersonService.getPerson failed:', error);
      throw error;
    }
  }

  /**
   * Get a person by email
   */
  static async getPersonByEmail(email: string): Promise<Person | null> {
    try {
      const persons = await PersonService.loadFromDatabase();
      return persons.find(person => person.profile.email === email) || null;
    } catch (error) {
      console.error('PersonService.getPersonByEmail failed:', error);
      throw error;
    }
  }

  // =============================================================================
  // PRIVATE UTILITIES
  // =============================================================================

  private static generateId(): string {
    return `person_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private static async saveToDatabase(person: Person): Promise<void> {
    // Placeholder for database save operation
    console.log('Saving person to database:', person.id);
  }

  private static async loadFromDatabase(): Promise<Person[]> {
    // Placeholder for database load operation
    return [];
  }

  private static emitPersonEvent(eventType: string, eventData: any): void {
    try {
      if (typeof global !== 'undefined' && global.$ && global.$.Event) {
        global.$.Event.emit(eventType, eventData);
      }
    } catch (error) {
      console.error('Failed to emit person event:', error);
    }
  }
}
