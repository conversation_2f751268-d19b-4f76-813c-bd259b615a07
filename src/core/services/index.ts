/**
 * Service Layer Index
 * 
 * Centralized export of all business logic services
 * Clean separation of concerns following the service layer pattern
 */

// Export all services
export { CardService } from './CardService';
export { OfferService } from './OfferService';
export { PersonService } from './PersonService';

// Export types for convenience
export type { 
  Card, 
  CreateCardData, 
  UpdateCardData, 
  CardQueryOptions 
} from './CardService';

export type { 
  Offer, 
  CreateOfferData, 
  UpdateOfferData, 
  OfferQueryOptions 
} from './OfferService';

export type { 
  Person, 
  CreatePersonData, 
  UpdatePersonData, 
  PersonQueryOptions 
} from './PersonService';

// Service layer utilities
export class ServiceLayer {
  /**
   * Initialize all services
   */
  static async initialize(): Promise<void> {
    console.log('🔧 Service layer initialized');
    // Add any global service initialization here
  }

  /**
   * Cleanup all services
   */
  static async cleanup(): Promise<void> {
    console.log('🧹 Service layer cleaned up');
    // Add any global service cleanup here
  }

  /**
   * Get service health status
   */
  static getHealthStatus() {
    return {
      isHealthy: true,
      services: {
        CardService: 'active',
        OfferService: 'active',
        PersonService: 'active',
      },
      timestamp: new Date().toISOString(),
    };
  }
}
