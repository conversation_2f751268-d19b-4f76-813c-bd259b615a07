import { store, initializeStore, getStoreStatistics } from '../index';
import { cardDataSlice, cardUISlice } from '../slices/card';
import { Card } from '../../../data/models/Card';

// Mock the services and middleware
jest.mock('../middleware/realmMiddleware', () => ({
  realmListenerMiddleware: {
    middleware: jest.fn(() => (next: any) => (action: any) => next(action)),
  },
  RealmReduxBridge: {
    initialize: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock('../middleware/eventBridge', () => ({
  eventBridgeMiddleware: {
    middleware: jest.fn(() => (next: any) => (action: any) => next(action)),
  },
  initializeEventBridge: jest.fn().mockResolvedValue(undefined),
  getBridgeHealth: jest.fn().mockReturnValue({ isInitialized: true, isHealthy: true }),
}));

jest.mock('../../services', () => ({
  ServiceLayer: {
    initialize: jest.fn().mockResolvedValue(undefined),
  },
}));

// Mock legacy services
jest.mock('../../../lib/common/services/offer', () => ({
  default: {
    createOffer: jest.fn(),
    updateOffer: jest.fn(),
    deleteOffer: jest.fn(),
  },
}), { virtual: true });

jest.mock('../../../lib/common/services/card', () => ({
  default: {
    createCard: jest.fn(),
    updateCard: jest.fn(),
    deleteCard: jest.fn(),
  },
}), { virtual: true });

jest.mock('../../../lib/Events.json', () => ({
  Card: {
    register: 'card.register',
    accept: 'card.accept',
    decline: 'card.decline',
  },
  Offer: {
    create: 'offer.create',
    update: 'offer.update',
    redeem: 'offer.redeem',
  },
}), { virtual: true });

describe('Enhanced Store Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Store Configuration', () => {
    it('should have correct initial state with new slices', () => {
      const state = store.getState();

      // Check new slice structure (since USE_NEW_SLICES = true)
      expect(state.cardData).toBeDefined();
      expect(state.cardUI).toBeDefined();
      expect(state.cardData.loading).toBe(false);
      expect(state.cardData.error).toBe(null);
      expect(state.cardData.ids).toEqual([]);
      expect(state.cardData.entities).toEqual({});

      expect(state.offerData).toBeDefined();
      expect(state.offerUI).toBeDefined();
      expect(state.offerData.loading).toBe(false);
      expect(state.offerData.error).toBe(null);
      expect(state.offerData.ids).toEqual([]);
      expect(state.offerData.entities).toEqual({});

      expect(state.personData).toBeDefined();
      expect(state.personUI).toBeDefined();
      expect(state.personData.loading).toBe(false);
      expect(state.personData.error).toBe(null);
      expect(state.personData.currentPersonId).toBe(null);
      expect(state.personData.ids).toEqual([]);
      expect(state.personData.entities).toEqual({});

      // Check common slices
      expect(state.app).toBeDefined();
      expect(state.profile).toBeDefined();
      expect(state.installation).toBeDefined();
      expect(state.permissions).toBeDefined();
      expect(state.notifications).toBeDefined();
    });

    it('should support new slice operations', () => {
      const mockCard: Card = {
        id: 'card-1',
        masterId: 'master-1',
        personId: 'person-1',
        displayName: 'Test Card',
        state: 'active',
        visible: true,
        createdAt: new Date('2023-01-01'),
      } as Card;

      // Test new slice operations
      store.dispatch(cardDataSlice.actions.syncFromRealm([mockCard]));
      let state = store.getState();
      expect(state.cardData.ids).toContain('card-1');
      expect(state.cardData.entities['card-1']).toEqual(mockCard);

      // Test UI slice
      store.dispatch(cardUISlice.actions.selectCard('card-1'));
      state = store.getState();
      expect(state.cardUI.selectedCardId).toBe('card-1');
    });
  });

  describe('Store Statistics', () => {
    it('should return correct statistics for new slices', () => {
      const mockCard: Card = {
        id: 'card-1',
        masterId: 'master-1',
        personId: 'person-1',
        displayName: 'Test Card',
        state: 'active',
        visible: true,
        createdAt: new Date('2023-01-01'),
      } as Card;

      // Add some test data
      store.dispatch(cardDataSlice.actions.syncFromRealm([mockCard]));

      const stats = getStoreStatistics();
      expect(stats.slices.cards.count).toBe(1);
      expect(stats.slices.cards.loading).toBe(false);
      expect(stats.performance.realmIntegration).toBe('active');
    });
  });

  describe('Store Initialization', () => {
    it('should initialize store successfully', async () => {
      const initializedStore = await initializeStore();
      expect(initializedStore).toBe(store);

      const state = initializedStore.getState();
      expect(state.app.isInitialized).toBe(true);
      expect(state.app.architecture).toBe('clean-entity-adapter');
      expect(state.app.schemaVersion).toBe(2);
    });
  });

  describe('Error Handling', () => {
    it('should handle slice errors gracefully', () => {
      // Test error clearing in new slices
      store.dispatch(cardDataSlice.actions.clearError());
      let state = store.getState();
      expect(state.cardData.error).toBe(null);
    });
  });

  describe('Performance', () => {
    it('should handle large datasets efficiently with new slices', () => {
      const startTime = Date.now();

      // Create large dataset
      const cards: Card[] = Array.from({ length: 1000 }, (_, i) => ({
        id: `card-${i}`,
        masterId: `master-${i % 10}`,
        personId: `person-${i % 100}`,
        displayName: `Card ${i}`,
        state: 'active',
        visible: true,
        createdAt: new Date(),
      } as Card));

      // Sync large dataset
      store.dispatch(cardDataSlice.actions.syncFromRealm(cards));

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time
      expect(duration).toBeLessThan(100);

      const state = store.getState();
      expect(state.cardData.ids).toHaveLength(1000);
    });
  });
});
