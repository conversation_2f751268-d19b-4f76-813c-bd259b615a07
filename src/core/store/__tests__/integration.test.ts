/**
 * Events-Redux Integration Test
 * 
 * Simple integration test to verify the enhanced store and bridge work together
 */

import { store, initializeStore } from '../index';

// Mock the dependencies to focus on integration
jest.mock('../middleware/realmMiddleware', () => ({
  realmListenerMiddleware: {
    middleware: jest.fn(),
  },
  RealmReduxBridge: {
    initialize: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock('../../events/globalEventEmitter', () => ({
  globalEventEmitter: {
    on: jest.fn(),
    emit: jest.fn(),
    safeEmit: jest.fn().mockReturnValue(true),
    enableReduxBridge: jest.fn(),
    disableReduxBridge: jest.fn(),
    isReduxBridgeEnabled: jest.fn().mockReturnValue(true),
    getMetrics: jest.fn().mockReturnValue({
      totalEvents: 0,
      errorCount: 0,
      listenerCount: 0,
      eventsPerSecond: 0,
      memoryUsage: 0,
      averageProcessingTime: 0,
      lastActivity: 0,
      eventsByType: {},
      thresholds: {
        maxListeners: 50,
        maxProcessingTime: 100,
        maxEventsPerSecond: 1000,
      },
    }),
    removeAllListeners: jest.fn(),
    resetMetrics: jest.fn(),
  },
}));

jest.mock('../../events/integrationHelpers', () => ({
  eventSystemManager: {
    initialize: jest.fn().mockResolvedValue(undefined),
    isSystemInitialized: jest.fn().mockReturnValue(true),
    getSystemHealth: jest.fn().mockReturnValue({
      isInitialized: true,
      legacyMode: true,
      reduxIntegration: true,
      metrics: {},
      issues: [],
      recommendations: [],
    }),
  },
}));

describe('Events-Redux Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Store Initialization', () => {
    it('should initialize store with all components', async () => {
      const initializedStore = await initializeStore();

      expect(initializedStore).toBe(store);

      // Verify store has correct structure
      const state = store.getState();
      expect(state).toHaveProperty('cardData');
      expect(state).toHaveProperty('cardUI');
      expect(state).toHaveProperty('offerData');
      expect(state).toHaveProperty('offerUI');
      expect(state).toHaveProperty('personData');
      expect(state).toHaveProperty('personUI');
      expect(state).toHaveProperty('app');
      expect(state).toHaveProperty('profile');
    });

    it('should initialize with proper app state', async () => {
      await initializeStore();

      const state = store.getState();
      expect(state.app.isInitialized).toBe(true);
      expect(state.app.version).toBe('1.0.0');
      expect(state.app.architecture).toBe('clean-entity-adapter');
    });
  });

  describe('Redux Actions', () => {
    it('should dispatch actions successfully', async () => {
      await initializeStore();
      
      // Import slice after store is initialized
      const { cardSlice } = require('../../../store/slices/cardSlice');
      
      expect(() => {
        store.dispatch(cardSlice.actions.setLoading(true));
      }).not.toThrow();
      
      const state = store.getState();
      expect(state.cardData.loading).toBe(false); // New slices start with loading: false
    });

    it('should handle invalid actions gracefully', async () => {
      await initializeStore();
      
      expect(() => {
        store.dispatch({ type: 'INVALID_ACTION' });
      }).not.toThrow();
    });
  });

  describe('Bridge Integration', () => {
    it('should initialize event system manager', async () => {
      const { eventSystemManager } = require('../../events/integrationHelpers');
      
      await initializeStore();
      
      expect(eventSystemManager.initialize).toHaveBeenCalledWith({
        preserveLegacy: true,
        enableReduxIntegration: true,
        store: store,
      });
    });

    it('should initialize Realm bridge', async () => {
      const { RealmReduxBridge } = require('../../../store/middleware/realmMiddleware');
      
      await initializeStore();
      
      expect(RealmReduxBridge.initialize).toHaveBeenCalledWith(store.dispatch);
    });

    it('should provide bridge health status', async () => {
      const { getBridgeHealth } = require('../middleware/eventBridge');
      
      await initializeStore();
      
      const health = getBridgeHealth();
      expect(health).toHaveProperty('isInitialized');
      expect(health).toHaveProperty('config');
      expect(health).toHaveProperty('health');
    });
  });

  describe('Store Health Monitoring', () => {
    it('should provide comprehensive health check', async () => {
      const { useStoreHealth } = require('../index');
      
      await initializeStore();
      
      // Mock React hook context
      const mockUseSelector = jest.fn().mockReturnValue({
        isInitialized: true,
        version: '1.0.0',
        architecture: 'clean-entity-adapter',
      });
      
      const mockUseDispatch = jest.fn();
      
      // Mock the hooks
      jest.doMock('react-redux', () => ({
        useSelector: mockUseSelector,
        useDispatch: mockUseDispatch,
      }));
      
      // This would normally be called within a React component
      // For testing, we'll just verify the function exists
      expect(typeof useStoreHealth).toBe('function');
    });

    it('should provide store statistics', async () => {
      const { getStoreStatistics } = require('../index');
      
      await initializeStore();
      
      const stats = getStoreStatistics();
      
      expect(stats).toHaveProperty('timestamp');
      expect(stats).toHaveProperty('architecture', 'clean-entity-adapter');
      expect(stats).toHaveProperty('slices');
      expect(stats).toHaveProperty('persistence');
      expect(stats).toHaveProperty('performance');
      
      expect(stats.persistence.realmIntegration).toBe('active');
      expect(stats.persistence.mmkvPersistence).toBe('active');
      expect(stats.persistence.eventBridge).toBe('active');
    });
  });

  describe('Error Handling', () => {
    it('should handle initialization errors gracefully', async () => {
      const { RealmReduxBridge } = require('../../../store/middleware/realmMiddleware');
      RealmReduxBridge.initialize.mockRejectedValueOnce(new Error('Test error'));

      await expect(initializeStore()).rejects.toThrow('Test error');
    });

    it('should handle serialization errors in actions', async () => {
      await initializeStore();
      
      // Test with non-serializable data (should be ignored)
      expect(() => {
        store.dispatch({
          type: 'cards/updateFromRealm',
          payload: {
            cards: [{ id: '1', realmObject: new Date() }], // Non-serializable
          },
        });
      }).not.toThrow();
    });
  });

  describe('Performance', () => {
    it('should handle multiple rapid dispatches', async () => {
      await initializeStore();
      
      const { cardSlice } = require('../../../store/slices/cardSlice');
      
      const startTime = performance.now();
      
      // Dispatch many actions rapidly
      for (let i = 0; i < 100; i++) {
        store.dispatch(cardSlice.actions.setLoading(i % 2 === 0));
      }
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete quickly
      expect(duration).toBeLessThan(100); // 100ms
      
      // Final state should be consistent
      const state = store.getState();
      expect(typeof state.cardData.loading).toBe('boolean');
    });
  });

  describe('Middleware Integration', () => {
    it('should have correct middleware order', async () => {
      await initializeStore();
      
      // Verify store is configured with middleware
      expect(store).toBeDefined();
      expect(typeof store.dispatch).toBe('function');
      expect(typeof store.getState).toBe('function');
      expect(typeof store.subscribe).toBe('function');
    });

    it('should support DevTools in development', async () => {
      await initializeStore();
      
      if (__DEV__) {
        // DevTools should be enabled in development
        expect(store).toBeDefined();
      }
    });
  });
});

describe('Architecture Validation', () => {
  it('should maintain separation of concerns', async () => {
    await initializeStore();
    
    const state = store.getState();
    
    // Business entities (managed by Realm middleware)
    expect(state).toHaveProperty('cards');
    expect(state).toHaveProperty('offers');
    expect(state).toHaveProperty('person');
    
    // UI state (managed by Redux persistence)
    expect(state).toHaveProperty('profile');
    expect(state).toHaveProperty('app');
  });

  it('should support the three-layer architecture', async () => {
    await initializeStore();
    
    const stats = require('../index').getStoreStatistics();
    
    // Layer 1: Business Logic (Event System)
    expect(stats.persistence.eventBridge).toBe('active');
    
    // Layer 2: State Management (Redux)
    expect(stats.persistence.mmkvPersistence).toBe('active');
    
    // Layer 3: Data Persistence (Realm)
    expect(stats.persistence.realmIntegration).toBe('active');
  });
});
