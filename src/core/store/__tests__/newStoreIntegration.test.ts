import { configureStore } from '@reduxjs/toolkit';
import { cardDataSlice, cardUISlice } from '../slices/card';
import { offerDataSlice, offerUISlice } from '../slices/offer';
import { personDataSlice, personUISlice } from '../slices/person';
import { appSlice } from '../slices/appSlice';
import { Card } from '../../../data/models/Card';
import { Offer } from '../../../data/models/Offer';
import { Person } from '../../../data/models/Person';

// Mock the services and middleware
jest.mock('../middleware/realmMiddleware', () => ({
  realmListenerMiddleware: {
    middleware: jest.fn(() => (next: any) => (action: any) => next(action)),
  },
  RealmReduxBridge: {
    initialize: jest.fn().mockResolvedValue(undefined),
    getInstance: jest.fn().mockReturnValue({
      connectCardDataSlice: jest.fn(),
      connectOfferDataSlice: jest.fn(),
      connectPersonDataSlice: jest.fn(),
    }),
  },
}));

// Mock legacy services that might not exist
jest.mock('../../../lib/common/services/offer', () => ({
  default: {
    createOffer: jest.fn(),
    updateOffer: jest.fn(),
    deleteOffer: jest.fn(),
  },
}), { virtual: true });

jest.mock('../../../lib/common/services/card', () => ({
  default: {
    createCard: jest.fn(),
    updateCard: jest.fn(),
    deleteCard: jest.fn(),
  },
}), { virtual: true });

jest.mock('../../../lib/Events.json', () => ({
  Card: {
    register: 'card.register',
    accept: 'card.accept',
    decline: 'card.decline',
  },
  Offer: {
    create: 'offer.create',
    update: 'offer.update',
    redeem: 'offer.redeem',
  },
}), { virtual: true });

jest.mock('../middleware/eventBridge', () => ({
  eventBridgeMiddleware: {
    middleware: jest.fn(() => (next: any) => (action: any) => next(action)),
  },
  initializeEventBridge: jest.fn().mockResolvedValue(undefined),
  getBridgeHealth: jest.fn().mockReturnValue({ isInitialized: true, isHealthy: true }),
}));

jest.mock('../../services', () => ({
  ServiceLayer: {
    initialize: jest.fn().mockResolvedValue(undefined),
  },
}));

describe('New Store Integration', () => {
  let testStore: ReturnType<typeof configureStore>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create a test store with only the new slices
    testStore = configureStore({
      reducer: {
        cardData: cardDataSlice.reducer,
        cardUI: cardUISlice.reducer,
        offerData: offerDataSlice.reducer,
        offerUI: offerUISlice.reducer,
        personData: personDataSlice.reducer,
        personUI: personUISlice.reducer,
        app: appSlice.reducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
          serializableCheck: {
            ignoredActions: [
              'cardData/syncFromRealm',
              'offerData/syncFromRealm',
              'personData/syncFromRealm',
            ],
            ignoredPaths: [
              'cardData.entities',
              'offerData.entities',
              'personData.entities',
            ],
          },
        }),
    });
  });

  describe('Store Configuration', () => {
    it('should have correct initial state with new slices', () => {
      const state = testStore.getState();

      // Check new slice structure
      expect(state.cardData).toBeDefined();
      expect(state.cardUI).toBeDefined();
      expect(state.cardData.loading).toBe(false);
      expect(state.cardData.error).toBe(null);
      expect(state.cardData.ids).toEqual([]);
      expect(state.cardData.entities).toEqual({});

      expect(state.offerData).toBeDefined();
      expect(state.offerUI).toBeDefined();
      expect(state.offerData.loading).toBe(false);
      expect(state.offerData.error).toBe(null);
      expect(state.offerData.ids).toEqual([]);
      expect(state.offerData.entities).toEqual({});

      expect(state.personData).toBeDefined();
      expect(state.personUI).toBeDefined();
      expect(state.personData.loading).toBe(false);
      expect(state.personData.error).toBe(null);
      expect(state.personData.currentPersonId).toBe(null);
      expect(state.personData.ids).toEqual([]);
      expect(state.personData.entities).toEqual({});

      // Check common slices
      expect(state.app).toBeDefined();
    });
  });

  describe('Card Slice Integration', () => {
    it('should handle card data operations', () => {
      const mockCards: Card[] = [
        {
          id: 'card-1',
          masterId: 'master-1',
          personId: 'person-1',
          displayName: 'Test Card',
          state: 'active',
          visible: true,
          createdAt: new Date('2023-01-01'),
        } as Card,
      ];

      // Test data slice
      testStore.dispatch(cardDataSlice.actions.syncFromRealm(mockCards));
      let state = testStore.getState();
      expect(state.cardData.ids).toContain('card-1');
      expect(state.cardData.entities['card-1']).toEqual(mockCards[0]);

      // Test UI slice
      testStore.dispatch(cardUISlice.actions.selectCard('card-1'));
      state = testStore.getState();
      expect(state.cardUI.selectedCardId).toBe('card-1');

      // Test filters
      testStore.dispatch(cardUISlice.actions.setFilters({ status: 'active' }));
      state = testStore.getState();
      expect(state.cardUI.filters.status).toBe('active');
    });
  });

  describe('Offer Slice Integration', () => {
    it('should handle offer data operations', () => {
      const mockOffers: Offer[] = [
        {
          id: 'offer-1',
          masterId: 'master-1',
          name: 'Test Offer',
          title: 'Great Deal',
          kind: 'discount',
          state: 'active',
          visible: true,
          createdAt: new Date('2023-01-01'),
        } as Offer,
      ];

      // Test data slice
      testStore.dispatch(offerDataSlice.actions.syncFromRealm(mockOffers));
      let state = testStore.getState();
      expect(state.offerData.ids).toContain('offer-1');
      expect(state.offerData.entities['offer-1']).toEqual(mockOffers[0]);

      // Test UI slice
      testStore.dispatch(offerUISlice.actions.selectOffer('offer-1'));
      state = testStore.getState();
      expect(state.offerUI.selectedOfferId).toBe('offer-1');

      // Test view mode
      testStore.dispatch(offerUISlice.actions.setViewMode('grid'));
      state = testStore.getState();
      expect(state.offerUI.viewMode).toBe('grid');
    });

    it('should handle redemption tracking', () => {
      const mockRedemptions = [
        {
          id: 'redemption-1',
          offerId: 'offer-1',
          cardId: 'card-1',
          timestamp: '2023-01-01T10:00:00Z',
        },
      ];

      testStore.dispatch(offerDataSlice.actions.syncRedemptionsFromRealm(mockRedemptions));
      const state = testStore.getState();
      expect(state.offerData.redemptions.ids).toContain('offer-1_2023-01-01T10:00:00Z');
    });
  });

  describe('Person Slice Integration', () => {
    it('should handle person data operations', () => {
      const mockPersons: Person[] = [
        {
          id: 'person-1',
          givenName: 'John',
          familyName: 'Doe',
          fullName: 'John Doe',
          gender: 'male',
          createdAt: new Date('2023-01-01'),
          modifiedAt: new Date('2023-01-01'),
        } as Person,
      ];

      // Test data slice
      testStore.dispatch(personDataSlice.actions.syncFromRealm(mockPersons));
      let state = testStore.getState();
      expect(state.personData.ids).toContain('person-1');
      expect(state.personData.entities['person-1']).toEqual(mockPersons[0]);

      // Test current person
      testStore.dispatch(personDataSlice.actions.setCurrentPerson('person-1'));
      state = testStore.getState();
      expect(state.personData.currentPersonId).toBe('person-1');

      // Test UI slice
      testStore.dispatch(personUISlice.actions.selectPerson('person-1'));
      state = testStore.getState();
      expect(state.personUI.selectedPersonId).toBe('person-1');

      // Test edit mode
      testStore.dispatch(personUISlice.actions.setEditMode(true));
      state = testStore.getState();
      expect(state.personUI.editMode).toBe(true);
    });
  });

  describe('Cross-Slice Integration', () => {
    it('should handle complex multi-slice operations', () => {
      // Setup test data
      const mockCard: Card = {
        id: 'card-1',
        masterId: 'master-1',
        personId: 'person-1',
        displayName: 'Test Card',
        state: 'active',
        visible: true,
        createdAt: new Date('2023-01-01'),
      } as Card;

      const mockOffer: Offer = {
        id: 'offer-1',
        masterId: 'master-1',
        name: 'Test Offer',
        title: 'Great Deal',
        kind: 'discount',
        state: 'active',
        visible: true,
        createdAt: new Date('2023-01-01'),
      } as Offer;

      const mockPerson: Person = {
        id: 'person-1',
        givenName: 'John',
        familyName: 'Doe',
        fullName: 'John Doe',
        gender: 'male',
        createdAt: new Date('2023-01-01'),
        modifiedAt: new Date('2023-01-01'),
      } as Person;

      // Sync all data
      testStore.dispatch(cardDataSlice.actions.syncFromRealm([mockCard]));
      testStore.dispatch(offerDataSlice.actions.syncFromRealm([mockOffer]));
      testStore.dispatch(personDataSlice.actions.syncFromRealm([mockPerson]));

      // Set current person
      testStore.dispatch(personDataSlice.actions.setCurrentPerson('person-1'));

      // Select related entities
      testStore.dispatch(cardUISlice.actions.selectCard('card-1'));
      testStore.dispatch(offerUISlice.actions.selectOffer('offer-1'));
      testStore.dispatch(personUISlice.actions.selectPerson('person-1'));

      // Apply filters based on master
      testStore.dispatch(cardUISlice.actions.setFilters({ masterId: 'master-1' }));
      testStore.dispatch(offerUISlice.actions.setFilters({ masterId: 'master-1' }));

      const state = testStore.getState();

      // Verify all data is connected
      expect(state.cardData.entities['card-1']?.masterId).toBe('master-1');
      expect(state.cardData.entities['card-1']?.personId).toBe('person-1');
      expect(state.offerData.entities['offer-1']?.masterId).toBe('master-1');
      expect(state.personData.entities['person-1']?.id).toBe('person-1');
      expect(state.personData.currentPersonId).toBe('person-1');

      // Verify UI state
      expect(state.cardUI.selectedCardId).toBe('card-1');
      expect(state.cardUI.filters.masterId).toBe('master-1');
      expect(state.offerUI.selectedOfferId).toBe('offer-1');
      expect(state.offerUI.filters.masterId).toBe('master-1');
      expect(state.personUI.selectedPersonId).toBe('person-1');
    });
  });

  describe('Error Handling', () => {
    it('should handle slice errors gracefully', () => {
      // Test error in card slice
      testStore.dispatch(cardDataSlice.actions.clearError());
      let state = testStore.getState();
      expect(state.cardData.error).toBe(null);

      // Test error in offer slice
      testStore.dispatch(offerDataSlice.actions.clearError());
      state = testStore.getState();
      expect(state.offerData.error).toBe(null);

      // Test error in person slice
      testStore.dispatch(personDataSlice.actions.clearError());
      state = testStore.getState();
      expect(state.personData.error).toBe(null);
    });
  });

  describe('Performance', () => {
    it('should handle large datasets efficiently', () => {
      const startTime = Date.now();

      // Create large datasets
      const cards: Card[] = Array.from({ length: 1000 }, (_, i) => ({
        id: `card-${i}`,
        masterId: `master-${i % 10}`,
        personId: `person-${i % 100}`,
        displayName: `Card ${i}`,
        state: 'active',
        visible: true,
        createdAt: new Date(),
      } as Card));

      const offers: Offer[] = Array.from({ length: 1000 }, (_, i) => ({
        id: `offer-${i}`,
        masterId: `master-${i % 10}`,
        name: `Offer ${i}`,
        title: `Deal ${i}`,
        kind: 'discount',
        state: 'active',
        visible: true,
        createdAt: new Date(),
      } as Offer));

      // Sync large datasets
      testStore.dispatch(cardDataSlice.actions.syncFromRealm(cards));
      testStore.dispatch(offerDataSlice.actions.syncFromRealm(offers));

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (< 100ms for 2000 entities)
      expect(duration).toBeLessThan(100);

      const state = testStore.getState();
      expect(state.cardData.ids).toHaveLength(1000);
      expect(state.offerData.ids).toHaveLength(1000);
    });
  });
});
