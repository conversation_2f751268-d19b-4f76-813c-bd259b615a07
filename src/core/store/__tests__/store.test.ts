/**
 * Enhanced Redux Store Tests
 * 
 * Tests for the Events-Redux architecture store configuration
 */

import { store, initializeStore, getStoreStatistics, useStoreHealth } from '../index';
import { getBridgeHealth } from '../middleware/eventBridge';
import { storageHealth } from '../storage';

// Mock dependencies
jest.mock('../../events/globalEventEmitter', () => ({
  globalEventEmitter: {
    on: jest.fn(),
    emit: jest.fn(),
    safeEmit: jest.fn(),
    enableReduxBridge: jest.fn(),
    disableReduxBridge: jest.fn(),
    isReduxBridgeEnabled: jest.fn().mockReturnValue(true),
    getMetrics: jest.fn().mockReturnValue({
      totalEvents: 0,
      errorCount: 0,
      listenerCount: 0,
    }),
  },
}));

jest.mock('../../events/integrationHelpers', () => ({
  eventSystemManager: {
    initialize: jest.fn().mockResolvedValue(undefined),
  },
}));

jest.mock('../../../store/middleware/realmMiddleware', () => ({
  realmListenerMiddleware: {
    middleware: jest.fn(),
  },
  RealmReduxBridge: {
    initialize: jest.fn().mockResolvedValue(undefined),
  },
}));

describe('Enhanced Redux Store', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Store Configuration', () => {
    it('should have correct initial state structure', () => {
      const state = store.getState();
      
      expect(state).toHaveProperty('cards');
      expect(state).toHaveProperty('offers');
      expect(state).toHaveProperty('person');
      expect(state).toHaveProperty('app');
      expect(state).toHaveProperty('profile');
    });

    it('should have proper middleware configuration', () => {
      // Store should be configured with middleware
      expect(store).toBeDefined();
      expect(typeof store.dispatch).toBe('function');
      expect(typeof store.getState).toBe('function');
    });

    it('should support Redux DevTools in development', () => {
      // DevTools should be enabled in development
      if (__DEV__) {
        expect(store).toHaveProperty('__REDUX_DEVTOOLS_EXTENSION__');
      }
    });
  });

  describe('Store Initialization', () => {
    it('should initialize store successfully', async () => {
      const initializedStore = await initializeStore();

      expect(initializedStore).toBe(store);

      // Check that app state is initialized
      const state = store.getState();
      expect(state.app.isInitialized).toBe(true);
      expect(state.app.version).toBe('7.0.0');
      expect(state.app.architecture).toBe('events-redux-enhanced');
    });

    it('should handle initialization errors gracefully', async () => {
      // Mock RealmReduxBridge to throw error
      const { RealmReduxBridge } = require('../../../store/middleware/realmMiddleware');
      RealmReduxBridge.initialize.mockRejectedValueOnce(new Error('Realm error'));

      await expect(initializeStore()).rejects.toThrow('Realm error');
    });
  });

  describe('Store Statistics', () => {
    it('should provide comprehensive store statistics', () => {
      const stats = getStoreStatistics();

      expect(stats).toHaveProperty('timestamp');
      expect(stats).toHaveProperty('stateSize');
      expect(stats).toHaveProperty('architecture', 'clean-entity-adapter');
      expect(stats).toHaveProperty('slices');
      expect(stats).toHaveProperty('persistence');
      expect(stats).toHaveProperty('performance');

      // Check slice statistics
      expect(stats.slices).toHaveProperty('cards');
      expect(stats.slices).toHaveProperty('offers');
      expect(stats.slices).toHaveProperty('person');
      expect(stats.slices).toHaveProperty('profile');

      // Check persistence status
      expect(stats.persistence.realmIntegration).toBe('active');
      expect(stats.persistence.mmkvPersistence).toBe('active');
      expect(stats.persistence.eventBridge).toBe('active');
    });

    it('should track performance metrics', () => {
      const stats = getStoreStatistics();

      expect(stats.performance.middlewareCount).toBe(3);
      expect(stats.performance.devToolsEnabled).toBe(__DEV__);
      expect(stats.performance.persistenceEnabled).toBe(true);
    });
  });

  describe('Bridge Health Monitoring', () => {
    it('should provide bridge health status', () => {
      const bridgeHealth = getBridgeHealth();
      
      expect(bridgeHealth).toHaveProperty('isInitialized');
      expect(bridgeHealth).toHaveProperty('config');
      expect(bridgeHealth).toHaveProperty('health');
      
      if (bridgeHealth.isInitialized) {
        expect(bridgeHealth).toHaveProperty('metrics');
        expect(bridgeHealth.health).toHaveProperty('isHealthy');
        expect(bridgeHealth.health).toHaveProperty('errorRate');
      }
    });
  });

  describe('Storage Health', () => {
    it('should provide storage statistics', () => {
      const stats = storageHealth.getStorageStats();
      
      expect(stats).toHaveProperty('redux');
      expect(stats).toHaveProperty('events');
      expect(stats).toHaveProperty('cache');
      expect(stats).toHaveProperty('total');
      
      expect(stats.redux).toHaveProperty('keys');
      expect(stats.redux).toHaveProperty('size');
    });

    it('should support storage cleanup', () => {
      expect(() => storageHealth.cleanup()).not.toThrow();
    });
  });

  describe('Action Dispatching', () => {
    it('should dispatch actions successfully', () => {
      const { cardSlice } = require('../../../store/slices/cardSlice');
      
      expect(() => {
        store.dispatch(cardSlice.actions.setLoading(true));
      }).not.toThrow();
      
      const state = store.getState();
      expect(state.cardData.loading).toBe(false); // New slices start with loading: false
    });

    it('should handle invalid actions gracefully', () => {
      expect(() => {
        store.dispatch({ type: 'INVALID_ACTION' });
      }).not.toThrow();
    });
  });

  describe('Persistence', () => {
    it('should persist whitelisted state', () => {
      // This would require more complex setup with actual persistence
      // For now, just verify the configuration
      expect(store).toBeDefined();
    });

    it('should not persist blacklisted state', () => {
      // Verify that business entities are not persisted via Redux
      // They should be handled by Realm middleware instead
      expect(store).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle serialization errors gracefully', () => {
      // Redux should ignore non-serializable data in specified paths
      expect(() => {
        store.dispatch({
          type: 'cards/updateFromRealm',
          payload: {
            cards: [{ id: '1', realmObject: {} }], // Non-serializable
          },
        });
      }).not.toThrow();
    });

    it('should handle large state gracefully', () => {
      // Performance checks should warn but not fail
      const largePayload = new Array(10000).fill({ data: 'test' });
      
      expect(() => {
        store.dispatch({
          type: 'test/largePayload',
          payload: largePayload,
        });
      }).not.toThrow();
    });
  });
});

describe('Store Integration', () => {
  it('should integrate with Realm middleware', async () => {
    await initializeStore();

    const { RealmReduxBridge } = require('../../../store/middleware/realmMiddleware');
    expect(RealmReduxBridge.initialize).toHaveBeenCalledWith(store.dispatch);
  });

  it('should integrate with Event-Redux bridge', async () => {
    await initializeStore();

    const bridgeHealth = getBridgeHealth();
    expect(bridgeHealth.isInitialized).toBe(true);
  });

  it('should maintain separation of concerns', () => {
    const state = store.getState();
    
    // Business entities should be managed by Realm - NEW ARCHITECTURE
    expect(state.cardData).toBeDefined();
    expect(state.cardUI).toBeDefined();
    expect(state.offerData).toBeDefined();
    expect(state.offerUI).toBeDefined();
    expect(state.personData).toBeDefined();
    expect(state.personUI).toBeDefined();
    
    // UI state should be managed by Redux persistence
    expect(state.profile).toBeDefined();
    expect(state.app).toBeDefined();
  });
});
