/**
 * Redux Store Configuration
 *
 * Clean architecture with entity adapters and Realm integration
 * Implements three-layer complementary architecture:
 * 1. Realm Middleware: Database persistence
 * 2. Event System: Business logic coordination
 * 3. Events-Redux Bridge: Event-Redux communication
 */

import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import { setupListeners } from '@reduxjs/toolkit/query';
import {
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER
} from 'redux-persist';

// Storage configuration
import { mmkvStorage } from './storage';

// New slice architecture - legacy slices removed

// Import slices - NEW ARCHITECTURE
import { cardDataSlice, cardUISlice } from './slices/card';
import { offerDataSlice, offerUISlice } from './slices/offer';
import { personDataSlice, personUISlice } from './slices/person';

// Import common slices
import { appSlice } from './slices/appSlice';

// Import Realm middleware (now local)
import { realmListenerMiddleware, RealmReduxBridge } from './middleware/realmMiddleware';

// Import Event-Redux bridge middleware
import { eventBridgeMiddleware, initializeEventBridge, getBridgeHealth, registerBridgeConfig } from './middleware/eventBridge';

// Import bridge configurations
import { cardBridgeConfig } from './middleware/cardBridgeConfig';

// Import service layer
import { ServiceLayer } from '../services';

// Import feature reducers (Critical Components)
import installationReducer from '../../features/installations/store/installationSlice';
import permissionsReducer from '../../features/permissions/store/permissionsSlice';
import notificationsReducer from '../../features/notifications/store/notificationsSlice';

// =============================================================================
// ROOT REDUCER CONFIGURATION
// =============================================================================

const rootReducer = combineReducers({
  // Core business entities - NEW ARCHITECTURE
  // Card slices
  cardData: cardDataSlice.reducer,
  cardUI: cardUISlice.reducer,

  // Offer slices
  offerData: offerDataSlice.reducer,
  offerUI: offerUISlice.reducer,

  // Person slices (includes profile functionality)
  personData: personDataSlice.reducer,
  personUI: personUISlice.reducer,

  // Application state
  app: appSlice.reducer,

  // Critical Components - Pure Redux Toolkit Implementation
  installation: installationReducer,
  permissions: permissionsReducer,
  notifications: notificationsReducer,
});

// =============================================================================
// PERSISTENCE CONFIGURATION
// =============================================================================

const persistConfig = {
  key: 'perkd-redux-key',
  storage: mmkvStorage,
  version: 1, // Clean architecture version

  // Persist UI state only - Realm handles business data
  whitelist: ['profile', 'app', 'cardUI', 'offerUI', 'personUI'],

  // Don't persist business entities - they're handled by Realm
  blacklist: ['cardData', 'offerData', 'personData'],
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

// =============================================================================
// STORE CONFIGURATION
// =============================================================================

export const store = configureStore({
  reducer: persistedReducer,

  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          // Redux persist actions
          FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER,

          // New slice actions (allow Date objects from Realm)
          'cardData/syncFromRealm',
          'cardData/addCardFromRealm',
          'cardData/updateCardFromRealm',
          'cardData/createOptimistic/fulfilled',
          'cardData/acceptOptimistic/fulfilled',
          'cardData/declineOptimistic/fulfilled',
          'cardData/updateOptimistic/fulfilled',

          'offerData/syncFromRealm',
          'offerData/addOfferFromRealm',
          'offerData/updateOfferFromRealm',
          'offerData/createOptimistic/fulfilled',
          'offerData/updateOptimistic/fulfilled',
          'offerData/redeemOptimistic/fulfilled',

          'personData/syncFromRealm',
          'personData/addPersonFromRealm',
          'personData/updatePersonFromRealm',
          'personData/createOptimistic/fulfilled',
          'personData/updateOptimistic/fulfilled',
          'personData/loadCurrentOptimistic/fulfilled',

          // Critical Components - Ignore async thunk actions
          'installation/initialize/pending',
          'installation/initialize/fulfilled',
          'installation/setToken/pending',
          'installation/setToken/fulfilled',
          'permissions/checkPermission/pending',
          'permissions/checkPermission/fulfilled',
          'permissions/requestPermission/pending',
          'permissions/requestPermission/fulfilled',
          'notifications/initializeNotifications/pending',
          'notifications/initializeNotifications/fulfilled',
          'notifications/processNotification/pending',
          'notifications/processNotification/fulfilled',
        ],
        ignoredPaths: [
          // Ignore Realm objects and Date objects in state
          'cardData.entities',
          'offerData.entities',
          'personData.entities',
          'cardData.lastSync',
          'offerData.lastSync',
          'personData.lastSync',
        ],
      },

      // Performance optimization
      immutableCheck: {
        warnAfter: 128,
      },

      // Thunk configuration with services access
      thunk: {
        extraArgument: {
          dataService: () => import('../../data/services/DataService'),
          realmManager: () => import('../database/config'),
        },
      },
    })
    .concat(realmListenerMiddleware.middleware)
    .concat(eventBridgeMiddleware.middleware),

  // DevTools configuration
  devTools: __DEV__ && {
    name: 'Store',
    maxAge: 50,
    trace: true,
    traceLimit: 25,
  },
});

// =============================================================================
// STORE INITIALIZATION
// =============================================================================

export const persistor = persistStore(store);

/**
 * Initialize Store
 *
 * Sets up store with Realm integration and prepares for Event bridge
 */
export async function initializeStore(): Promise<typeof store> {
  try {
    console.log('🏪 Initializing Store...');
    console.log('🚩 Using clean slice architecture');

    // Initialize existing Realm-Redux bridge (PRESERVE)
    await RealmReduxBridge.initialize(store.dispatch);

    // New slices are connected via middleware
    console.log('✅ New slices ready for Realm bridge connection');

    // Initialize Service Layer
    await ServiceLayer.initialize();

    // Initialize Event-Redux bridge (NEW)
    await initializeEventBridge(store);

    // Register bridge configurations
    registerBridgeConfig(cardBridgeConfig);
    console.log('✅ Card bridge configuration registered');

    // Setup RTK Query listeners
    setupListeners(store.dispatch);

    // Initialize application state
    store.dispatch(appSlice.actions.initialize({
      version: '1.0.0',
      schemaVersion: 1,
      initializedAt: new Date().toISOString(),
      architecture: 'clean-entity-adapter',
    }));

    console.log('✅ Store initialized successfully');
    console.log('🔗 Realm middleware: Active');
    console.log('🔗 Event-Redux bridge: Active');
    console.log('🔗 Slice architecture: Clean');

    return store;
  } catch (error) {
    console.error('❌ Failed to initialize store:', error);
    throw error;
  }
}

// =============================================================================
// TYPED HOOKS
// =============================================================================

import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';

/**
 * Typed useDispatch hook for store
 */
export const useAppDispatch = () => useDispatch<AppDispatch>();

/**
 * Typed useSelector hook for store
 */
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// =============================================================================
// STORE UTILITIES
// =============================================================================

/**
 * Get Store Statistics
 */
export function getStoreStatistics() {
  const state = store.getState();

  return {
    timestamp: new Date(),
    stateSize: JSON.stringify(state).length,
    architecture: 'clean-entity-adapter',

    slices: {
      // Clean slice structure
      cards: {
        count: (state as any).cardData?.ids?.length || 0,
        loading: (state as any).cardData?.loading || false,
        lastSync: (state as any).cardData?.lastSync,
      },
      offers: {
        count: (state as any).offerData?.ids?.length || 0,
        loading: (state as any).offerData?.loading || false,
        lastSync: (state as any).offerData?.lastSync,
      },
      person: {
        isLoaded: !!(state as any).personData?.currentPersonId,
        loading: (state as any).personData?.loading || false,
        lastSync: (state as any).personData?.lastSync,
      },
      profile: {
        isLoaded: !!state.profile,
      },
    },

    persistence: {
      realmIntegration: 'active',
      mmkvPersistence: 'active',
      eventBridge: 'active',
    },

    performance: {
      middlewareCount: 3, // realmListener + eventBridge + thunk
      devToolsEnabled: __DEV__,
      persistenceEnabled: true,
    },
  };
}

/**
 * Store Health Check
 */
export function useStoreHealth() {
  const appState = useAppSelector(state => state.app);

  const checkHealth = () => {
    try {
      const stats = getStoreStatistics();
      const health = {
        isHealthy: true,
        timestamp: new Date(),
        issues: [] as string[],
        recommendations: [] as string[],
        statistics: stats,
      };

      // Check store initialization
      if (!appState.isInitialized) {
        health.issues.push('Store not properly initialized');
        health.recommendations.push('Call initializeStore()');
      }

      // Check Realm integration
      if (stats.persistence.realmIntegration !== 'active') {
        health.issues.push('Realm integration not active');
        health.recommendations.push('Check Realm middleware configuration');
      }

      // Check Event-Redux bridge
      const bridgeHealth = getBridgeHealth();
      if (!bridgeHealth.isInitialized) {
        health.issues.push('Event-Redux bridge not initialized');
        health.recommendations.push('Call initializeEventBridge()');
      } else if (!bridgeHealth.isHealthy) {
        health.issues.push('Event-Redux bridge has issues');
        health.recommendations.push('Check bridge error logs and configuration');
      }

      // Check for loading states
      if (stats.slices.cards.loading || stats.slices.offers.loading || stats.slices.person.loading) {
        health.recommendations.push('Some data is still loading');
      }

      // Check state size (warn if > 1MB)
      if (stats.stateSize > 1024 * 1024) {
        health.recommendations.push('Large state size detected - consider data cleanup');
      }

      health.isHealthy = health.issues.length === 0;
      return health;

    } catch (error) {
      return {
        isHealthy: false,
        timestamp: new Date(),
        issues: [`Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        recommendations: ['Restart application', 'Check store configuration'],
        statistics: getStoreStatistics(),
      };
    }
  };

  return {
    checkHealth,
    isInitialized: appState.isInitialized,
    version: appState.version,
    schemaVersion: appState.schemaVersion,
    architecture: appState.architecture || 'clean-entity-adapter',
  };
}

// =============================================================================
// TYPE DEFINITIONS
// =============================================================================

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

export interface StoreStatistics {
  timestamp: Date;
  stateSize: number;
  architecture: string;
  slices: {
    cards: { count: number; loading: boolean; lastSync?: string };
    offers: { count: number; loading: boolean; lastSync?: string };
    person: { isLoaded: boolean; loading: boolean; lastSync?: string };
    profile: { isLoaded: boolean };
  };
  persistence: {
    realmIntegration: string;
    mmkvPersistence: string;
    eventBridge: string;
  };
  performance: {
    middlewareCount: number;
    devToolsEnabled: boolean;
    persistenceEnabled: boolean;
  };
}

// Compatible types from deleted store
export interface StoreStatistics {
  timestamp: Date;
  stateSize: number;
  slices: {
    cards: {
      count: number;
      loading: boolean;
      lastSync?: string;
    };
    offers: {
      count: number;
      loading: boolean;
      lastSync?: string;
    };
    person: {
      isLoaded: boolean;
      loading: boolean;
      lastSync?: string;
    };
  };
  performance: {
    realmIntegration: string;
    middlewareCount: number;
    devToolsEnabled: boolean;
  };
}

export interface StoreHealthResult {
  isHealthy: boolean;
  timestamp: Date;
  issues: string[];
  recommendations: string[];
  statistics: StoreStatistics;
}

// =============================================================================
// TEST UTILITIES
// =============================================================================

/**
 * Get store for testing
 */
export const getTestStore = () => {
  return store;
};

/**
 * Cleanup store for testing
 */
export const cleanupTestStore = () => {
  return Promise.resolve();
};

// =============================================================================
// ADDITIONAL STORE UTILITIES
// =============================================================================

/**
 * Monitor Store Performance
 *
 * Monitors store performance and logs metrics
 */
export function monitorStorePerformance(): void {
  if (!__DEV__) return;

  let lastActionTime = Date.now();
  let actionCount = 0;

  store.subscribe(() => {
    const now = Date.now();
    const timeSinceLastAction = now - lastActionTime;
    actionCount++;

    // Log slow actions
    if (timeSinceLastAction > 100) {
      console.warn(`⚠️ Slow Redux action detected: ${timeSinceLastAction}ms`);
    }

    // Log performance summary every 100 actions
    if (actionCount % 100 === 0) {
      const stats = getStoreStatistics();
      console.log('📊 Store performance summary:', {
        actions: actionCount,
        stateSize: `${(stats.stateSize / 1024).toFixed(2)}KB`,
        cardsCount: stats.slices.cards.count,
        offersCount: stats.slices.offers.count,
      });
    }

    lastActionTime = now;
  });

  console.log('📊 Store performance monitoring enabled');
}



/**
 * Cleanup Store (Compatible with deleted store)
 *
 * Properly cleanup store and listeners
 */
export function cleanupStore(): void {
  try {
    console.log('🧹 Cleaning up Redux store...');

    // Cleanup Realm-Redux bridge
    RealmReduxBridge.cleanup();

    console.log('✅ Redux store cleanup completed');
  } catch (error) {
    console.error('❌ Failed to cleanup Redux store:', error);
  }
}
