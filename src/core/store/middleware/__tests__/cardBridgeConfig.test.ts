/**
 * Card Bridge Configuration Tests
 * 
 * Tests the card-specific Event-Redux Bridge configuration
 * Validates bidirectional communication for card domain
 */

import { configureStore } from '@reduxjs/toolkit';
import { globalEventEmitter } from '../../../events/globalEventEmitter';
import * as EVENT from '../../../../lib/Events.json';
import {
  initializeEventBridge,
  registerBridgeConfig,
  cleanupBridge,
} from '../eventBridge';
import { cardBridgeConfig } from '../cardBridgeConfig';
import { cardDataSlice } from '../../slices/card/cardDataSlice';
import { cardUISlice } from '../../slices/card/cardUISlice';

// Mock store with card slices
const createCardTestStore = () => {
  return configureStore({
    reducer: {
      cardData: cardDataSlice.reducer,
      cardUI: cardUISlice.reducer,
    },
    middleware: (getDefaultMiddleware) => getDefaultMiddleware(),
  });
};

describe('Card Bridge Configuration', () => {
  let store: ReturnType<typeof createCardTestStore>;

  beforeEach(async () => {
    store = createCardTestStore();
    await initializeEventBridge(store);
    registerBridgeConfig(cardBridgeConfig);
  });

  afterEach(() => {
    cleanupBridge();
    globalEventEmitter.removeAllListeners();
  });

  describe('Card Event to Redux Mappings', () => {
    it('should handle card registration events', (done) => {
      const mockCard = {
        id: 'test-card-1',
        masterId: 'master-1',
        personId: 'person-1',
        display: { name: 'Test Card' },
      };

      const unsubscribe = store.subscribe(() => {
        const state = store.getState();
        const cardEntity = state.cardData.entities['test-card-1'];
        
        if (cardEntity) {
          expect(cardEntity.id).toBe('test-card-1');
          expect(cardEntity.display?.name).toBe('Test Card');
          unsubscribe();
          done();
        }
      });

      globalEventEmitter.emit(EVENT.Card.register, {
        cardData: mockCard,
        source: 'external',
      });
    });

    it('should handle card acceptance events', (done) => {
      // First add a card to the store
      store.dispatch(cardDataSlice.actions.addCardFromRealm({
        id: 'test-card-2',
        masterId: 'master-2',
        personId: 'person-1',
      }));

      const unsubscribe = store.subscribe(() => {
        const state = store.getState();
        
        // Check if loading state was set (from accept event)
        if (state.cardData.loading) {
          expect(state.cardData.loading).toBe(true);
          unsubscribe();
          done();
        }
      });

      globalEventEmitter.emit(EVENT.Card.accept, {
        id: 'test-card-2',
        source: 'external',
      });
    });

    it('should handle card view events', (done) => {
      const unsubscribe = store.subscribe(() => {
        const state = store.getState();
        
        if (state.cardUI.selectedCardId === 'test-card-3') {
          expect(state.cardUI.selectedCardId).toBe('test-card-3');
          unsubscribe();
          done();
        }
      });

      globalEventEmitter.emit(EVENT.Card.view, {
        id: 'test-card-3',
        source: 'external',
      });
    });

    it('should handle card deletion events', (done) => {
      // First add a card
      store.dispatch(cardDataSlice.actions.addCardFromRealm({
        id: 'test-card-4',
        masterId: 'master-4',
        personId: 'person-1',
      }));

      const unsubscribe = store.subscribe(() => {
        const state = store.getState();
        
        // Check if card was removed
        if (!state.cardData.entities['test-card-4'] && state.cardData.ids.length === 0) {
          expect(state.cardData.entities['test-card-4']).toBeUndefined();
          unsubscribe();
          done();
        }
      });

      globalEventEmitter.emit(EVENT.Card.deleted, {
        id: 'test-card-4',
        source: 'external',
      });
    });

    it('should respect condition filters', (done) => {
      let stateChanged = false;
      
      const unsubscribe = store.subscribe(() => {
        stateChanged = true;
      });

      // Emit event from Redux source (should be filtered out)
      globalEventEmitter.emit(EVENT.Card.register, {
        cardData: { id: 'test-card-5' },
        source: 'redux', // This should be filtered out
      });

      setTimeout(() => {
        expect(stateChanged).toBe(false);
        unsubscribe();
        done();
      }, 100);
    });
  });

  describe('Redux to Event Mappings', () => {
    it('should emit events when card actions are dispatched', (done) => {
      globalEventEmitter.once(EVENT.Card.accept, (eventData) => {
        expect(eventData.id).toBe('test-card-6');
        expect(eventData.source).toBe('redux');
        expect(eventData.timestamp).toBeDefined();
        done();
      });

      // Simulate fulfilled optimistic action
      store.dispatch({
        type: 'cardData/acceptOptimistic/fulfilled',
        payload: 'test-card-6',
      });
    });

    it('should emit card view events on selection', (done) => {
      globalEventEmitter.once(EVENT.Card.view, (eventData) => {
        expect(eventData.id).toBe('test-card-7');
        expect(eventData.source).toBe('redux');
        done();
      });

      store.dispatch(cardUISlice.actions.selectCard('test-card-7'));
    });

    it('should emit filter change events', (done) => {
      globalEventEmitter.once('card.filter.changed', (eventData) => {
        expect(eventData.filters.status).toBe('active');
        expect(eventData.source).toBe('redux');
        done();
      });

      store.dispatch(cardUISlice.actions.setFilters({ status: 'active' }));
    });

    it('should respect condition filters for Redux actions', (done) => {
      let eventEmitted = false;
      
      globalEventEmitter.once(EVENT.Card.view, () => {
        eventEmitted = true;
      });

      // Select null card (should be filtered out)
      store.dispatch(cardUISlice.actions.selectCard(null));

      setTimeout(() => {
        expect(eventEmitted).toBe(false);
        done();
      }, 100);
    });
  });

  describe('Bridge Configuration Properties', () => {
    it('should have correct configuration structure', () => {
      expect(cardBridgeConfig.name).toBe('CardBridge');
      expect(cardBridgeConfig.description).toContain('Enhanced');
      expect(cardBridgeConfig.eventToRedux).toBeInstanceOf(Array);
      expect(cardBridgeConfig.reduxToEvent).toBeInstanceOf(Array);
      expect(cardBridgeConfig.options).toBeDefined();
    });

    it('should have appropriate configuration options', () => {
      expect(cardBridgeConfig.options.enableLogging).toBe(__DEV__);
      expect(cardBridgeConfig.options.enableMetrics).toBe(true);
      expect(cardBridgeConfig.options.realmFirst).toBe(true);
      expect(cardBridgeConfig.options.debounceDefault).toBe(200);
    });

    it('should have event-to-redux mappings', () => {
      expect(cardBridgeConfig.eventToRedux.length).toBeGreaterThan(0);
      
      // Check for key event mappings
      const eventNames = cardBridgeConfig.eventToRedux.map(mapping => mapping.eventName);
      expect(eventNames).toContain(EVENT.Card.register);
      expect(eventNames).toContain(EVENT.Card.accept);
      expect(eventNames).toContain(EVENT.Card.decline);
      expect(eventNames).toContain(EVENT.Card.view);
    });

    it('should have redux-to-event mappings', () => {
      expect(cardBridgeConfig.reduxToEvent.length).toBeGreaterThan(0);
      
      // Check for key action mappings
      const actionTypes = cardBridgeConfig.reduxToEvent.map(mapping => mapping.actionType);
      expect(actionTypes).toContain('cardData/acceptOptimistic/fulfilled');
      expect(actionTypes).toContain('cardData/declineOptimistic/fulfilled');
      expect(actionTypes).toContain('cardUI/selectCard');
    });
  });

  describe('Integration with Store', () => {
    it('should work with actual card slice actions', (done) => {
      // Test that optimistic actions trigger events
      globalEventEmitter.once(EVENT.Card.accept, (eventData) => {
        expect(eventData.source).toBe('redux');
        done();
      });

      // Dispatch actual optimistic action (simulated fulfilled)
      store.dispatch({
        type: 'cardData/acceptOptimistic/fulfilled',
        payload: 'integration-test-card',
      });
    });

    it('should maintain state consistency', () => {
      const initialState = store.getState();
      
      // Add card via event
      globalEventEmitter.emit(EVENT.Card.register, {
        cardData: {
          id: 'consistency-test-card',
          masterId: 'master-consistency',
          personId: 'person-1',
        },
        source: 'external',
      });

      const finalState = store.getState();
      
      expect(finalState.cardData.ids).toContain('consistency-test-card');
      expect(finalState.cardData.entities['consistency-test-card']).toBeDefined();
    });

    it('should handle rapid event sequences', (done) => {
      let eventCount = 0;
      const targetEvents = 10;

      const unsubscribe = store.subscribe(() => {
        const state = store.getState();
        eventCount = state.cardData.ids.length;
        
        if (eventCount === targetEvents) {
          expect(eventCount).toBe(targetEvents);
          unsubscribe();
          done();
        }
      });

      // Emit multiple card registration events rapidly
      for (let i = 0; i < targetEvents; i++) {
        globalEventEmitter.emit(EVENT.Card.register, {
          cardData: {
            id: `rapid-test-card-${i}`,
            masterId: `master-${i}`,
            personId: 'person-1',
          },
          source: 'external',
        });
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed event data gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Emit event with missing required data
      globalEventEmitter.emit(EVENT.Card.register, {
        // Missing cardData
        source: 'external',
      });

      // Should not crash the application
      expect(store.getState()).toBeDefined();
      
      consoleSpy.mockRestore();
    });

    it('should handle invalid Redux actions gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Dispatch malformed action
      store.dispatch({
        type: 'cardData/acceptOptimistic/fulfilled',
        payload: null, // Invalid payload
      });

      // Should not crash the application
      expect(store.getState()).toBeDefined();
      
      consoleSpy.mockRestore();
    });
  });
});
