/**
 * Event-Redux Bridge Tests
 * 
 * Comprehensive test suite for the Event-Redux Bridge implementation
 * Tests bidirectional communication, performance, and error handling
 */

import { configureStore } from '@reduxjs/toolkit';
import { globalEventEmitter } from '../../../events/globalEventEmitter';
import {
  EventToReduxBridge,
  initializeEventBridge,
  registerBridgeConfig,
  getBridgeHealth,
  getBridgeMetrics,
  cleanupBridge,
  createBridgeConfig,
  createEventToReduxMapping,
  createReduxToEventMapping,
} from '../eventBridge';

// Mock store setup
const mockReducer = (state = { test: 'initial' }, action: any) => {
  switch (action.type) {
    case 'TEST_ACTION':
      return { ...state, test: action.payload };
    case 'EVENT_RECEIVED':
      return { ...state, eventData: action.payload };
    default:
      return state;
  }
};

const createTestStore = () => {
  return configureStore({
    reducer: mockReducer,
    middleware: (getDefaultMiddleware) => getDefaultMiddleware(),
  });
};

describe('Event-Redux Bridge', () => {
  let store: ReturnType<typeof createTestStore>;
  let bridge: EventToReduxBridge;

  beforeEach(async () => {
    store = createTestStore();
    bridge = await initializeEventBridge(store);
  });

  afterEach(() => {
    cleanupBridge();
    globalEventEmitter.removeAllListeners();
  });

  describe('Bridge Initialization', () => {
    it('should initialize bridge successfully', () => {
      expect(bridge).toBeInstanceOf(EventToReduxBridge);
      
      const health = getBridgeHealth();
      expect(health.isInitialized).toBe(true);
      expect(health.isHealthy).toBe(true);
    });

    it('should prevent double initialization', async () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      await initializeEventBridge(store);
      
      expect(consoleSpy).toHaveBeenCalledWith('🔗 Event Bridge already initialized');
      consoleSpy.mockRestore();
    });

    it('should throw error if store not provided', async () => {
      cleanupBridge();
      
      await expect(
        EventToReduxBridge.getInstance().initialize()
      ).rejects.toThrow('Store must be provided before initializing bridge');
    });
  });

  describe('Bridge Configuration', () => {
    it('should register bridge configuration successfully', () => {
      const testConfig = createBridgeConfig(
        'TestBridge',
        'Test bridge configuration',
        [
          createEventToReduxMapping(
            'test.event',
            (eventData: any) => ({ type: 'EVENT_RECEIVED', payload: eventData })
          ),
        ],
        [
          createReduxToEventMapping(
            'TEST_ACTION',
            'test.redux.action'
          ),
        ]
      );

      expect(() => registerBridgeConfig(testConfig)).not.toThrow();
      
      const configs = bridge.getBridgeConfigs();
      expect(configs).toHaveLength(1);
      expect(configs[0].name).toBe('TestBridge');
    });

    it('should handle multiple bridge configurations', () => {
      const config1 = createBridgeConfig('Bridge1', 'First bridge');
      const config2 = createBridgeConfig('Bridge2', 'Second bridge');

      registerBridgeConfig(config1);
      registerBridgeConfig(config2);

      const configs = bridge.getBridgeConfigs();
      expect(configs).toHaveLength(2);
    });
  });

  describe('Event-to-Redux Flow', () => {
    beforeEach(() => {
      const testConfig = createBridgeConfig(
        'TestBridge',
        'Test bridge',
        [
          createEventToReduxMapping(
            'test.event',
            (eventData: any) => ({ type: 'EVENT_RECEIVED', payload: eventData }),
            {
              condition: (eventData, state) => !!eventData.valid,
              transform: (eventData) => ({ ...eventData, transformed: true }),
            }
          ),
        ]
      );

      registerBridgeConfig(testConfig);
    });

    it('should dispatch Redux action when event is emitted', (done) => {
      const unsubscribe = store.subscribe(() => {
        const state = store.getState();
        if (state.eventData) {
          expect(state.eventData.message).toBe('Hello from event');
          expect(state.eventData.transformed).toBe(true);
          unsubscribe();
          done();
        }
      });

      globalEventEmitter.emit('test.event', {
        message: 'Hello from event',
        valid: true,
      });
    });

    it('should respect condition filters', (done) => {
      let actionDispatched = false;
      
      const unsubscribe = store.subscribe(() => {
        actionDispatched = true;
      });

      globalEventEmitter.emit('test.event', {
        message: 'Invalid event',
        valid: false, // Should be filtered out
      });

      setTimeout(() => {
        expect(actionDispatched).toBe(false);
        unsubscribe();
        done();
      }, 100);
    });

    it('should update metrics on successful processing', () => {
      globalEventEmitter.emit('test.event', {
        message: 'Test event',
        valid: true,
      });

      const metrics = getBridgeMetrics();
      expect(metrics.actionsDispatched).toBeGreaterThan(0);
      expect(metrics.averageProcessingTime).toBeGreaterThan(0);
    });
  });

  describe('Redux-to-Event Flow', () => {
    beforeEach(() => {
      const testConfig = createBridgeConfig(
        'TestBridge',
        'Test bridge',
        [],
        [
          createReduxToEventMapping(
            'TEST_ACTION',
            'test.redux.action',
            {
              transform: (action) => ({ 
                ...action.payload, 
                source: 'redux',
                timestamp: new Date().toISOString(),
              }),
              condition: (action, state) => action.payload !== 'invalid',
            }
          ),
        ]
      );

      registerBridgeConfig(testConfig);
    });

    it('should emit event when Redux action is dispatched', (done) => {
      globalEventEmitter.once('test.redux.action', (eventData) => {
        expect(eventData.message).toBe('Hello from Redux');
        expect(eventData.source).toBe('redux');
        expect(eventData.timestamp).toBeDefined();
        done();
      });

      store.dispatch({
        type: 'TEST_ACTION',
        payload: { message: 'Hello from Redux' },
      });
    });

    it('should respect condition filters for Redux actions', (done) => {
      let eventEmitted = false;
      
      globalEventEmitter.once('test.redux.action', () => {
        eventEmitted = true;
      });

      store.dispatch({
        type: 'TEST_ACTION',
        payload: 'invalid', // Should be filtered out
      });

      setTimeout(() => {
        expect(eventEmitted).toBe(false);
        done();
      }, 100);
    });

    it('should update metrics on successful event emission', () => {
      store.dispatch({
        type: 'TEST_ACTION',
        payload: { message: 'Test action' },
      });

      const metrics = getBridgeMetrics();
      expect(metrics.eventsProcessed).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle event listener errors gracefully', () => {
      const testConfig = createBridgeConfig(
        'ErrorBridge',
        'Error test bridge',
        [
          createEventToReduxMapping(
            'error.event',
            () => {
              throw new Error('Test error');
            }
          ),
        ]
      );

      registerBridgeConfig(testConfig);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      globalEventEmitter.emit('error.event', { test: 'data' });

      expect(consoleSpy).toHaveBeenCalled();
      
      const metrics = getBridgeMetrics();
      expect(metrics.errorsCount).toBeGreaterThan(0);
      
      consoleSpy.mockRestore();
    });

    it('should handle Redux-to-event errors gracefully', () => {
      const testConfig = createBridgeConfig(
        'ErrorBridge',
        'Error test bridge',
        [],
        [
          createReduxToEventMapping(
            'ERROR_ACTION',
            'error.redux.event',
            {
              transform: () => {
                throw new Error('Transform error');
              },
            }
          ),
        ]
      );

      registerBridgeConfig(testConfig);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      store.dispatch({ type: 'ERROR_ACTION', payload: 'test' });

      expect(consoleSpy).toHaveBeenCalled();
      
      const metrics = getBridgeMetrics();
      expect(metrics.errorsCount).toBeGreaterThan(0);
      
      consoleSpy.mockRestore();
    });
  });

  describe('Performance Monitoring', () => {
    it('should track processing times', () => {
      const testConfig = createBridgeConfig(
        'PerfBridge',
        'Performance test bridge',
        [
          createEventToReduxMapping(
            'perf.event',
            (eventData: any) => ({ type: 'EVENT_RECEIVED', payload: eventData })
          ),
        ]
      );

      registerBridgeConfig(testConfig);

      // Emit multiple events
      for (let i = 0; i < 5; i++) {
        globalEventEmitter.emit('perf.event', { index: i });
      }

      const metrics = getBridgeMetrics();
      expect(metrics.averageProcessingTime).toBeGreaterThan(0);
      expect(metrics.actionsDispatched).toBe(5);
    });

    it('should maintain processing time history', () => {
      const testConfig = createBridgeConfig(
        'HistoryBridge',
        'History test bridge',
        [
          createEventToReduxMapping(
            'history.event',
            (eventData: any) => ({ type: 'EVENT_RECEIVED', payload: eventData })
          ),
        ]
      );

      registerBridgeConfig(testConfig);

      // Emit many events to test history management
      for (let i = 0; i < 150; i++) {
        globalEventEmitter.emit('history.event', { index: i });
      }

      const metrics = getBridgeMetrics();
      expect(metrics.actionsDispatched).toBe(150);
      expect(metrics.averageProcessingTime).toBeGreaterThan(0);
    });
  });

  describe('Health Monitoring', () => {
    it('should report healthy status when functioning normally', () => {
      const health = getBridgeHealth();
      
      expect(health.isInitialized).toBe(true);
      expect(health.isHealthy).toBe(true);
      expect(health.timestamp).toBeInstanceOf(Date);
      expect(health.metrics).toBeDefined();
    });

    it('should detect unhealthy status with high error count', () => {
      // Simulate high error count
      const testConfig = createBridgeConfig(
        'ErrorBridge',
        'Error bridge',
        [
          createEventToReduxMapping(
            'error.event',
            () => {
              throw new Error('Simulated error');
            }
          ),
        ]
      );

      registerBridgeConfig(testConfig);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Generate multiple errors
      for (let i = 0; i < 15; i++) {
        globalEventEmitter.emit('error.event', { index: i });
      }

      const health = getBridgeHealth();
      expect(health.isHealthy).toBe(false);
      
      consoleSpy.mockRestore();
    });
  });

  describe('Cleanup', () => {
    it('should cleanup all resources properly', () => {
      const testConfig = createBridgeConfig(
        'CleanupBridge',
        'Cleanup test bridge',
        [
          createEventToReduxMapping(
            'cleanup.event',
            (eventData: any) => ({ type: 'EVENT_RECEIVED', payload: eventData })
          ),
        ]
      );

      registerBridgeConfig(testConfig);

      cleanupBridge();

      const health = getBridgeHealth();
      expect(health.isInitialized).toBe(false);
    });
  });
});
