/**
 * Card Bridge Configuration for Enhanced Event-Redux Bridge
 * 
 * Adapts existing card event mappings to work with the new bridge architecture
 * Provides bidirectional communication between card events and Redux state
 */

import { PayloadAction } from '@reduxjs/toolkit';
import * as EVENT from '../../../lib/Events.json';
import {
  // Card data actions (optimistic operations)
  createCardOptimistic,
  acceptCardOptimistic,
  declineCardOptimistic,
  updateCardOptimistic,
  clearError,
  addCardFromRealm,
  updateCardFromRealm,
  removeCardFromRealm,
  setRealmConnectionStatus,
} from '../slices/card/cardDataSlice';

import {
  // Card UI actions
  selectCard,
  setFilters,
  clearFilters,
} from '../slices/card/cardUISlice';

import {
  createBridgeConfig,
  createEventToReduxMapping,
  createReduxToEventMapping,
  type BridgeConfig,
} from './eventBridge';

// =============================================================================
// CARD EVENT TO REDUX MAPPINGS
// =============================================================================

/**
 * Event-to-Redux mappings for card domain
 * Converts legacy event system events into Redux actions
 */
const cardEventToReduxMappings = [
  // Card registration events
  createEventToReduxMapping(
    EVENT.Card.register,
    (eventData: any) => {
      if (eventData.cardData) {
        return addCardFromRealm(eventData.cardData);
      }
      return setRealmConnectionStatus(true);
    },
    {
      condition: (eventData, state) => eventData.source !== 'redux',
      debounce: 500,
    }
  ),

  // Card acceptance events
  createEventToReduxMapping(
    EVENT.Card.accept,
    (eventData: any) => {
      if (eventData.cardData) {
        return updateCardFromRealm(eventData.cardData);
      }
      if (eventData.id && eventData.source !== 'redux') {
        return acceptCardOptimistic(eventData.id);
      }
      return clearError();
    },
    {
      condition: (eventData, state) => !!eventData.id || !!eventData.cardData,
      debounce: 300,
    }
  ),

  // Card decline events
  createEventToReduxMapping(
    EVENT.Card.decline,
    (eventData: any) => {
      if (eventData.cardData) {
        return updateCardFromRealm(eventData.cardData);
      }
      if (eventData.id && eventData.source !== 'redux') {
        return declineCardOptimistic(eventData.id);
      }
      return clearError();
    },
    {
      condition: (eventData, state) => !!eventData.id || !!eventData.cardData,
      debounce: 300,
    }
  ),

  // Card view events
  createEventToReduxMapping(
    EVENT.Card.view,
    (eventData: any) => selectCard(eventData.id),
    {
      condition: (eventData, state) => !!eventData.id,
      debounce: 100,
    }
  ),

  // Card creation events
  createEventToReduxMapping(
    EVENT.Card.new,
    (eventData: any) => {
      if (eventData.cardData) {
        return addCardFromRealm(eventData.cardData);
      }
      return setRealmConnectionStatus(true);
    },
    {
      condition: (eventData, state) => !!eventData.cardData,
      debounce: 500,
    }
  ),

  // Card update events
  createEventToReduxMapping(
    EVENT.Card.updated,
    (eventData: any) => {
      if (eventData.cardData) {
        return updateCardFromRealm(eventData.cardData);
      }
      return clearError();
    },
    {
      condition: (eventData, state) => !!eventData.cardData,
      debounce: 200,
    }
  ),

  // Card deletion events
  createEventToReduxMapping(
    EVENT.Card.deleted,
    (eventData: any) => {
      const cardId = eventData.id || (eventData.ids && eventData.ids[0]);
      return removeCardFromRealm(cardId);
    },
    {
      condition: (eventData, state) => !!eventData.id || (eventData.ids && eventData.ids.length > 0),
      debounce: 300,
    }
  ),
];

// =============================================================================
// CARD REDUX TO EVENT MAPPINGS
// =============================================================================

/**
 * Redux-to-Event mappings for card domain
 * Converts Redux actions into legacy event system events
 */
const cardReduxToEventMappings = [
  // Create card optimistic fulfilled → card register event
  createReduxToEventMapping(
    createCardOptimistic.fulfilled.type,
    EVENT.Card.register,
    {
      transform: (action: PayloadAction<any>) => ({
        id: action.payload?.id,
        masterId: action.payload?.masterId,
        source: 'redux',
        timestamp: new Date().toISOString(),
        cardData: action.payload,
      }),
      condition: (action, state) => !!action.payload?.id,
    }
  ),

  // Accept card optimistic fulfilled → card accept event
  createReduxToEventMapping(
    acceptCardOptimistic.fulfilled.type,
    EVENT.Card.accept,
    {
      transform: (action: PayloadAction<string>) => ({
        id: action.payload,
        source: 'redux',
        timestamp: new Date().toISOString(),
      }),
      condition: (action, state) => !!action.payload,
    }
  ),

  // Decline card optimistic fulfilled → card decline event
  createReduxToEventMapping(
    declineCardOptimistic.fulfilled.type,
    EVENT.Card.decline,
    {
      transform: (action: PayloadAction<string>) => ({
        id: action.payload,
        source: 'redux',
        timestamp: new Date().toISOString(),
      }),
      condition: (action, state) => !!action.payload,
    }
  ),

  // Card selection → card view event
  createReduxToEventMapping(
    selectCard.type,
    EVENT.Card.view,
    {
      transform: (action: PayloadAction<string | null>) => ({
        id: action.payload,
        source: 'redux',
        timestamp: new Date().toISOString(),
      }),
      condition: (action, state) => action.payload !== null,
    }
  ),

  // Filter changes → analytics events (if needed)
  createReduxToEventMapping(
    setFilters.type,
    'card.filter.changed', // Custom analytics event
    {
      transform: (action: PayloadAction<any>) => ({
        filters: action.payload,
        source: 'redux',
        timestamp: new Date().toISOString(),
      }),
    }
  ),
];

// =============================================================================
// BRIDGE CONFIGURATION
// =============================================================================

/**
 * Complete card bridge configuration
 * Integrates with the enhanced Event-Redux Bridge
 */
export const cardBridgeConfig: BridgeConfig = createBridgeConfig(
  'CardBridge',
  'Bidirectional bridge for card events and Redux actions (Enhanced)',
  cardEventToReduxMappings,
  cardReduxToEventMappings,
  {
    enableLogging: __DEV__,
    enableMetrics: true,
    debounceDefault: 200,
    maxEventQueueSize: 100,
    realmFirst: true, // Realm-first architecture
  }
);

/**
 * Export individual mappings for testing and debugging
 */
export { cardEventToReduxMappings, cardReduxToEventMappings };
