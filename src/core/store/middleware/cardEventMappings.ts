/**
 * Card Event-Redux Bridge Mappings
 *
 * Defines bidirectional mappings between card events and Redux actions
 * Part of the Events-Redux architecture for card management
 * Updated for refactored slice structure
 */

import { AppDispatch } from '../index';
import * as EVENT from '../../../lib/Events.json';
import {
  // Card data actions (optimistic operations)
  createCardOptimistic,
  acceptCardOptimistic,
  declineCardOptimistic,
  updateCardOptimistic,
  clearError,
  syncFromRealm,
  addCardFromRealm,
  updateCardFromRealm,
  removeCardFromRealm,
  setRealmConnectionStatus,
} from '../slices/card/cardDataSlice';
import {
  // Card UI actions
  selectCard,
  setFilters,
  clearFilters,
  resetUI,
} from '../slices/card/cardUISlice';

// =============================================================================
// EVENT TO REDUX MAPPINGS
// =============================================================================

/**
 * Maps card events to Redux actions
 * These events come from the legacy event system and trigger Redux state updates
 */
export const cardEventToReduxMappings = [
  // Card registration events
  {
    event: EVENT.Card.register,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Card registered event received:', eventData);

      // Clear any previous errors
      dispatch(clearError());

      // Trigger Realm sync to update Redux state
      // The new architecture uses Realm-first approach
      if (eventData.source !== 'redux') {
        // Trigger a sync from Realm to Redux
        // This will be handled by the Realm bridge middleware
        dispatch(setRealmConnectionStatus(true));
      }

      // If we have card data, add it directly to Redux
      if (eventData.cardData) {
        dispatch(addCardFromRealm(eventData.cardData));
      }
    },
    debounce: 500,
  },
  
  // Card acceptance events
  {
    event: EVENT.Card.accept,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Card accepted event received:', eventData);

      // Clear any previous errors
      dispatch(clearError());

      // If this event comes from outside Redux, trigger optimistic update
      if (eventData.source !== 'redux' && eventData.id) {
        // Use optimistic accept action which updates Realm first
        dispatch(acceptCardOptimistic(eventData.id));
      }

      // If we have updated card data, update Redux directly
      if (eventData.cardData) {
        dispatch(updateCardFromRealm(eventData.cardData));
      }
    },
    debounce: 300,
  },
  
  // Card decline events
  {
    event: EVENT.Card.decline,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Card declined event received:', eventData);

      // Clear any previous errors
      dispatch(clearError());

      // If this event comes from outside Redux, trigger optimistic update
      if (eventData.source !== 'redux' && eventData.id) {
        // Use optimistic decline action which updates Realm first
        dispatch(declineCardOptimistic(eventData.id));
      }

      // If we have updated card data, update Redux directly
      if (eventData.cardData) {
        dispatch(updateCardFromRealm(eventData.cardData));
      }
    },
    debounce: 300,
  },
  
  // Card view events (for analytics and state tracking)
  {
    event: EVENT.Card.view,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Card viewed event received:', eventData);

      // Select the viewed card in UI state
      if (eventData.id) {
        dispatch(selectCard(eventData.id));
      }
    },
    debounce: 100,
  },
  
  // Card creation events
  {
    event: EVENT.Card.new,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 New card event received:', eventData);

      // Clear any previous errors
      dispatch(clearError());

      // If we have new card data, add it to Redux
      if (eventData.cardData) {
        dispatch(addCardFromRealm(eventData.cardData));
      }

      // Trigger Realm sync to ensure we have the latest data
      dispatch(setRealmConnectionStatus(true));
    },
    debounce: 500,
  },
  
  // Card update events from Realm
  {
    event: EVENT.Card.updated,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Card updated event received:', eventData);

      // If we have updated card data, update Redux directly
      if (eventData.cardData) {
        dispatch(updateCardFromRealm(eventData.cardData));
      } else if (eventData.cards && Array.isArray(eventData.cards)) {
        // Handle multiple card updates
        eventData.cards.forEach((card: any) => {
          dispatch(updateCardFromRealm(card));
        });
      }
    },
    debounce: 200,
  },
  
  // Card deletion events
  {
    event: EVENT.Card.deleted,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Card deleted event received:', eventData);

      const cardIds = eventData.ids || [eventData.id];

      // Remove cards from Redux state
      cardIds.forEach((cardId: string) => {
        if (cardId) {
          dispatch(removeCardFromRealm(cardId));
        }
      });
    },
    debounce: 300,
  },
];

// =============================================================================
// REDUX TO EVENT MAPPINGS
// =============================================================================

/**
 * Maps Redux actions to events
 * These Redux actions trigger events in the legacy event system
 */
export const cardReduxToEventMappings = [
  // Create card action triggers card registration event
  {
    actionType: 'cardData/createOptimistic/fulfilled',
    handler: (action: any, emit: (event: string, data: any) => void) => {
      const card = action.payload;
      if (card && card.id) {
        emit(EVENT.Card.register, {
          id: card.id,
          masterId: card.masterId,
          source: 'redux',
          timestamp: new Date().toISOString(),
          cardData: card,
        });
      }
    },
  },

  // Accept card action triggers card acceptance event
  {
    actionType: 'cardData/acceptOptimistic/fulfilled',
    handler: (action: any, emit: (event: string, data: any) => void) => {
      const cardId = action.payload;
      if (cardId) {
        emit(EVENT.Card.accept, {
          id: cardId,
          source: 'redux',
          timestamp: new Date().toISOString(),
        });
      }
    },
  },

  // Decline card action triggers card decline event
  {
    actionType: 'cardData/declineOptimistic/fulfilled',
    handler: (action: any, emit: (event: string, data: any) => void) => {
      const cardId = action.payload;
      if (cardId) {
        emit(EVENT.Card.decline, {
          id: cardId,
          source: 'redux',
          timestamp: new Date().toISOString(),
        });
      }
    },
  },

  // Card selection triggers card view event
  {
    actionType: 'cardUI/selectCard',
    handler: (action: any, emit: (event: string, data: any) => void) => {
      const cardId = action.payload;
      if (cardId) {
        emit(EVENT.Card.view, {
          id: cardId,
          source: 'redux',
          timestamp: new Date().toISOString(),
        });
      }
    },
    condition: (action: any) => action.payload !== null, // Only emit when selecting a card, not deselecting
  },
];

// =============================================================================
// CARD EVENT BRIDGE CONFIGURATION
// =============================================================================

/**
 * Complete card event bridge configuration
 * Combines event-to-redux and redux-to-event mappings
 * Updated for refactored slice structure
 */
export const cardEventBridgeConfig = {
  name: 'CardEventBridge',
  description: 'Bidirectional bridge for card events and Redux actions (Refactored)',
  eventToRedux: cardEventToReduxMappings,
  reduxToEvent: cardReduxToEventMappings,

  // Configuration options
  options: {
    enableLogging: __DEV__,
    enableMetrics: true,
    debounceDefault: 200,
    maxEventQueueSize: 100,
    realmFirst: true, // New: Indicates Realm-first architecture
  },

  // Health check function
  healthCheck: () => ({
    isHealthy: true,
    eventMappings: cardEventToReduxMappings.length,
    actionMappings: cardReduxToEventMappings.length,
    lastActivity: Date.now(),
    architecture: 'refactored', // New: Indicates refactored architecture
    sliceStructure: 'cardData + cardUI', // New: Documents slice structure
  }),
};
