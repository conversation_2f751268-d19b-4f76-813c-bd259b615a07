/**
 * Event-Redux Bridge - Complete Implementation
 *
 * Bidirectional bridge connecting the proven V6 event system with modern Redux patterns.
 * Preserves business logic in events while enabling modern UI state management.
 */

import { createListenerMiddleware, isAnyOf, PayloadAction } from '@reduxjs/toolkit';
import type { AppStore, BridgeHealthCheck } from '../types';
import { globalEventEmitter } from '../../events/globalEventEmitter';
import { EVENT_TYPES, TypedEvents, BaseEventData } from '../../events/eventDefinitions';

// =============================================================================
// BRIDGE TYPES AND INTERFACES
// =============================================================================

/**
 * Event-to-Redux mapping configuration
 */
export interface EventToReduxMapping {
  eventName: string;
  actionCreator: (eventData: any) => PayloadAction<any>;
  transform?: (eventData: any) => any;
  debounce?: number;
  condition?: (eventData: any, state: any) => boolean;
}

/**
 * Redux-to-Event mapping configuration
 */
export interface ReduxToEventMapping {
  actionType: string;
  eventName: string;
  transform?: (action: PayloadAction<any>) => any;
  condition?: (action: PayloadAction<any>, state: any) => boolean;
}

/**
 * Bridge configuration
 */
export interface BridgeConfig {
  name: string;
  description: string;
  eventToRedux: EventToReduxMapping[];
  reduxToEvent: ReduxToEventMapping[];
  options: {
    enableLogging: boolean;
    enableMetrics: boolean;
    debounceDefault: number;
    maxEventQueueSize: number;
    realmFirst: boolean;
  };
}

/**
 * Bridge metrics for monitoring
 */
export interface BridgeMetrics {
  eventsProcessed: number;
  actionsDispatched: number;
  errorsCount: number;
  averageProcessingTime: number;
  lastActivity: Date;
  queueSize: number;
}

// =============================================================================
// ENHANCED EVENT BRIDGE MIDDLEWARE
// =============================================================================

/**
 * Enhanced Event-Redux Bridge Middleware
 */
export const eventBridgeMiddleware = createListenerMiddleware();

// =============================================================================
// CORE BRIDGE IMPLEMENTATION
// =============================================================================

/**
 * Enhanced Event-Redux Bridge
 * Provides bidirectional communication between events and Redux
 */
export class EventToReduxBridge {
  private static instance: EventToReduxBridge | null = null;
  private store: AppStore | null = null;
  private isInitialized: boolean = false;
  private eventListeners: Map<string, Function> = new Map();
  private bridgeConfigs: Map<string, BridgeConfig> = new Map();
  private metrics: BridgeMetrics = {
    eventsProcessed: 0,
    actionsDispatched: 0,
    errorsCount: 0,
    averageProcessingTime: 0,
    lastActivity: new Date(),
    queueSize: 0,
  };
  private processingTimes: number[] = [];
  private metricsTimer: NodeJS.Timeout | null = null;

  static getInstance(storeInstance?: AppStore): EventToReduxBridge {
    if (!EventToReduxBridge.instance) {
      EventToReduxBridge.instance = new EventToReduxBridge();
      if (storeInstance) {
        EventToReduxBridge.instance.store = storeInstance;
      }
    }
    return EventToReduxBridge.instance;
  }

  /**
   * Initialize the bridge with store and event listeners
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn('🔗 Event Bridge already initialized');
      return;
    }

    if (!this.store) {
      throw new Error('Store must be provided before initializing bridge');
    }

    try {
      // Set up error handling
      this.setupErrorHandling();

      // Initialize metrics collection
      this.initializeMetrics();

      this.isInitialized = true;

      if (__DEV__) {
        console.log('🔗 Event-Redux Bridge initialized successfully');
        console.log('📊 Bridge metrics collection started');
      }
    } catch (error) {
      console.error('❌ Failed to initialize Event Bridge:', error);
      throw error;
    }
  }

  /**
   * Register a bridge configuration for specific domain (cards, offers, etc.)
   */
  registerBridgeConfig(config: BridgeConfig): void {
    if (!this.isInitialized) {
      throw new Error('Bridge must be initialized before registering configurations');
    }

    try {
      // Store the configuration
      this.bridgeConfigs.set(config.name, config);

      // Set up event-to-redux mappings
      this.setupEventToReduxMappings(config);

      // Set up redux-to-event mappings
      this.setupReduxToEventMappings(config);

      if (__DEV__ && config.options.enableLogging) {
        console.log(`🔗 Registered bridge config: ${config.name}`);
        console.log(`📥 Event→Redux mappings: ${config.eventToRedux.length}`);
        console.log(`📤 Redux→Event mappings: ${config.reduxToEvent.length}`);
      }
    } catch (error) {
      console.error(`❌ Failed to register bridge config ${config.name}:`, error);
      this.metrics.errorsCount++;
      throw error;
    }
  }

  /**
   * Set up event-to-redux mappings from configuration
   */
  private setupEventToReduxMappings(config: BridgeConfig): void {
    config.eventToRedux.forEach(mapping => {
      const listener = this.createEventListener(mapping, config);

      // Register with global event emitter
      globalEventEmitter.on(mapping.eventName, listener);

      // Store listener for cleanup
      this.eventListeners.set(`${config.name}:${mapping.eventName}`, listener);
    });
  }

  /**
   * Set up redux-to-event mappings from configuration
   */
  private setupReduxToEventMappings(config: BridgeConfig): void {
    config.reduxToEvent.forEach(mapping => {
      // Use RTK listener middleware for Redux action monitoring
      eventBridgeMiddleware.startListening({
        actionCreator: (action: PayloadAction<any>) => action.type === mapping.actionType,
        effect: (action, listenerApi) => {
          this.handleReduxToEvent(mapping, action, config, listenerApi.getState());
        },
      });
    });
  }

  /**
   * Create event listener for event-to-redux mapping
   */
  private createEventListener(mapping: EventToReduxMapping, config: BridgeConfig): Function {
    return (eventData: any) => {
      const startTime = performance.now();

      try {
        // Check condition if provided
        if (mapping.condition && !mapping.condition(eventData, this.store?.getState())) {
          return;
        }

        // Transform data if transformer provided
        const transformedData = mapping.transform ? mapping.transform(eventData) : eventData;

        // Create and dispatch Redux action
        const action = mapping.actionCreator(transformedData);
        this.store?.dispatch(action);

        // Update metrics
        this.updateMetrics(startTime, true);
        this.metrics.actionsDispatched++;

        if (__DEV__ && config.options.enableLogging) {
          console.log(`🔗 Event→Redux: ${mapping.eventName} → ${action.type}`);
        }
      } catch (error) {
        this.updateMetrics(startTime, false);
        this.metrics.errorsCount++;
        console.error(`❌ Event→Redux error for ${mapping.eventName}:`, error);
      }
    };
  }

  /**
   * Handle redux-to-event mapping
   */
  private handleReduxToEvent(
    mapping: ReduxToEventMapping,
    action: PayloadAction<any>,
    config: BridgeConfig,
    state: any
  ): void {
    const startTime = performance.now();

    try {
      // Check condition if provided
      if (mapping.condition && !mapping.condition(action, state)) {
        return;
      }

      // Transform action if transformer provided
      const eventData = mapping.transform ? mapping.transform(action) : action.payload;

      // Emit event
      globalEventEmitter.safeEmit(mapping.eventName, eventData);

      // Update metrics
      this.updateMetrics(startTime, true);
      this.metrics.eventsProcessed++;

      if (__DEV__ && config.options.enableLogging) {
        console.log(`🔗 Redux→Event: ${action.type} → ${mapping.eventName}`);
      }
    } catch (error) {
      this.updateMetrics(startTime, false);
      this.metrics.errorsCount++;
      console.error(`❌ Redux→Event error for ${action.type}:`, error);
    }
  }

  /**
   * Set up error handling for the bridge
   */
  private setupErrorHandling(): void {
    // Handle global event emitter errors
    globalEventEmitter.on('error', (error: Error) => {
      console.error('🔗 Bridge: Global event emitter error:', error);
      this.metrics.errorsCount++;
    });

    // Handle unhandled promise rejections in bridge operations
    if (typeof process !== 'undefined') {
      process.on('unhandledRejection', (reason, promise) => {
        console.error('🔗 Bridge: Unhandled promise rejection:', reason);
        this.metrics.errorsCount++;
      });
    }
  }

  /**
   * Initialize metrics collection
   */
  private initializeMetrics(): void {
    // Reset metrics
    this.metrics = {
      eventsProcessed: 0,
      actionsDispatched: 0,
      errorsCount: 0,
      averageProcessingTime: 0,
      lastActivity: new Date(),
      queueSize: 0,
    };

    this.processingTimes = [];

    // Set up periodic metrics logging in development
    if (__DEV__) {
      this.metricsTimer = setInterval(() => {
        this.logMetrics();
      }, 30000); // Log every 30 seconds
    }
  }

  /**
   * Update processing metrics
   */
  private updateMetrics(startTime: number, success: boolean): void {
    const processingTime = performance.now() - startTime;

    if (success) {
      this.processingTimes.push(processingTime);

      // Keep only last 100 measurements for average calculation
      if (this.processingTimes.length > 100) {
        this.processingTimes.shift();
      }

      // Calculate average processing time
      this.metrics.averageProcessingTime =
        this.processingTimes.reduce((sum, time) => sum + time, 0) / this.processingTimes.length;
    }

    this.metrics.lastActivity = new Date();
  }

  /**
   * Log current metrics (development only)
   */
  private logMetrics(): void {
    if (!__DEV__) return;

    console.group('🔗 Event Bridge Metrics');
    console.log(`📊 Events processed: ${this.metrics.eventsProcessed}`);
    console.log(`📊 Actions dispatched: ${this.metrics.actionsDispatched}`);
    console.log(`📊 Errors: ${this.metrics.errorsCount}`);
    console.log(`📊 Avg processing time: ${this.metrics.averageProcessingTime.toFixed(2)}ms`);
    console.log(`📊 Registered configs: ${this.bridgeConfigs.size}`);
    console.log(`📊 Active listeners: ${this.eventListeners.size}`);
    console.groupEnd();
  }

  /**
   * Get current bridge metrics
   */
  getMetrics(): BridgeMetrics {
    return { ...this.metrics };
  }

  /**
   * Get registered bridge configurations
   */
  getBridgeConfigs(): BridgeConfig[] {
    return Array.from(this.bridgeConfigs.values());
  }

  /**
   * Check if bridge is healthy
   */
  isHealthy(): boolean {
    const now = Date.now();
    const lastActivity = this.metrics.lastActivity.getTime();
    const timeSinceActivity = now - lastActivity;

    return (
      this.isInitialized &&
      this.store !== null &&
      timeSinceActivity < 300000 && // 5 minutes
      this.metrics.errorsCount < 10 // Less than 10 errors
    );
  }

  /**
   * Cleanup bridge resources
   */
  cleanup(): void {
    try {
      // Clear metrics timer
      if (this.metricsTimer) {
        clearInterval(this.metricsTimer);
        this.metricsTimer = null;
      }

      // Remove all event listeners
      this.eventListeners.forEach((listener, key) => {
        const [configName, eventName] = key.split(':');
        globalEventEmitter.removeListener(eventName, listener);
      });

      this.eventListeners.clear();
      this.bridgeConfigs.clear();

      // Clear middleware listeners
      eventBridgeMiddleware.clearListeners();

      this.isInitialized = false;

      if (__DEV__) {
        console.log('🧹 Event Bridge cleaned up successfully');
      }
    } catch (error) {
      console.error('❌ Error during bridge cleanup:', error);
    }
  }
}

// =============================================================================
// PUBLIC API FUNCTIONS
// =============================================================================

/**
 * Initialize Event Bridge with store
 */
export async function initializeEventBridge(storeInstance: AppStore): Promise<EventToReduxBridge> {
  const bridge = EventToReduxBridge.getInstance(storeInstance);
  await bridge.initialize();
  return bridge;
}

/**
 * Register a bridge configuration
 */
export function registerBridgeConfig(config: BridgeConfig): void {
  const bridge = EventToReduxBridge.getInstance();
  bridge.registerBridgeConfig(config);
}

/**
 * Get bridge health status
 */
export function getBridgeHealth(): BridgeHealthCheck {
  const bridge = EventToReduxBridge.getInstance();
  return {
    isInitialized: bridge.isHealthy(),
    isHealthy: bridge.isHealthy(),
    timestamp: new Date(),
    metrics: bridge.getMetrics(),
  };
}

/**
 * Get bridge metrics
 */
export function getBridgeMetrics(): BridgeMetrics {
  const bridge = EventToReduxBridge.getInstance();
  return bridge.getMetrics();
}

/**
 * Get all registered bridge configurations
 */
export function getBridgeConfigs(): BridgeConfig[] {
  const bridge = EventToReduxBridge.getInstance();
  return bridge.getBridgeConfigs();
}

/**
 * Cleanup bridge (for testing or shutdown)
 */
export function cleanupBridge(): void {
  const bridge = EventToReduxBridge.getInstance();
  bridge.cleanup();
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

/**
 * Create a simple event-to-redux mapping
 */
export function createEventToReduxMapping(
  eventName: string,
  actionCreator: (eventData: any) => PayloadAction<any>,
  options: {
    transform?: (eventData: any) => any;
    debounce?: number;
    condition?: (eventData: any, state: any) => boolean;
  } = {}
): EventToReduxMapping {
  return {
    eventName,
    actionCreator,
    ...options,
  };
}

/**
 * Create a simple redux-to-event mapping
 */
export function createReduxToEventMapping(
  actionType: string,
  eventName: string,
  options: {
    transform?: (action: PayloadAction<any>) => any;
    condition?: (action: PayloadAction<any>, state: any) => boolean;
  } = {}
): ReduxToEventMapping {
  return {
    actionType,
    eventName,
    ...options,
  };
}

/**
 * Create a bridge configuration with default options
 */
export function createBridgeConfig(
  name: string,
  description: string,
  eventToRedux: EventToReduxMapping[] = [],
  reduxToEvent: ReduxToEventMapping[] = [],
  options: Partial<BridgeConfig['options']> = {}
): BridgeConfig {
  return {
    name,
    description,
    eventToRedux,
    reduxToEvent,
    options: {
      enableLogging: __DEV__,
      enableMetrics: true,
      debounceDefault: 200,
      maxEventQueueSize: 100,
      realmFirst: true,
      ...options,
    },
  };
}
