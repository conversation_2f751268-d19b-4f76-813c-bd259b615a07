/**
 * Offer Event-Redux Bridge Mappings
 *
 * Defines bidirectional mappings between offer events and Redux actions
 * Part of the Events-Redux architecture for offer management
 * Updated for refactored slice structure
 */

import { AppDispatch } from '../index';
import * as EVENT from '../../../lib/Events.json';
import {
  // Offer data actions (optimistic operations)
  createOfferOptimistic,
  updateOfferOptimistic,
  redeemOfferOptimistic,
  clearError,
  syncFromRealm,
  addOfferFromRealm,
  updateOfferFromRealm,
  removeOfferFromRealm,
  syncRedemptionsFromRealm,
  addRedemptionFromRealm,
  setRealmConnectionStatus,
} from '../slices/offer/offerDataSlice';
import {
  // Offer UI actions
  selectOffer,
  setFilters,
  clearFilters,
  resetUI,
} from '../slices/offer/offerUISlice';

// =============================================================================
// EVENT TO REDUX MAPPINGS
// =============================================================================

/**
 * Maps offer events to Redux actions
 * These events come from the legacy event system and trigger Redux state updates
 */
export const offerEventToReduxMappings = [
  // Offer received events
  {
    event: EVENT.Offer.received,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Offer received event received:', eventData);

      // Clear any previous errors
      dispatch(clearError());

      // Trigger Realm sync to update Redux state
      // The new architecture uses Realm-first approach
      if (eventData.source !== 'redux') {
        // Trigger a sync from Realm to Redux
        // This will be handled by the Realm bridge middleware
        dispatch(setRealmConnectionStatus(true));
      }

      // If we have offer data, add it directly to Redux
      if (eventData.offerData) {
        dispatch(addOfferFromRealm(eventData.offerData));
      }
    },
    debounce: 500,
  },
  
  // Offer redemption events
  {
    event: EVENT.Offer.redeemed,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Offer redeemed event received:', eventData);

      // Clear any previous errors
      dispatch(clearError());

      // If this event comes from outside Redux, trigger optimistic update
      if (eventData.source !== 'redux' && eventData.id) {
        // Use optimistic redeem action which updates Realm first
        dispatch(redeemOfferOptimistic({
          offerId: eventData.id,
          cardId: eventData.cardId,
          count: eventData.count || 1,
        }));
      }

      // Add redemption data to tracking
      if (eventData.redemptionData) {
        dispatch(addRedemptionFromRealm(eventData.redemptionData));
      }

      // If we have updated offer data, update Redux directly
      if (eventData.offerData) {
        dispatch(updateOfferFromRealm(eventData.offerData));
      }
    },
    debounce: 300,
  },
  
  // Offer sharing events
  {
    event: EVENT.Offer.shared,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Offer shared event received:', eventData);

      // Clear any previous errors
      dispatch(clearError());

      // If we have updated offer data, update Redux directly
      if (eventData.offerData) {
        dispatch(updateOfferFromRealm(eventData.offerData));
      }

      // Trigger Realm sync to ensure we have the latest data
      dispatch(setRealmConnectionStatus(true));
    },
    debounce: 300,
  },
  
  // Offer creation events
  {
    event: EVENT.Offer.created,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Offer created event received:', eventData);

      // Clear any previous errors
      dispatch(clearError());

      // If we have new offer data, add it to Redux
      if (eventData.offerData) {
        dispatch(addOfferFromRealm(eventData.offerData));
      } else if (eventData.offers && Array.isArray(eventData.offers)) {
        // Handle multiple offer creations
        eventData.offers.forEach((offer: any) => {
          dispatch(addOfferFromRealm(offer));
        });
      }

      // Trigger Realm sync to ensure we have the latest data
      dispatch(setRealmConnectionStatus(true));
    },
    debounce: 500,
  },
  
  // Offer update events from Realm
  {
    event: EVENT.Offer.updated,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Offer updated event received:', eventData);

      // If we have updated offer data, update Redux directly
      if (eventData.offerData) {
        dispatch(updateOfferFromRealm(eventData.offerData));
      } else if (eventData.offers && Array.isArray(eventData.offers)) {
        // Handle multiple offer updates
        eventData.offers.forEach((offer: any) => {
          dispatch(updateOfferFromRealm(offer));
        });
      }
    },
    debounce: 200,
  },
  
  // Offer deletion events
  {
    event: EVENT.Offer.deleted,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Offer deleted event received:', eventData);

      const offerIds = eventData.ids || [eventData.id];

      // Remove offers from Redux state
      offerIds.forEach((offerId: string) => {
        if (offerId) {
          dispatch(removeOfferFromRealm(offerId));
        }
      });
    },
    debounce: 300,
  },
  
  // Offer expiration events
  {
    event: EVENT.Offer.expired,
    handler: (dispatch: AppDispatch, eventData: any) => {
      console.log('🎯 Offer expired event received:', eventData);

      // If we have updated offer data (with expired status), update Redux directly
      if (eventData.offerData) {
        dispatch(updateOfferFromRealm(eventData.offerData));
      } else if (eventData.offers && Array.isArray(eventData.offers)) {
        // Handle multiple offer expirations
        eventData.offers.forEach((offer: any) => {
          dispatch(updateOfferFromRealm(offer));
        });
      }

      // Trigger Realm sync to ensure we have the latest data
      dispatch(setRealmConnectionStatus(true));
    },
    debounce: 300,
  },
];

// =============================================================================
// REDUX TO EVENT MAPPINGS
// =============================================================================

/**
 * Maps Redux actions to events
 * These Redux actions trigger events in the legacy event system
 */
export const offerReduxToEventMappings = [
  // Create offer action triggers offer creation event
  {
    actionType: 'offerData/createOptimistic/fulfilled',
    handler: (action: any, emit: (event: string, data: any) => void) => {
      const offer = action.payload;
      if (offer && offer.id) {
        emit(EVENT.Offer.created, {
          id: offer.id,
          masterId: offer.masterId,
          cardId: offer.cardId,
          source: 'redux',
          timestamp: new Date().toISOString(),
          offerData: offer,
        });
      }
    },
  },

  // Redeem offer action triggers offer redemption event
  {
    actionType: 'offerData/redeemOptimistic/fulfilled',
    handler: (action: any, emit: (event: string, data: any) => void) => {
      const { offerId, cardId, count, redemptionData } = action.payload || {};
      if (offerId) {
        emit(EVENT.Offer.redeemed, {
          id: offerId,
          cardId,
          count,
          source: 'redux',
          timestamp: new Date().toISOString(),
          redemptionData,
        });
      }
    },
  },

  // Update offer action triggers offer update event
  {
    actionType: 'offerData/updateOptimistic/fulfilled',
    handler: (action: any, emit: (event: string, data: any) => void) => {
      const offer = action.payload;
      if (offer && offer.id) {
        emit(EVENT.Offer.updated, {
          id: offer.id,
          source: 'redux',
          timestamp: new Date().toISOString(),
          offerData: offer,
        });
      }
    },
  },

  // Offer selection triggers offer view event (if such event exists)
  {
    actionType: 'offerUI/selectOffer',
    handler: (action: any, emit: (event: string, data: any) => void) => {
      const offerId = action.payload;
      if (offerId) {
        // Emit offer view event if it exists in the legacy system
        emit('offer.view', {
          id: offerId,
          source: 'redux',
          timestamp: new Date().toISOString(),
        });
      }
    },
    condition: (action: any) => action.payload !== null, // Only emit when selecting an offer
  },
];

// =============================================================================
// OFFER EVENT BRIDGE CONFIGURATION
// =============================================================================

/**
 * Complete offer event bridge configuration
 * Combines event-to-redux and redux-to-event mappings
 * Updated for refactored slice structure
 */
export const offerEventBridgeConfig = {
  name: 'OfferEventBridge',
  description: 'Bidirectional bridge for offer events and Redux actions (Refactored)',
  eventToRedux: offerEventToReduxMappings,
  reduxToEvent: offerReduxToEventMappings,

  // Configuration options
  options: {
    enableLogging: __DEV__,
    enableMetrics: true,
    debounceDefault: 200,
    maxEventQueueSize: 100,
    realmFirst: true, // New: Indicates Realm-first architecture
  },

  // Health check function
  healthCheck: () => ({
    isHealthy: true,
    eventMappings: offerEventToReduxMappings.length,
    actionMappings: offerReduxToEventMappings.length,
    lastActivity: Date.now(),
    architecture: 'refactored', // New: Indicates refactored architecture
    sliceStructure: 'offerData + offerUI + redemptions', // New: Documents slice structure
  }),
};
