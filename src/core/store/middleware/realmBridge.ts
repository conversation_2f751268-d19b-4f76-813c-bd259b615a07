import { Card } from '../../../data/models/Card';
import { Offer } from '../../../data/models/Offer';
import { Person } from '../../../data/models/Person';
import { SyncOperation } from '../types/common';

export class RealmReduxBridge {
  private static instance: RealmReduxBridge;
  private store: any;
  private realmListeners: Map<string, any> = new Map();

  static initialize(store: any) {
    if (!this.instance) {
      this.instance = new RealmReduxBridge(store);
    }
    return this.instance;
  }

  constructor(store: any) {
    this.store = store;
    this.setupRealmListeners();
  }

  private setupRealmListeners() {
    // Card changes - using actual Card model
    const cardListener = (cards: Realm.Results<Card>, changes: Realm.CollectionChangeSet) => {
      // Use Realm's toJSON() for efficient conversion
      const plainCards = Array.from(cards).map(card => card.toJSON());
      
      if (changes.insertions.length > 0) {
        changes.insertions.forEach(index => {
          // Will be connected to cardDataSlice once created
          console.log('Card inserted:', plainCards[index]);
        });
      }
      
      if (changes.modifications.length > 0) {
        changes.modifications.forEach(index => {
          // Will be connected to cardDataSlice once created
          console.log('Card modified:', plainCards[index]);
        });
      }
      
      if (changes.deletions.length > 0) {
        // Handle deletions - implement soft delete tracking
        changes.deletions.forEach(index => {
          console.warn('Card deletion detected, implement soft delete tracking');
        });
      }
    };
    
    this.realmListeners.set('Card', cardListener);

    // Offer changes - using actual Offer model
    const offerListener = (offers: Realm.Results<Offer>, changes: Realm.CollectionChangeSet) => {
      // Use Realm's toJSON() for efficient conversion
      const plainOffers = Array.from(offers).map(offer => offer.toJSON());
      
      if (changes.insertions.length > 0) {
        changes.insertions.forEach(index => {
          // Will be connected to offerDataSlice once created
          console.log('Offer inserted:', plainOffers[index]);
        });
      }
      
      if (changes.modifications.length > 0) {
        changes.modifications.forEach(index => {
          // Will be connected to offerDataSlice once created
          console.log('Offer modified:', plainOffers[index]);
        });
      }
    };
    
    this.realmListeners.set('Offer', offerListener);

    // Person changes - using actual Person model
    const personListener = (persons: Realm.Results<Person>, changes: Realm.CollectionChangeSet) => {
      const plainPersons = Array.from(persons).map(person => person.toJSON());
      
      if (changes.insertions.length > 0) {
        changes.insertions.forEach(index => {
          // Will be connected to personDataSlice
          console.log('Person inserted:', plainPersons[index]);
        });
      }

      if (changes.modifications.length > 0) {
        changes.modifications.forEach(index => {
          // Will be connected to personDataSlice
          console.log('Person modified:', plainPersons[index]);
        });
      }
    };
    
    this.realmListeners.set('Person', personListener);
  }

  // Connect slice actions once they're created
  connectCardDataSlice(cardDataSlice: any) {
    // Import the actual slice actions
    const { addCardFromRealm, updateCardFromRealm, syncFromRealm } = cardDataSlice.actions;

    // Update listener to dispatch to actual slice
    this.realmListeners.set('Card', (cards: Realm.Results<Card>, changes: Realm.CollectionChangeSet) => {
      const plainCards = Array.from(cards).map(card => card.toJSON());

      // If this is the initial load, sync all cards
      if (changes.insertions.length === cards.length && changes.modifications.length === 0) {
        this.store.dispatch(syncFromRealm(plainCards));
        return;
      }

      // Handle individual changes
      if (changes.insertions.length > 0) {
        changes.insertions.forEach(index => {
          this.store.dispatch(addCardFromRealm(plainCards[index]));
        });
      }

      if (changes.modifications.length > 0) {
        changes.modifications.forEach(index => {
          this.store.dispatch(updateCardFromRealm(plainCards[index]));
        });
      }
    });
  }

  connectOfferDataSlice(offerDataSlice: any) {
    // Import the actual slice actions
    const { addOfferFromRealm, updateOfferFromRealm, syncFromRealm } = offerDataSlice.actions;

    // Update listener to dispatch to actual slice
    this.realmListeners.set('Offer', (offers: Realm.Results<Offer>, changes: Realm.CollectionChangeSet) => {
      const plainOffers = Array.from(offers).map(offer => offer.toJSON());

      // If this is the initial load, sync all offers
      if (changes.insertions.length === offers.length && changes.modifications.length === 0) {
        this.store.dispatch(syncFromRealm(plainOffers));
        return;
      }

      // Handle individual changes
      if (changes.insertions.length > 0) {
        changes.insertions.forEach(index => {
          this.store.dispatch(addOfferFromRealm(plainOffers[index]));
        });
      }

      if (changes.modifications.length > 0) {
        changes.modifications.forEach(index => {
          this.store.dispatch(updateOfferFromRealm(plainOffers[index]));
        });
      }
    });
  }

  connectPersonDataSlice(personDataSlice: any) {
    // Import the actual slice actions
    const { addPersonFromRealm, updatePersonFromRealm, syncFromRealm } = personDataSlice.actions;

    // Update listener to dispatch to actual slice
    this.realmListeners.set('Person', (persons: Realm.Results<Person>, changes: Realm.CollectionChangeSet) => {
      const plainPersons = Array.from(persons).map(person => person.toJSON());

      // If this is the initial load, sync all persons
      if (changes.insertions.length === persons.length && changes.modifications.length === 0) {
        this.store.dispatch(syncFromRealm(plainPersons));
        return;
      }

      // Handle individual changes
      if (changes.insertions.length > 0) {
        changes.insertions.forEach(index => {
          this.store.dispatch(addPersonFromRealm(plainPersons[index]));
        });
      }

      if (changes.modifications.length > 0) {
        changes.modifications.forEach(index => {
          this.store.dispatch(updatePersonFromRealm(plainPersons[index]));
        });
      }
    });
  }

  // Use Realm's optimized toJSON() method
  private realmToPlain<T extends Realm.Object>(realmObject: T): any {
    // Realm's toJSON() is optimized for this exact use case
    // Handles Realm.Mixed fields, embedded objects, and relationships properly
    return realmObject.toJSON();
  }

  // Sync queue management (placeholder for now)
  static async processSync(): Promise<void> {
    // Will implement sync queue processing
    console.log('Sync queue processing - to be implemented');
  }

  // Cleanup method
  destroy() {
    this.realmListeners.clear();
  }
}

// Data conversion utilities using Realm's toJSON()
export const realmToRedux = {
  card: (realmCard: Card): Card => {
    // Use Realm's optimized toJSON() method
    // This properly handles Realm.Mixed fields, embedded objects, and dates
    return realmCard.toJSON();
  },
  
  offer: (realmOffer: Offer): Offer => {
    // Use Realm's optimized toJSON() method
    return realmOffer.toJSON();
  },
  
  person: (realmPerson: Person): Person => {
    // Use Realm's optimized toJSON() method
    return realmPerson.toJSON();
  },
};

export default RealmReduxBridge;
