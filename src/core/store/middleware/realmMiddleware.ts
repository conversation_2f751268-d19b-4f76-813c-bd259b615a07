/**
 * Realm-Redux Middleware (Refactored)
 *
 * Generic, scalable middleware for bidirectional sync between Realm database and Redux state
 * Follows clean architecture principles with configuration-driven approach
 *
 * REFACTORED FOR:
 * - Entity adapter-based slices (cardDataSlice, offerDataSlice, personDataSlice)
 * - Individual CRUD actions (addFromRealm, updateFromRealm, removeFromRealm)
 * - Optimistic updates with Realm-first architecture
 * - Improved error handling and connection status management
 */

import { createListenerMiddleware } from '@reduxjs/toolkit';
import { DatabaseManager } from '../../database/config';
import { cardDataSlice } from '../slices/card/cardDataSlice';
import { offerDataSlice } from '../slices/offer/offerDataSlice';
import { personDataSlice } from '../slices/person/personDataSlice';
import type { DispatchFunction } from '../types';

// =============================================================================
// TYPES
// =============================================================================

interface RealmObjectData {
  id?: string;
  [key: string]: unknown;
}

interface ModelConfig {
  slice: any;
  collectionName: string;
  syncAction: string; // For bulk sync (syncFromRealm)
  addAction: string; // For individual additions (addFromRealm)
  updateAction: string; // For individual updates (updateFromRealm)
  removeAction: string; // For individual removals (removeFromRealm)
  errorAction?: string;
}

// =============================================================================
// CONFIGURATION
// =============================================================================

/**
 * Model configuration mapping for refactored slices
 * Add new models here to automatically get Realm-Redux sync
 *
 * All models with external sync requirements should be included here
 * Updated to use entity adapter-based actions
 */
const MODEL_CONFIGS: Record<string, ModelConfig> = {
  Card: {
    slice: cardDataSlice,
    collectionName: 'Card',
    syncAction: 'syncFromRealm',
    addAction: 'addCardFromRealm',
    updateAction: 'updateCardFromRealm',
    removeAction: 'removeCardFromRealm',
    errorAction: 'clearError',
  },
  Offer: {
    slice: offerDataSlice,
    collectionName: 'Offer',
    syncAction: 'syncFromRealm',
    addAction: 'addOfferFromRealm',
    updateAction: 'updateOfferFromRealm',
    removeAction: 'removeOfferFromRealm',
    errorAction: 'clearError',
  },
  Person: {
    slice: personDataSlice,
    collectionName: 'Person',
    syncAction: 'syncFromRealm',
    addAction: 'addPersonFromRealm',
    updateAction: 'updatePersonFromRealm',
    removeAction: 'removePersonFromRealm',
    errorAction: 'clearError',
  },
} as const;

// =============================================================================
// REALM-REDUX BRIDGE
// =============================================================================

/**
 * Realm-Redux Listener Middleware
 */
export const realmListenerMiddleware = createListenerMiddleware();

/**
 * Generic Realm-Redux Bridge
 *
 * Scalable bridge that automatically syncs any configured model between Realm and Redux
 */
export class RealmReduxBridge {
  private static listeners: Map<string, Realm.CollectionChangeCallback<any>> = new Map();
  private static isInitialized = false;

  /**
   * Initialize Realm-Redux Bridge
   */
  static async initialize(dispatch: DispatchFunction): Promise<void> {
    if (this.isInitialized) {
      console.log('🔗 Realm-Redux bridge already initialized');
      return;
    }

    try {
      console.log('🔗 Initializing Realm-Redux bridge...');

      const realm = await DatabaseManager.initialize();

      // Setup listeners for all configured models
      for (const [modelName, config] of Object.entries(MODEL_CONFIGS)) {
        await this.setupModelListener(realm, modelName, config, dispatch);
      }

      this.isInitialized = true;
      console.log('✅ Realm-Redux bridge initialized successfully');
      console.log(`🔗 Active listeners: ${this.listeners.size}`);
    } catch (error) {
      console.error('❌ Failed to initialize Realm-Redux bridge:', error);
      throw error;
    }
  }

  /**
   * Setup listener for a specific model with entity adapter support
   */
  private static async setupModelListener(
    realm: Realm,
    modelName: string,
    config: ModelConfig,
    dispatch: DispatchFunction
  ): Promise<void> {
    const listener: Realm.CollectionChangeCallback = (objects, changes) => {
      if (this.hasChanges(changes)) {
        console.log(`🔄 ${modelName} changes detected: +${changes.insertions.length} ~${changes.newModifications.length} -${changes.deletions.length}`);

        try {
          // Handle insertions
          if (changes.insertions.length > 0) {
            changes.insertions.forEach(index => {
              const obj = (objects as any)[index];
              if (obj && config.slice.actions[config.addAction]) {
                const plainObj = this.realmObjectToPlain(obj);
                dispatch(config.slice.actions[config.addAction](plainObj));
              }
            });
          }

          // Handle modifications
          if (changes.newModifications.length > 0) {
            changes.newModifications.forEach(index => {
              const obj = (objects as any)[index];
              if (obj && config.slice.actions[config.updateAction]) {
                const plainObj = this.realmObjectToPlain(obj);
                dispatch(config.slice.actions[config.updateAction](plainObj));
              }
            });
          }

          // Handle deletions
          if (changes.deletions.length > 0) {
            // For deletions, we need to track the IDs before they're removed
            // This is a limitation of Realm - we can't get the deleted object data
            console.warn(`⚠️ ${modelName} deletions detected but object data not available. Consider using soft deletes.`);
            // If you have a way to track deleted IDs, dispatch removeAction here
          }

        } catch (error) {
          console.error(`❌ Failed to sync ${modelName} changes:`, error);

          // Dispatch error if slice supports it
          if (config.errorAction && config.slice.actions[config.errorAction]) {
            dispatch(config.slice.actions[config.errorAction](
              error instanceof Error ? error.message : `Failed to sync ${modelName}: Unknown error`
            ));
          }
        }
      }
    };

    const objects = realm.objects(config.collectionName);
    (objects as any).addListener(listener);
    this.listeners.set(modelName, listener);

    console.log(`🔗 ${modelName} change listener registered`);

    // Initial sync - load existing data into Redux
    try {
      const existingData = Array.from(objects as any).map((obj: any) => this.realmObjectToPlain(obj));
      if (existingData.length > 0 && config.slice.actions[config.syncAction]) {
        dispatch(config.slice.actions[config.syncAction](existingData));
        console.log(`📊 ${modelName} initial sync completed: ${existingData.length} items`);
      }
    } catch (error) {
      console.error(`❌ Failed to perform initial ${modelName} sync:`, error);
    }
  }

  /**
   * Check if changes contain actual modifications
   */
  private static hasChanges(changes: Realm.CollectionChangeSet): boolean {
    return changes.insertions.length > 0 ||
           changes.newModifications.length > 0 ||
           changes.deletions.length > 0;
  }

  /**
   * Convert Realm Object to Plain Object
   */
  private static realmObjectToPlain(realmObject: any): RealmObjectData {
    try {
      return realmObject.toJSON();
    } catch (error) {
      console.warn('⚠️ Failed to convert Realm object to plain object:', error);
      return {};
    }
  }

  /**
   * Cleanup Listeners
   */
  static cleanup(): void {
    try {
      const realm = DatabaseManager.getInstance();

      this.listeners.forEach((listener, modelName) => {
        const config = MODEL_CONFIGS[modelName];
        if (config) {
          const objects = realm.objects(config.collectionName);
          (objects as any).removeListener(listener);
          console.log(`🔗 Removed ${modelName} listener`);
        }
      });

      this.listeners.clear();
      this.isInitialized = false;

      console.log('🧹 Realm-Redux bridge cleanup completed');
    } catch (error) {
      console.error('❌ Failed to cleanup Realm-Redux bridge:', error);
    }
  }

  /**
   * Get initialization status (public accessor)
   */
  static getInitializationStatus(): boolean {
    return this.isInitialized;
  }

  /**
   * Get active listeners count (public accessor)
   */
  static getActiveListenersCount(): number {
    return this.listeners.size;
  }

  /**
   * Public method to convert Realm object to plain object
   */
  static convertRealmObjectToPlain(realmObject: any): RealmObjectData {
    return this.realmObjectToPlain(realmObject);
  }
}

// =============================================================================
// REDUX MIDDLEWARE LISTENERS
// =============================================================================

/**
 * Redux-to-Realm sync listeners for optimistic actions
 *
 * The refactored architecture uses optimistic updates where:
 * 1. Redux action triggers service call
 * 2. Service updates Realm immediately
 * 3. Realm listener updates Redux state
 * 4. These listeners handle connection status and error recovery
 */

// Connection status management
realmListenerMiddleware.startListening({
  predicate: (action) => {
    return action.type.includes('Optimistic/pending');
  },
  effect: async (action, listenerApi) => {
    // Set realm connection status when optimistic operations start
    if (action.type.includes('cardData')) {
      listenerApi.dispatch(cardDataSlice.actions.setRealmConnectionStatus(true));
    } else if (action.type.includes('offerData')) {
      listenerApi.dispatch(offerDataSlice.actions.setRealmConnectionStatus(true));
    } else if (action.type.includes('personData')) {
      listenerApi.dispatch(personDataSlice.actions.setRealmConnectionStatus(true));
    }
  },
});

// Error handling for failed optimistic operations
realmListenerMiddleware.startListening({
  predicate: (action) => {
    return action.type.includes('Optimistic/rejected');
  },
  effect: async (action) => {
    console.error('🚨 Optimistic operation failed:', action.type, action.error);

    // The error is already handled by the slice's extraReducers
    // This listener can be used for additional error reporting or recovery
  },
});

// =============================================================================
// UTILITY METHODS
// =============================================================================

/**
 * Utility methods for the refactored architecture
 */
export const RealmMiddlewareUtils = {
  /**
   * Get health status of all model listeners
   */
  getHealthStatus: () => ({
    isInitialized: RealmReduxBridge.getInitializationStatus(),
    activeListeners: RealmReduxBridge.getActiveListenersCount(),
    modelConfigs: Object.keys(MODEL_CONFIGS),
    architecture: 'refactored-entity-adapters',
    lastCheck: new Date().toISOString(),
  }),

  /**
   * Force sync all models from Realm to Redux
   */
  forceSyncAll: async (dispatch: DispatchFunction) => {
    try {
      const realm = DatabaseManager.getInstance();

      for (const [modelName, config] of Object.entries(MODEL_CONFIGS)) {
        const objects = realm.objects(config.collectionName);
        const data = Array.from(objects as any).map((obj: any) =>
          RealmReduxBridge.convertRealmObjectToPlain(obj)
        );

        if (config.slice.actions[config.syncAction]) {
          dispatch(config.slice.actions[config.syncAction](data));
          console.log(`🔄 Force synced ${modelName}: ${data.length} items`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to force sync all models:', error);
    }
  },

  /**
   * Get model configuration for debugging
   */
  getModelConfig: (modelName: string) => MODEL_CONFIGS[modelName],

  /**
   * Get all available model configurations
   */
  getAllModelConfigs: () => MODEL_CONFIGS,
};



