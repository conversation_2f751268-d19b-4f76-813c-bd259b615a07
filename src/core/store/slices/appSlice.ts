/**
 * App Redux Slice
 * 
 * Modern Redux Toolkit slice for application state management
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// =============================================================================
// TYPES
// =============================================================================

export interface AppState {
  isInitialized: boolean;
  version: string;
  schemaVersion: number;
  initializedAt?: string;
  architecture?: string;
  loading: boolean;
  error: string | null;
}

// =============================================================================
// SLICE
// =============================================================================

const initialState: AppState = {
  isInitialized: false,
  version: '1.0.0',
  schemaVersion: 1,
  initializedAt: undefined,
  architecture: undefined,
  loading: false,
  error: null,
};

export const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    initialize: (state, action: PayloadAction<{
      version: string;
      schemaVersion: number;
      initializedAt: string;
      architecture?: string;
    }>) => {
      state.isInitialized = true;
      state.version = action.payload.version;
      state.schemaVersion = action.payload.schemaVersion;
      state.initializedAt = action.payload.initializedAt;
      state.architecture = action.payload.architecture;
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const { initialize, setLoading, setError, clearError } = appSlice.actions;
export default appSlice.reducer;
