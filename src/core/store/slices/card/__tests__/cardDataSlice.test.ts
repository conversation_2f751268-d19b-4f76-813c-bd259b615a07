import { configureStore } from '@reduxjs/toolkit';
import { cardDataSlice, cardsAdapter, createCardOptimistic, acceptCardOptimistic } from '../cardDataSlice';
import { CardService } from '../../../../services/CardService';
import { Card } from '../../../../../data/models/Card';

// Mock CardService
jest.mock('../../../../services/CardService');
const mockCardService = CardService as jest.Mocked<typeof CardService>;

describe('CardDataSlice', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        cardData: cardDataSlice.reducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
          serializableCheck: {
            ignoredActions: [
              // Realm sync actions with Date objects
              'cardData/syncFromRealm',
              'cardData/addCardFromRealm',
              'cardData/updateCardFromRealm',
              'cardData/createOptimistic/fulfilled',
              'cardData/acceptOptimistic/fulfilled',
              'cardData/declineOptimistic/fulfilled',
              'cardData/updateOptimistic/fulfilled',
            ],
            ignoredPaths: [
              // Ignore Date objects in state
              'cardData.entities',
              'cardData.lastSync',
            ],
          },
        }),
    });
    jest.clearAllMocks();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().cardData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(null);
      expect(state.lastSync).toBe(null);
      expect(state.realmConnected).toBe(false);
      expect(state.ids).toEqual([]);
      expect(state.entities).toEqual({});
    });
  });

  describe('syncFromRealm', () => {
    it('should sync cards from Realm', () => {
      const mockCards: Card[] = [
        {
          id: 'card-1',
          masterId: 'master-1',
          personId: 'person-1',
          displayName: 'Test Card 1',
          state: 'active',
          visible: true,
          createdAt: new Date('2023-01-01'),
        } as Card,
        {
          id: 'card-2',
          masterId: 'master-2',
          personId: 'person-1',
          displayName: 'Test Card 2',
          state: 'pending',
          visible: true,
          createdAt: new Date('2023-01-02'),
        } as Card,
      ];

      store.dispatch(cardDataSlice.actions.syncFromRealm(mockCards));

      const state = store.getState().cardData;
      expect(state.ids).toHaveLength(2);
      expect(state.entities['card-1']).toEqual(mockCards[0]);
      expect(state.entities['card-2']).toEqual(mockCards[1]);
      expect(state.lastSync).toBeDefined();
      expect(state.realmConnected).toBe(true);
    });
  });

  describe('addCardFromRealm', () => {
    it('should add a single card from Realm', () => {
      const mockCard: Card = {
        id: 'card-1',
        masterId: 'master-1',
        personId: 'person-1',
        displayName: 'Test Card',
        state: 'active',
        visible: true,
        createdAt: new Date('2023-01-01'),
      } as Card;

      store.dispatch(cardDataSlice.actions.addCardFromRealm(mockCard));

      const state = store.getState().cardData;
      expect(state.ids).toContain('card-1');
      expect(state.entities['card-1']).toEqual(mockCard);
    });
  });

  describe('updateCardFromRealm', () => {
    it('should update an existing card from Realm', () => {
      const initialCard: Card = {
        id: 'card-1',
        masterId: 'master-1',
        personId: 'person-1',
        displayName: 'Test Card',
        state: 'pending',
        visible: true,
        createdAt: new Date('2023-01-01'),
      } as Card;

      const updatedCard: Card = {
        ...initialCard,
        state: 'active',
        displayName: 'Updated Card',
      };

      // Add initial card
      store.dispatch(cardDataSlice.actions.addCardFromRealm(initialCard));
      
      // Update card
      store.dispatch(cardDataSlice.actions.updateCardFromRealm(updatedCard));

      const state = store.getState().cardData;
      expect(state.entities['card-1']?.state).toBe('active');
      expect(state.entities['card-1']?.displayName).toBe('Updated Card');
    });
  });

  describe('removeCardFromRealm', () => {
    it('should remove a card from state', () => {
      const mockCard: Card = {
        id: 'card-1',
        masterId: 'master-1',
        personId: 'person-1',
        displayName: 'Test Card',
        state: 'active',
        visible: true,
        createdAt: new Date('2023-01-01'),
      } as Card;

      // Add card
      store.dispatch(cardDataSlice.actions.addCardFromRealm(mockCard));
      expect(store.getState().cardData.ids).toContain('card-1');

      // Remove card
      store.dispatch(cardDataSlice.actions.removeCardFromRealm('card-1'));
      
      const state = store.getState().cardData;
      expect(state.ids).not.toContain('card-1');
      expect(state.entities['card-1']).toBeUndefined();
    });
  });

  describe('createCardOptimistic', () => {
    it('should handle card creation successfully', async () => {
      const createData = {
        masterId: 'master-1',
        personId: 'person-1',
        displayName: 'New Card',
      };

      const mockCreatedCard: Card = {
        id: 'card-new',
        ...createData,
        state: 'pending',
        visible: true,
        createdAt: new Date(),
      } as Card;

      mockCardService.createCard.mockResolvedValue(mockCreatedCard);

      await store.dispatch(createCardOptimistic(createData));

      const state = store.getState().cardData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(null);
      expect(mockCardService.createCard).toHaveBeenCalledWith(createData);
    });

    it('should handle card creation failure', async () => {
      const createData = {
        masterId: 'master-1',
        personId: 'person-1',
        displayName: 'New Card',
      };

      const errorMessage = 'Failed to create card';
      mockCardService.createCard.mockRejectedValue(new Error(errorMessage));

      await store.dispatch(createCardOptimistic(createData));

      const state = store.getState().cardData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(errorMessage);
    });
  });

  describe('acceptCardOptimistic', () => {
    it('should handle card acceptance successfully', async () => {
      const cardId = 'card-1';
      const acceptResult = { cardId, acceptedAt: new Date() };

      mockCardService.acceptCard.mockResolvedValue(acceptResult);

      await store.dispatch(acceptCardOptimistic(cardId));

      const state = store.getState().cardData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(null);
      expect(mockCardService.acceptCard).toHaveBeenCalledWith(cardId);
    });

    it('should handle card acceptance failure', async () => {
      const cardId = 'card-1';
      const errorMessage = 'Failed to accept card';
      
      mockCardService.acceptCard.mockRejectedValue(new Error(errorMessage));

      await store.dispatch(acceptCardOptimistic(cardId));

      const state = store.getState().cardData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(errorMessage);
    });
  });

  describe('clearError', () => {
    it('should clear error state', () => {
      // Set an error first
      store.dispatch(cardDataSlice.actions.clearError());
      
      // Manually set error to test clearing
      const stateWithError = {
        ...store.getState().cardData,
        error: 'Some error',
      };
      
      store.dispatch(cardDataSlice.actions.clearError());
      
      const state = store.getState().cardData;
      expect(state.error).toBe(null);
    });
  });
});
