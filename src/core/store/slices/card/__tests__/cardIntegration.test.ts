import { configureStore } from '@reduxjs/toolkit';
import { cardDataSlice } from '../cardDataSlice';
import { cardUISlice } from '../cardUISlice';
import { 
  selectCardMetrics, 
  selectFilteredCards, 
  selectSelectedCard,
  selectCardDisplayInfo 
} from '../cardSelectors';
import { Card } from '../../../../../data/models/Card';

describe('Card Slices Integration', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        cardData: cardDataSlice.reducer,
        cardUI: cardUISlice.reducer,
      },
    });
  });

  const mockCards: Card[] = [
    {
      id: 'card-1',
      masterId: 'master-1',
      personId: 'person-1',
      displayName: 'Active Card',
      state: 'active',
      visible: true,
      createdAt: new Date('2023-01-01'),
      storedValue: { balance: 100 },
    } as Card,
    {
      id: 'card-2',
      masterId: 'master-2',
      personId: 'person-1',
      displayName: 'Pending Card',
      state: 'pending',
      visible: true,
      createdAt: new Date('2023-01-02'),
    } as Card,
    {
      id: 'card-3',
      masterId: 'master-1',
      personId: 'person-2',
      displayName: 'Hidden Card',
      state: 'active',
      visible: false,
      createdAt: new Date('2023-01-03'),
      endTime: new Date('2022-12-31'), // Expired
    } as Card,
  ];

  describe('Complete card flow', () => {
    it('should handle card data sync and UI interactions', () => {
      // 1. Sync cards from Realm
      store.dispatch(cardDataSlice.actions.syncFromRealm(mockCards));

      // 2. Verify metrics are computed correctly
      const metrics = selectCardMetrics(store.getState());
      expect(metrics.totalCards).toBe(3);
      expect(metrics.activeCards).toBe(2);
      expect(metrics.visibleCards).toBe(2);
      expect(metrics.cardsWithStoredValue).toBe(1);
      expect(metrics.expiredCards).toBe(1);

      // 3. Test card display info
      const displayInfo = selectCardDisplayInfo(store.getState());
      expect(displayInfo).toHaveLength(3);
      expect(displayInfo[0].name).toBe('Active Card');
      expect(displayInfo[0].hasStoredValue).toBe(true);
      expect(displayInfo[1].name).toBe('Pending Card');
      expect(displayInfo[1].hasStoredValue).toBe(false);

      // 4. Select a card
      store.dispatch(cardUISlice.actions.selectCard('card-1'));
      const selectedCard = selectSelectedCard(store.getState());
      expect(selectedCard?.id).toBe('card-1');
      expect(selectedCard?.displayName).toBe('Active Card');

      // 5. Apply filters
      store.dispatch(cardUISlice.actions.setFilters({ status: 'active' }));
      const filteredCards = selectFilteredCards(store.getState());
      expect(filteredCards).toHaveLength(2);
      expect(filteredCards.every(card => card.state === 'active')).toBe(true);

      // 6. Apply search filter
      store.dispatch(cardUISlice.actions.setFilters({ 
        status: 'active',
        searchTerm: 'Active'
      }));
      const searchedCards = selectFilteredCards(store.getState());
      expect(searchedCards).toHaveLength(1);
      expect(searchedCards[0].displayName).toBe('Active Card');

      // 7. Change sorting
      store.dispatch(cardUISlice.actions.setSorting({ 
        sortBy: 'name', 
        sortOrder: 'asc' 
      }));
      const sortedCards = selectFilteredCards(store.getState());
      expect(sortedCards[0].displayName).toBe('Active Card');

      // 8. Clear filters
      store.dispatch(cardUISlice.actions.clearFilters());
      const allCards = selectFilteredCards(store.getState());
      expect(allCards).toHaveLength(3);
    });

    it('should handle card updates through Realm', () => {
      // 1. Initial sync
      store.dispatch(cardDataSlice.actions.syncFromRealm(mockCards));

      // 2. Select a card
      store.dispatch(cardUISlice.actions.selectCard('card-2'));
      let selectedCard = selectSelectedCard(store.getState());
      expect(selectedCard?.state).toBe('pending');

      // 3. Update card through Realm (simulating acceptance)
      const updatedCard: Card = {
        ...mockCards[1],
        state: 'active',
        activeTime: new Date(),
      };
      store.dispatch(cardDataSlice.actions.updateCardFromRealm(updatedCard));

      // 4. Verify selected card is updated
      selectedCard = selectSelectedCard(store.getState());
      expect(selectedCard?.state).toBe('active');

      // 5. Verify metrics are updated
      const metrics = selectCardMetrics(store.getState());
      expect(metrics.activeCards).toBe(3); // Now all 3 are active
    });

    it('should handle card removal', () => {
      // 1. Initial sync
      store.dispatch(cardDataSlice.actions.syncFromRealm(mockCards));
      
      // 2. Select a card
      store.dispatch(cardUISlice.actions.selectCard('card-1'));
      expect(selectSelectedCard(store.getState())?.id).toBe('card-1');

      // 3. Remove the selected card
      store.dispatch(cardDataSlice.actions.removeCardFromRealm('card-1'));

      // 4. Verify card is removed and selection is cleared
      const selectedCard = selectSelectedCard(store.getState());
      expect(selectedCard).toBe(null);

      // 5. Verify metrics are updated
      const metrics = selectCardMetrics(store.getState());
      expect(metrics.totalCards).toBe(2);
    });

    it('should handle complex filtering scenarios', () => {
      // 1. Initial sync
      store.dispatch(cardDataSlice.actions.syncFromRealm(mockCards));

      // 2. Filter by visibility
      store.dispatch(cardUISlice.actions.setFilters({ isVisible: true }));
      let filtered = selectFilteredCards(store.getState());
      expect(filtered).toHaveLength(2);

      // 3. Filter by stored value
      store.dispatch(cardUISlice.actions.setFilters({ 
        isVisible: true,
        hasStoredValue: true 
      }));
      filtered = selectFilteredCards(store.getState());
      expect(filtered).toHaveLength(1);
      expect(filtered[0].id).toBe('card-1');

      // 4. Filter by master ID
      store.dispatch(cardUISlice.actions.setFilters({ 
        masterId: 'master-1'
      }));
      filtered = selectFilteredCards(store.getState());
      expect(filtered).toHaveLength(2); // card-1 and card-3
      expect(filtered.every(card => card.masterId === 'master-1')).toBe(true);
    });
  });

  describe('Error handling', () => {
    it('should maintain UI state when data operations fail', () => {
      // 1. Set up UI state
      store.dispatch(cardUISlice.actions.selectCard('card-1'));
      store.dispatch(cardUISlice.actions.setFilters({ status: 'active' }));

      // 2. Simulate data error
      store.dispatch(cardDataSlice.actions.clearError());
      
      // 3. Verify UI state is preserved
      const uiState = store.getState().cardUI;
      expect(uiState.selectedCardId).toBe('card-1');
      expect(uiState.filters.status).toBe('active');
    });
  });
});
