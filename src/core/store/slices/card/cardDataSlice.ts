import { createEntityAdapter, createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Card } from '../../../../data/models/Card';
import { CreateCardData, UpdateCardData } from '../../types/card';
import { CardService } from '../../../services/CardService';
import { RootState } from '../../types';

// Entity adapter for normalized Card state
export const cardsAdapter = createEntityAdapter<Card>({
  // Use Realm's primary key
  selectId: (card) => card.id,
  // Sort by creation date (newest first)
  sortComparer: (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
});

export interface CardDataState {
  loading: boolean;
  error: string | null;
  lastSync: string | null;
  realmConnected: boolean;
}

// Initialize with adapter's initial state
const initialState = cardsAdapter.getInitialState<CardDataState>({
  loading: false,
  error: null,
  lastSync: null,
  realmConnected: false,
});

// Async thunks for optimistic operations
export const createCardOptimistic = createAsyncThunk(
  'cardData/createOptimistic',
  async (data: CreateCardData) => {
    // This will update Realm immediately and queue for sync
    return await CardService.createCard(data);
  }
);

export const acceptCardOptimistic = createAsyncThunk(
  'cardData/acceptOptimistic',
  async (cardId: string) => {
    // This will update Realm immediately and queue for sync
    const result = await CardService.acceptCard(cardId);
    return result.cardId;
  }
);

export const declineCardOptimistic = createAsyncThunk(
  'cardData/declineOptimistic',
  async (cardId: string) => {
    // This will update Realm immediately and queue for sync
    const result = await CardService.declineCard(cardId);
    return result.cardId;
  }
);

export const updateCardOptimistic = createAsyncThunk(
  'cardData/updateOptimistic',
  async ({ id, changes }: { id: string; changes: UpdateCardData }) => {
    // This will update Realm immediately and queue for sync
    return await CardService.updateCard(id, changes);
  }
);

export const cardDataSlice = createSlice({
  name: 'cardData',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    
    // Realm integration: Sync from Realm to Redux
    syncFromRealm: (state, action: PayloadAction<Card[]>) => {
      cardsAdapter.setAll(state, action.payload);
      state.lastSync = new Date().toISOString();
      state.realmConnected = true;
    },
    
    // Realm integration: Handle individual Realm changes
    addCardFromRealm: (state, action: PayloadAction<Card>) => {
      cardsAdapter.addOne(state, action.payload);
    },
    
    updateCardFromRealm: (state, action: PayloadAction<Card>) => {
      cardsAdapter.updateOne(state, {
        id: action.payload.id,
        changes: action.payload,
      });
    },
    
    removeCardFromRealm: (state, action: PayloadAction<string>) => {
      cardsAdapter.removeOne(state, action.payload);
    },
    
    setRealmConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.realmConnected = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Create card optimistic
    builder
      .addCase(createCardOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createCardOptimistic.fulfilled, (state) => {
        // Realm listener will handle the actual Redux update
        // This is just for loading state management
        state.loading = false;
      })
      .addCase(createCardOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create card';
      });

    // Accept card optimistic
    builder
      .addCase(acceptCardOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(acceptCardOptimistic.fulfilled, (state) => {
        // Realm listener will handle the actual Redux update
        state.loading = false;
      })
      .addCase(acceptCardOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to accept card';
      });

    // Decline card optimistic
    builder
      .addCase(declineCardOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(declineCardOptimistic.fulfilled, (state) => {
        // Realm listener will handle the actual Redux update
        state.loading = false;
      })
      .addCase(declineCardOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to decline card';
      });

    // Update card optimistic
    builder
      .addCase(updateCardOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCardOptimistic.fulfilled, (state) => {
        // Realm listener will handle the actual Redux update
        state.loading = false;
      })
      .addCase(updateCardOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update card';
      });
  },
});

export const {
  clearError,
  syncFromRealm,
  addCardFromRealm,
  updateCardFromRealm,
  removeCardFromRealm,
  setRealmConnectionStatus,
} = cardDataSlice.actions;

export default cardDataSlice.reducer;
