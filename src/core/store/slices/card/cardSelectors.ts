import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../types';
import { Card } from '../../../../data/models/Card';
import { CardDisplayInfo, CardMetrics, CardOptionsTyped, CardFormDataTyped } from '../../types/card';
import { cardsAdapter } from './cardDataSlice';

// Base selectors from entity adapter (using actual Realm Card model)
export const cardDataSelectors = cardsAdapter.getSelectors(
  (state: RootState) => state.cardData
);

// UI selectors
export const selectCardUI = (state: RootState) => state.cardUI;
export const selectSelectedCardId = (state: RootState) => state.cardUI.selectedCardId;
export const selectCardFilters = (state: RootState) => state.cardUI.filters;
export const selectCardSorting = (state: RootState) => ({
  sortBy: state.cardUI.sortBy,
  sortOrder: state.cardUI.sortOrder,
});

// Data state selectors
export const selectCardDataState = (state: RootState) => ({
  loading: state.cardData.loading,
  error: state.cardData.error,
  lastSync: state.cardData.lastSync,
  realmConnected: state.cardData.realmConnected,
});

// Computed selectors using actual Realm fields
export const selectCardMetrics = createSelector(
  [cardDataSelectors.selectAll],
  (cards): CardMetrics => ({
    totalCards: cards.length,
    activeCards: cards.filter(c => c.state === 'active').length,
    expiredCards: cards.filter(c => c.endTime && c.endTime < new Date()).length,
    visibleCards: cards.filter(c => c.visible !== false).length,
    cardsWithStoredValue: cards.filter(c => c.storedValue).length,
    cardsByState: cards.reduce((acc, card) => {
      acc[card.state] = (acc[card.state] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
  })
);

// UI-friendly card display info
export const selectCardDisplayInfo = createSelector(
  [cardDataSelectors.selectAll],
  (cards): CardDisplayInfo[] => cards.map(card => ({
    id: card.id,
    name: card.displayName || 'Unnamed Card',
    number: card.number,
    barcode: card.barcode,
    barcodeType: card.barcodeType,
    state: card.state,
    isVisible: card.visible ?? true,
    hasStoredValue: !!card.storedValue,
    masterId: card.masterId,
    personId: card.personId,
  }))
);

// Filtered cards using actual Realm fields
export const selectFilteredCards = createSelector(
  [cardDataSelectors.selectAll, selectCardFilters, selectCardSorting],
  (cards, filters, sorting) => {
    let filtered = cards;

    // Apply filters using actual Realm fields
    if (filters.status) {
      filtered = filtered.filter(card => card.state === filters.status);
    }
    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(card => 
        (card.displayName || '').toLowerCase().includes(term) ||
        (card.number || '').toLowerCase().includes(term)
      );
    }
    if (filters.masterId) {
      filtered = filtered.filter(card => card.masterId === filters.masterId);
    }
    if (filters.hasStoredValue !== undefined) {
      filtered = filtered.filter(card => !!card.storedValue === filters.hasStoredValue);
    }
    if (filters.isVisible !== undefined) {
      filtered = filtered.filter(card => (card.visible ?? true) === filters.isVisible);
    }

    // Apply sorting using actual Realm fields
    return filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sorting.sortBy) {
        case 'name':
          aValue = a.displayName || '';
          bValue = b.displayName || '';
          break;
        case 'created':
          aValue = a.createdAt;
          bValue = b.createdAt;
          break;
        case 'lastUsed':
          aValue = a.modifiedAt || a.createdAt;
          bValue = b.modifiedAt || b.createdAt;
          break;
        default:
          return 0;
      }
      
      const order = sorting.sortOrder === 'asc' ? 1 : -1;
      return aValue > bValue ? order : -order;
    });
  }
);

// Selected card with proper typing
export const selectSelectedCard = createSelector(
  [cardDataSelectors.selectAll, selectSelectedCardId],
  (cards, selectedId): Card | null => 
    selectedId ? cards.find(card => card.id === selectedId) || null : null
);

// Card by ID selector factory
export const selectCardById = (cardId: string) => createSelector(
  [cardDataSelectors.selectById],
  (card): Card | null => card || null
);

// Selectors for Realm.Mixed fields
export const selectCardOptions = (cardId: string) => createSelector(
  [(state: RootState) => cardDataSelectors.selectById(state, cardId)],
  (card): CardOptionsTyped | null => card ? (card.options as CardOptionsTyped) || null : null
);

export const selectCardFormData = (cardId: string) => createSelector(
  [(state: RootState) => cardDataSelectors.selectById(state, cardId)],
  (card): CardFormDataTyped | null => card ? (card.formData as CardFormDataTyped) || null : null
);

// Selectors for embedded objects
export const selectCardStoredValue = (cardId: string) => createSelector(
  [(state: RootState) => cardDataSelectors.selectById(state, cardId)],
  (card) => card?.storedValue || null
);

// Selectors for relationship objects
export const selectCardSharer = (cardId: string) => createSelector(
  [(state: RootState) => cardDataSelectors.selectById(state, cardId)],
  (card) => card?.sharer || null
);

// Cards by person
export const selectCardsByPerson = (personId: string) => createSelector(
  [cardDataSelectors.selectAll],
  (cards) => cards.filter(card => card.personId === personId)
);

// Cards by master
export const selectCardsByMaster = (masterId: string) => createSelector(
  [cardDataSelectors.selectAll],
  (cards) => cards.filter(card => card.masterId === masterId)
);

// Active cards for a person
export const selectActiveCardsByPerson = (personId: string) => createSelector(
  [cardDataSelectors.selectAll],
  (cards) => cards.filter(card => 
    card.personId === personId && 
    card.state === 'active' &&
    (card.visible ?? true)
  )
);
