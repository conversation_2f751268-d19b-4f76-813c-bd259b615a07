import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CardUIFilters } from '../../types/card';

export interface CardUIState {
  selectedCardId: string | null;
  filters: CardUIFilters;
  sortBy: 'name' | 'created' | 'lastUsed';
  sortOrder: 'asc' | 'desc';
}

const initialState: CardUIState = {
  selectedCardId: null,
  filters: {},
  sortBy: 'created',
  sortOrder: 'desc',
};

export const cardUISlice = createSlice({
  name: 'cardUI',
  initialState,
  reducers: {
    selectCard: (state, action: PayloadAction<string | null>) => {
      state.selectedCardId = action.payload;
    },
    
    setFilters: (state, action: PayloadAction<Partial<CardUIFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    clearFilters: (state) => {
      state.filters = {};
    },
    
    setSorting: (state, action: PayloadAction<{ 
      sortBy: CardUIState['sortBy']; 
      sortOrder: CardUIState['sortOrder']; 
    }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    resetUI: (state) => {
      state.selectedCardId = null;
      state.filters = {};
      state.sortBy = 'created';
      state.sortOrder = 'desc';
    },
  },
});

export const {
  selectCard,
  setFilters,
  clearFilters,
  setSorting,
  toggleSortOrder,
  resetUI,
} = cardUISlice.actions;

export default cardUISlice.reducer;
