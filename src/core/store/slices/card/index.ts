// Card slice exports - clean re-exports to avoid importing entire slices

// Slices
export { cardDataSlice, cardsAdapter } from './cardDataSlice';
export { cardUISlice } from './cardUISlice';

// Actions from cardDataSlice
export {
  clearError,
  syncFromRealm,
  addCardFromRealm,
  updateCardFromRealm,
  removeCardFromRealm,
  setRealmConnectionStatus,
  createCardOptimistic,
  acceptCardOptimistic,
  declineCardOptimistic,
  updateCardOptimistic,
} from './cardDataSlice';

// Actions from cardUISlice
export {
  selectCard,
  setFilters,
  clearFilters,
  setSorting,
  toggleSortOrder,
  resetUI,
} from './cardUISlice';

// All selectors
export * from './cardSelectors';

// Types
export type { CardDataState } from './cardDataSlice';
export type { CardUIState } from './cardUISlice';
