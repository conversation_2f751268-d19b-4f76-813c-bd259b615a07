import { configureStore } from '@reduxjs/toolkit';
import { offerDataSlice, offersAdapter, createOfferOptimistic, redeemOfferOptimistic } from '../offerDataSlice';
import { OfferService } from '../../../../services/OfferService';
import { Offer } from '../../../../../data/models/Offer';

// Mock OfferService
jest.mock('../../../../services/OfferService');
const mockOfferService = OfferService as jest.Mocked<typeof OfferService>;

describe('OfferDataSlice', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        offerData: offerDataSlice.reducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
          serializableCheck: {
            ignoredActions: [
              // Realm sync actions with Date objects
              'offerData/syncFromRealm',
              'offerData/addOfferFromRealm',
              'offerData/updateOfferFromRealm',
              'offerData/syncRedemptionsFromRealm',
              'offerData/addRedemptionFromRealm',
              'offerData/createOptimistic/fulfilled',
              'offerData/updateOptimistic/fulfilled',
              'offerData/redeemOptimistic/fulfilled',
            ],
            ignoredPaths: [
              // Ignore Date objects in state
              'offerData.entities',
              'offerData.lastSync',
              'offerData.redemptions.entities',
            ],
          },
        }),
    });
    jest.clearAllMocks();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().offerData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(null);
      expect(state.lastSync).toBe(null);
      expect(state.realmConnected).toBe(false);
      expect(state.ids).toEqual([]);
      expect(state.entities).toEqual({});
      expect(state.redemptions.ids).toEqual([]);
      expect(state.redemptions.entities).toEqual({});
    });
  });

  describe('syncFromRealm', () => {
    it('should sync offers from Realm', () => {
      const mockOffers: Offer[] = [
        {
          id: 'offer-1',
          masterId: 'master-1',
          name: 'Test Offer 1',
          title: 'Great Deal',
          kind: 'discount',
          state: 'active',
          visible: true,
          createdAt: new Date('2023-01-01'),
        } as Offer,
        {
          id: 'offer-2',
          masterId: 'master-2',
          name: 'Test Offer 2',
          title: 'Amazing Deal',
          kind: 'voucher',
          state: 'pending',
          visible: true,
          createdAt: new Date('2023-01-02'),
        } as Offer,
      ];

      store.dispatch(offerDataSlice.actions.syncFromRealm(mockOffers));

      const state = store.getState().offerData;
      expect(state.ids).toHaveLength(2);
      expect(state.entities['offer-1']).toEqual(mockOffers[0]);
      expect(state.entities['offer-2']).toEqual(mockOffers[1]);
      expect(state.lastSync).toBeDefined();
      expect(state.realmConnected).toBe(true);
    });
  });

  describe('addOfferFromRealm', () => {
    it('should add a single offer from Realm', () => {
      const mockOffer: Offer = {
        id: 'offer-1',
        masterId: 'master-1',
        name: 'Test Offer',
        title: 'Great Deal',
        kind: 'discount',
        state: 'active',
        visible: true,
        createdAt: new Date('2023-01-01'),
      } as Offer;

      store.dispatch(offerDataSlice.actions.addOfferFromRealm(mockOffer));

      const state = store.getState().offerData;
      expect(state.ids).toContain('offer-1');
      expect(state.entities['offer-1']).toEqual(mockOffer);
    });
  });

  describe('updateOfferFromRealm', () => {
    it('should update an existing offer from Realm', () => {
      const initialOffer: Offer = {
        id: 'offer-1',
        masterId: 'master-1',
        name: 'Test Offer',
        title: 'Great Deal',
        kind: 'discount',
        state: 'pending',
        visible: true,
        createdAt: new Date('2023-01-01'),
      } as Offer;

      const updatedOffer: Offer = {
        ...initialOffer,
        state: 'active',
        title: 'Updated Deal',
      };

      // Add initial offer
      store.dispatch(offerDataSlice.actions.addOfferFromRealm(initialOffer));
      
      // Update offer
      store.dispatch(offerDataSlice.actions.updateOfferFromRealm(updatedOffer));

      const state = store.getState().offerData;
      expect(state.entities['offer-1']?.state).toBe('active');
      expect(state.entities['offer-1']?.title).toBe('Updated Deal');
    });
  });

  describe('removeOfferFromRealm', () => {
    it('should remove an offer from state', () => {
      const mockOffer: Offer = {
        id: 'offer-1',
        masterId: 'master-1',
        name: 'Test Offer',
        title: 'Great Deal',
        kind: 'discount',
        state: 'active',
        visible: true,
        createdAt: new Date('2023-01-01'),
      } as Offer;

      // Add offer
      store.dispatch(offerDataSlice.actions.addOfferFromRealm(mockOffer));
      expect(store.getState().offerData.ids).toContain('offer-1');

      // Remove offer
      store.dispatch(offerDataSlice.actions.removeOfferFromRealm('offer-1'));
      
      const state = store.getState().offerData;
      expect(state.ids).not.toContain('offer-1');
      expect(state.entities['offer-1']).toBeUndefined();
    });
  });

  describe('createOfferOptimistic', () => {
    it('should handle offer creation successfully', async () => {
      const createData = {
        masterId: 'master-1',
        name: 'New Offer',
        title: 'Amazing Deal',
        kind: 'discount' as const,
      };

      const mockCreatedOffer: Offer = {
        id: 'offer-new',
        ...createData,
        state: 'pending',
        visible: true,
        createdAt: new Date(),
      } as Offer;

      mockOfferService.createOffer.mockResolvedValue(mockCreatedOffer);

      await store.dispatch(createOfferOptimistic(createData));

      const state = store.getState().offerData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(null);
      expect(mockOfferService.createOffer).toHaveBeenCalledWith(createData);
    });

    it('should handle offer creation failure', async () => {
      const createData = {
        masterId: 'master-1',
        name: 'New Offer',
        title: 'Amazing Deal',
        kind: 'discount' as const,
      };

      const errorMessage = 'Failed to create offer';
      mockOfferService.createOffer.mockRejectedValue(new Error(errorMessage));

      await store.dispatch(createOfferOptimistic(createData));

      const state = store.getState().offerData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(errorMessage);
    });
  });

  describe('redeemOfferOptimistic', () => {
    it('should handle offer redemption successfully', async () => {
      const redeemData = {
        offerId: 'offer-1',
        cardId: 'card-1',
        personId: 'person-1',
      };

      const redeemResult = {
        offerId: 'offer-1',
        redemptionId: 'redemption-1',
        redeemedAt: new Date(),
      };

      mockOfferService.redeemOffer.mockResolvedValue(redeemResult);

      await store.dispatch(redeemOfferOptimistic(redeemData));

      const state = store.getState().offerData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(null);
      expect(mockOfferService.redeemOffer).toHaveBeenCalledWith(redeemData);
    });

    it('should handle offer redemption failure', async () => {
      const redeemData = {
        offerId: 'offer-1',
        cardId: 'card-1',
        personId: 'person-1',
      };

      const errorMessage = 'Failed to redeem offer';
      mockOfferService.redeemOffer.mockRejectedValue(new Error(errorMessage));

      await store.dispatch(redeemOfferOptimistic(redeemData));

      const state = store.getState().offerData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(errorMessage);
    });
  });

  describe('redemption tracking', () => {
    it('should sync redemptions from Realm', () => {
      const mockRedemptions = [
        {
          id: 'redemption-1',
          offerId: 'offer-1',
          cardId: 'card-1',
          timestamp: '2023-01-01T10:00:00Z',
        },
        {
          id: 'redemption-2',
          offerId: 'offer-2',
          cardId: 'card-2',
          timestamp: '2023-01-02T10:00:00Z',
        },
      ];

      store.dispatch(offerDataSlice.actions.syncRedemptionsFromRealm(mockRedemptions));

      const state = store.getState().offerData;
      expect(state.redemptions.ids).toHaveLength(2);
      expect(state.redemptions.entities['offer-1_2023-01-01T10:00:00Z']).toBeDefined();
      expect(state.redemptions.entities['offer-2_2023-01-02T10:00:00Z']).toBeDefined();
    });

    it('should add individual redemption from Realm', () => {
      const mockRedemption = {
        id: 'redemption-1',
        offerId: 'offer-1',
        cardId: 'card-1',
        timestamp: '2023-01-01T10:00:00Z',
      };

      store.dispatch(offerDataSlice.actions.addRedemptionFromRealm(mockRedemption));

      const state = store.getState().offerData;
      expect(state.redemptions.ids).toContain('offer-1_2023-01-01T10:00:00Z');
      expect(state.redemptions.entities['offer-1_2023-01-01T10:00:00Z']).toEqual(mockRedemption);
    });
  });

  describe('clearError', () => {
    it('should clear error state', () => {
      store.dispatch(offerDataSlice.actions.clearError());
      
      const state = store.getState().offerData;
      expect(state.error).toBe(null);
    });
  });
});
