import { configureStore } from '@reduxjs/toolkit';
import { offerDataSlice } from '../offerDataSlice';
import { offerUISlice } from '../offerUISlice';
import { 
  selectOfferMetrics, 
  selectFilteredOffers, 
  selectSelectedOffer,
  selectOfferDisplayInfo,
  selectActiveOffers 
} from '../offerSelectors';
import { Offer } from '../../../../../data/models/Offer';

describe('Offer Slices Integration', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        offerData: offerDataSlice.reducer,
        offerUI: offerUISlice.reducer,
      },
    });
  });

  const mockOffers: Offer[] = [
    {
      id: 'offer-1',
      masterId: 'master-1',
      name: 'Active Discount',
      title: 'Great Deal',
      description: 'Save 20% on your purchase',
      kind: 'discount',
      state: 'active',
      visible: true,
      createdAt: new Date('2023-01-01'),
      startTime: new Date('2023-01-01'),
      endTime: new Date('2025-12-31'), // Active
      barcode: '123456789',
    } as Offer,
    {
      id: 'offer-2',
      masterId: 'master-2',
      name: 'Pending Voucher',
      title: 'Amazing Voucher',
      description: 'Free coffee with purchase',
      kind: 'voucher',
      state: 'pending',
      visible: true,
      createdAt: new Date('2023-01-02'),
      startTime: new Date('2023-01-02'),
      endTime: new Date('2025-12-31'), // Active
    } as Offer,
    {
      id: 'offer-3',
      masterId: 'master-1',
      name: 'Hidden Offer',
      title: 'Secret Deal',
      description: 'Special offer',
      kind: 'discount',
      state: 'active',
      visible: false,
      createdAt: new Date('2023-01-03'),
      startTime: new Date('2023-01-03'),
      endTime: new Date('2022-12-31'), // Expired
    } as Offer,
  ];

  describe('Complete offer flow', () => {
    it('should handle offer data sync and UI interactions', () => {
      // 1. Sync offers from Realm
      store.dispatch(offerDataSlice.actions.syncFromRealm(mockOffers));

      // 2. Verify metrics are computed correctly
      const metrics = selectOfferMetrics(store.getState());
      expect(metrics.totalOffers).toBe(3);
      expect(metrics.activeOffers).toBe(1); // Only offer-1 is active and not expired
      expect(metrics.expiredOffers).toBe(1); // offer-3 is expired
      expect(metrics.visibleOffers).toBe(2); // offer-1 and offer-2
      expect(metrics.offersByKind.discount).toBe(2);
      expect(metrics.offersByKind.voucher).toBe(1);

      // 3. Test offer display info
      const displayInfo = selectOfferDisplayInfo(store.getState());
      expect(displayInfo).toHaveLength(3);
      expect(displayInfo[0].name).toBe('Active Discount');
      expect(displayInfo[0].hasBarcode).toBe(true);
      expect(displayInfo[0].isActive).toBe(true);
      expect(displayInfo[1].name).toBe('Pending Voucher');
      expect(displayInfo[1].hasBarcode).toBe(false);

      // 4. Select an offer
      store.dispatch(offerUISlice.actions.selectOffer('offer-1'));
      const selectedOffer = selectSelectedOffer(store.getState());
      expect(selectedOffer?.id).toBe('offer-1');
      expect(selectedOffer?.name).toBe('Active Discount');

      // 5. Apply filters
      store.dispatch(offerUISlice.actions.setFilters({ kind: 'discount' }));
      const filteredOffers = selectFilteredOffers(store.getState());
      expect(filteredOffers).toHaveLength(2);
      expect(filteredOffers.every(offer => offer.kind === 'discount')).toBe(true);

      // 6. Apply search filter
      store.dispatch(offerUISlice.actions.setFilters({ 
        kind: 'discount',
        searchTerm: 'Active'
      }));
      const searchedOffers = selectFilteredOffers(store.getState());
      expect(searchedOffers).toHaveLength(1);
      expect(searchedOffers[0].name).toBe('Active Discount');

      // 7. Change sorting
      store.dispatch(offerUISlice.actions.setSorting({ 
        sortBy: 'name', 
        sortOrder: 'asc' 
      }));
      const sortedOffers = selectFilteredOffers(store.getState());
      expect(sortedOffers[0].name).toBe('Active Discount');

      // 8. Clear filters
      store.dispatch(offerUISlice.actions.clearFilters());
      const allOffers = selectFilteredOffers(store.getState());
      expect(allOffers).toHaveLength(3);
    });

    it('should handle offer updates through Realm', () => {
      // 1. Initial sync
      store.dispatch(offerDataSlice.actions.syncFromRealm(mockOffers));

      // 2. Select an offer
      store.dispatch(offerUISlice.actions.selectOffer('offer-2'));
      let selectedOffer = selectSelectedOffer(store.getState());
      expect(selectedOffer?.state).toBe('pending');

      // 3. Update offer through Realm (simulating activation)
      const updatedOffer: Offer = {
        ...mockOffers[1],
        state: 'active',
        modifiedAt: new Date(),
      };
      store.dispatch(offerDataSlice.actions.updateOfferFromRealm(updatedOffer));

      // 4. Verify selected offer is updated
      selectedOffer = selectSelectedOffer(store.getState());
      expect(selectedOffer?.state).toBe('active');

      // 5. Verify metrics are updated
      const metrics = selectOfferMetrics(store.getState());
      expect(metrics.activeOffers).toBe(2); // Now both offer-1 and offer-2 are active
    });

    it('should handle offer removal', () => {
      // 1. Initial sync
      store.dispatch(offerDataSlice.actions.syncFromRealm(mockOffers));
      
      // 2. Select an offer
      store.dispatch(offerUISlice.actions.selectOffer('offer-1'));
      expect(selectSelectedOffer(store.getState())?.id).toBe('offer-1');

      // 3. Remove the selected offer
      store.dispatch(offerDataSlice.actions.removeOfferFromRealm('offer-1'));

      // 4. Verify offer is removed and selection is cleared
      const selectedOffer = selectSelectedOffer(store.getState());
      expect(selectedOffer).toBe(null);

      // 5. Verify metrics are updated
      const metrics = selectOfferMetrics(store.getState());
      expect(metrics.totalOffers).toBe(2);
    });

    it('should handle complex filtering scenarios', () => {
      // 1. Initial sync
      store.dispatch(offerDataSlice.actions.syncFromRealm(mockOffers));

      // 2. Filter by visibility
      store.dispatch(offerUISlice.actions.setFilters({ isVisible: true }));
      let filtered = selectFilteredOffers(store.getState());
      expect(filtered).toHaveLength(2);

      // 3. Filter by active status
      store.dispatch(offerUISlice.actions.setFilters({ 
        isVisible: true,
        isActive: true 
      }));
      filtered = selectFilteredOffers(store.getState());
      expect(filtered).toHaveLength(1);
      expect(filtered[0].id).toBe('offer-1');

      // 4. Filter by barcode presence
      store.dispatch(offerUISlice.actions.setFilters({ 
        hasBarcode: true
      }));
      filtered = selectFilteredOffers(store.getState());
      expect(filtered).toHaveLength(1);
      expect(filtered[0].id).toBe('offer-1');

      // 5. Filter by master ID
      store.dispatch(offerUISlice.actions.setFilters({ 
        masterId: 'master-1'
      }));
      filtered = selectFilteredOffers(store.getState());
      expect(filtered).toHaveLength(2); // offer-1 and offer-3
      expect(filtered.every(offer => offer.masterId === 'master-1')).toBe(true);
    });

    it('should handle view mode changes', () => {
      // 1. Initial state
      expect(store.getState().offerUI.viewMode).toBe('list');

      // 2. Change to grid view
      store.dispatch(offerUISlice.actions.setViewMode('grid'));
      expect(store.getState().offerUI.viewMode).toBe('grid');

      // 3. Change to map view
      store.dispatch(offerUISlice.actions.setViewMode('map'));
      expect(store.getState().offerUI.viewMode).toBe('map');
    });

    it('should handle redemption tracking', () => {
      // 1. Initial sync
      store.dispatch(offerDataSlice.actions.syncFromRealm(mockOffers));

      // 2. Add redemptions
      const mockRedemptions = [
        {
          id: 'redemption-1',
          offerId: 'offer-1',
          cardId: 'card-1',
          timestamp: '2023-01-01T10:00:00Z',
        },
        {
          id: 'redemption-2',
          offerId: 'offer-1',
          cardId: 'card-2',
          timestamp: '2023-01-02T10:00:00Z',
        },
      ];

      store.dispatch(offerDataSlice.actions.syncRedemptionsFromRealm(mockRedemptions));

      // 3. Verify redemptions are tracked
      const state = store.getState().offerData;
      expect(state.redemptions.ids).toHaveLength(2);

      // 4. Verify metrics include redemptions
      const metrics = selectOfferMetrics(store.getState());
      expect(metrics.redeemedOffers).toBe(2);
    });
  });

  describe('Active offers selector', () => {
    it('should correctly identify active offers', () => {
      // 1. Initial sync
      store.dispatch(offerDataSlice.actions.syncFromRealm(mockOffers));

      // 2. Get active offers
      const activeOffers = selectActiveOffers(store.getState());
      
      // 3. Should only include offer-1 (active, visible, not expired)
      expect(activeOffers).toHaveLength(1);
      expect(activeOffers[0].id).toBe('offer-1');
    });
  });

  describe('Error handling', () => {
    it('should maintain UI state when data operations fail', () => {
      // 1. Set up UI state
      store.dispatch(offerUISlice.actions.selectOffer('offer-1'));
      store.dispatch(offerUISlice.actions.setFilters({ kind: 'discount' }));
      store.dispatch(offerUISlice.actions.setViewMode('grid'));

      // 2. Simulate data error
      store.dispatch(offerDataSlice.actions.clearError());
      
      // 3. Verify UI state is preserved
      const uiState = store.getState().offerUI;
      expect(uiState.selectedOfferId).toBe('offer-1');
      expect(uiState.filters.kind).toBe('discount');
      expect(uiState.viewMode).toBe('grid');
    });
  });
});
