// Offer slice exports - clean re-exports to avoid importing entire slices

// Slices
export { offerDataSlice, offersAdapter, redemptionsAdapter } from './offerDataSlice';
export { offerUISlice } from './offerUISlice';

// Actions from offerDataSlice
export {
  clearError,
  syncFromRealm,
  addOfferFromRealm,
  updateOfferFromRealm,
  removeOfferFromRealm,
  syncRedemptionsFromRealm,
  addRedemptionFromRealm,
  setRealmConnectionStatus,
  createOfferOptimistic,
  updateOfferOptimistic,
  redeemOfferOptimistic,
} from './offerDataSlice';

// Actions from offerUISlice
export {
  selectOffer,
  setFilters,
  clearFilters,
  setSorting,
  toggleSortOrder,
  setViewMode,
  resetUI,
} from './offerUISlice';

// All selectors
export * from './offerSelectors';

// Types
export type { OfferDataState } from './offerDataSlice';
export type { OfferUIState } from './offerUISlice';
