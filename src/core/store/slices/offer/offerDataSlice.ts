import { createEntityAdapter, createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Offer } from '../../../../data/models/Offer';
import { CreateOfferData, UpdateOfferData, RedeemOfferData } from '../../types/offer';
import { OfferService } from '../../../services/OfferService';
import { RootState } from '../../types';

// Entity adapter for normalized Offer state
export const offersAdapter = createEntityAdapter<Offer>({
  // Use Realm's primary key
  selectId: (offer) => offer.id,
  // Sort by creation date (newest first)
  sortComparer: (a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
});

// Entity adapter for redemptions
export const redemptionsAdapter = createEntityAdapter<any>({
  // Use composite key for redemptions
  selectId: (redemption) => `${redemption.offerId}_${redemption.timestamp}`,
  // Sort by timestamp (newest first)
  sortComparer: (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime(),
});

export interface OfferDataState {
  loading: boolean;
  error: string | null;
  lastSync: string | null;
  realmConnected: boolean;
  redemptions: ReturnType<typeof redemptionsAdapter.getInitialState>;
}

// Initialize with adapter's initial state
const initialState = offersAdapter.getInitialState<OfferDataState>({
  loading: false,
  error: null,
  lastSync: null,
  realmConnected: false,
  redemptions: redemptionsAdapter.getInitialState(),
});

// Async thunks for optimistic operations
export const createOfferOptimistic = createAsyncThunk(
  'offerData/createOptimistic',
  async (data: CreateOfferData) => {
    // This will update Realm immediately and queue for sync
    return await OfferService.createOffer(data);
  }
);

export const updateOfferOptimistic = createAsyncThunk(
  'offerData/updateOptimistic',
  async ({ id, changes }: { id: string; changes: UpdateOfferData }) => {
    // This will update Realm immediately and queue for sync
    return await OfferService.updateOffer(id, changes);
  }
);

export const redeemOfferOptimistic = createAsyncThunk(
  'offerData/redeemOptimistic',
  async (data: RedeemOfferData) => {
    // This will update Realm immediately and queue for sync
    return await OfferService.redeemOffer(data);
  }
);

export const offerDataSlice = createSlice({
  name: 'offerData',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    
    // Realm integration: Sync from Realm to Redux
    syncFromRealm: (state, action: PayloadAction<Offer[]>) => {
      offersAdapter.setAll(state, action.payload);
      state.lastSync = new Date().toISOString();
      state.realmConnected = true;
    },
    
    // Realm integration: Handle individual Realm changes
    addOfferFromRealm: (state, action: PayloadAction<Offer>) => {
      offersAdapter.addOne(state, action.payload);
    },
    
    updateOfferFromRealm: (state, action: PayloadAction<Offer>) => {
      offersAdapter.updateOne(state, {
        id: action.payload.id,
        changes: action.payload,
      });
    },
    
    removeOfferFromRealm: (state, action: PayloadAction<string>) => {
      offersAdapter.removeOne(state, action.payload);
    },
    
    // Redemption tracking
    syncRedemptionsFromRealm: (state, action: PayloadAction<any[]>) => {
      redemptionsAdapter.setAll(state.redemptions, action.payload);
    },
    
    addRedemptionFromRealm: (state, action: PayloadAction<any>) => {
      redemptionsAdapter.addOne(state.redemptions, action.payload);
    },
    
    setRealmConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.realmConnected = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Create offer optimistic
    builder
      .addCase(createOfferOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createOfferOptimistic.fulfilled, (state) => {
        // Realm listener will handle the actual Redux update
        // This is just for loading state management
        state.loading = false;
      })
      .addCase(createOfferOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create offer';
      });

    // Update offer optimistic
    builder
      .addCase(updateOfferOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateOfferOptimistic.fulfilled, (state) => {
        // Realm listener will handle the actual Redux update
        state.loading = false;
      })
      .addCase(updateOfferOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update offer';
      });

    // Redeem offer optimistic
    builder
      .addCase(redeemOfferOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(redeemOfferOptimistic.fulfilled, (state) => {
        // Realm listener will handle the actual Redux update
        state.loading = false;
      })
      .addCase(redeemOfferOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to redeem offer';
      });
  },
});

export const {
  clearError,
  syncFromRealm,
  addOfferFromRealm,
  updateOfferFromRealm,
  removeOfferFromRealm,
  syncRedemptionsFromRealm,
  addRedemptionFromRealm,
  setRealmConnectionStatus,
} = offerDataSlice.actions;

export default offerDataSlice.reducer;
