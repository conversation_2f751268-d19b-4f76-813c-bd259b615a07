import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../types';
import { Offer } from '../../../../data/models/Offer';
import { OfferDisplayInfo, OfferMetrics, OfferOptionsTyped, OfferPlaceTyped } from '../../types/offer';
import { offersAdapter, redemptionsAdapter } from './offerDataSlice';

// Base selectors from entity adapter (using actual Realm Offer model)
export const offerDataSelectors = offersAdapter.getSelectors(
  (state: RootState) => state.offerData
);

// Redemption selectors
export const redemptionSelectors = redemptionsAdapter.getSelectors(
  (state: RootState) => state.offerData.redemptions
);

// UI selectors
export const selectOfferUI = (state: RootState) => state.offerUI;
export const selectSelectedOfferId = (state: RootState) => state.offerUI.selectedOfferId;
export const selectOfferFilters = (state: RootState) => state.offerUI.filters;
export const selectOfferSorting = (state: RootState) => ({
  sortBy: state.offerUI.sortBy,
  sortOrder: state.offerUI.sortOrder,
});
export const selectOfferViewMode = (state: RootState) => state.offerUI.viewMode;

// Data state selectors
export const selectOfferDataState = (state: RootState) => ({
  loading: state.offerData.loading,
  error: state.offerData.error,
  lastSync: state.offerData.lastSync,
  realmConnected: state.offerData.realmConnected,
});

// Computed selectors using actual Realm fields
export const selectOfferMetrics = createSelector(
  [offerDataSelectors.selectAll, redemptionSelectors.selectAll],
  (offers, redemptions): OfferMetrics => {
    const now = new Date();
    
    return {
      totalOffers: offers.length,
      activeOffers: offers.filter(o => 
        o.startTime && o.endTime && 
        o.startTime <= now && 
        o.endTime > now &&
        (o.visible ?? true)
      ).length,
      expiredOffers: offers.filter(o => 
        o.endTime && o.endTime < now
      ).length,
      redeemedOffers: redemptions.length,
      visibleOffers: offers.filter(o => o.visible !== false).length,
      offersByKind: offers.reduce((acc, offer) => {
        const kind = offer.kind || 'unknown';
        acc[kind] = (acc[kind] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      offersByState: offers.reduce((acc, offer) => {
        const state = offer.state || 'unknown';
        acc[state] = (acc[state] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };
  }
);

// UI-friendly offer display info
export const selectOfferDisplayInfo = createSelector(
  [offerDataSelectors.selectAll],
  (offers): OfferDisplayInfo[] => offers.map(offer => {
    const now = new Date();
    const isActive = offer.startTime && offer.endTime && 
                    offer.startTime <= now && 
                    offer.endTime > now;
    
    return {
      id: offer.id,
      masterId: offer.masterId,
      name: offer.name,
      shortName: offer.shortName,
      title: offer.title,
      description: offer.description,
      terms: offer.terms,
      kind: offer.kind,
      state: offer.state,
      isVisible: offer.visible ?? true,
      hasBarcode: !!(offer.barcode),
      isActive: !!isActive,
    };
  })
);

// Filtered offers using actual Realm fields
export const selectFilteredOffers = createSelector(
  [offerDataSelectors.selectAll, selectOfferFilters, selectOfferSorting],
  (offers, filters, sorting) => {
    let filtered = offers;
    const now = new Date();

    // Apply filters using actual Realm fields
    if (filters.kind) {
      filtered = filtered.filter(offer => offer.kind === filters.kind);
    }
    if (filters.state) {
      filtered = filtered.filter(offer => offer.state === filters.state);
    }
    if (filters.masterId) {
      filtered = filtered.filter(offer => offer.masterId === filters.masterId);
    }
    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(offer => 
        (offer.name || '').toLowerCase().includes(term) ||
        (offer.title || '').toLowerCase().includes(term) ||
        (offer.description || '').toLowerCase().includes(term)
      );
    }
    if (filters.isActive !== undefined) {
      filtered = filtered.filter(offer => {
        const isActive = offer.startTime && offer.endTime && 
                        offer.startTime <= now && 
                        offer.endTime > now;
        return !!isActive === filters.isActive;
      });
    }
    if (filters.isVisible !== undefined) {
      filtered = filtered.filter(offer => (offer.visible ?? true) === filters.isVisible);
    }
    if (filters.hasBarcode !== undefined) {
      filtered = filtered.filter(offer => !!offer.barcode === filters.hasBarcode);
    }

    // Apply sorting using actual Realm fields
    return filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sorting.sortBy) {
        case 'name':
          aValue = a.name || a.title || '';
          bValue = b.name || b.title || '';
          break;
        case 'created':
          aValue = a.createdAt;
          bValue = b.createdAt;
          break;
        case 'startTime':
          aValue = a.startTime || new Date(0);
          bValue = b.startTime || new Date(0);
          break;
        case 'endTime':
          aValue = a.endTime || new Date(0);
          bValue = b.endTime || new Date(0);
          break;
        default:
          return 0;
      }
      
      const order = sorting.sortOrder === 'asc' ? 1 : -1;
      return aValue > bValue ? order : -order;
    });
  }
);

// Selected offer with proper typing
export const selectSelectedOffer = createSelector(
  [offerDataSelectors.selectAll, selectSelectedOfferId],
  (offers, selectedId): Offer | null => 
    selectedId ? offers.find(offer => offer.id === selectedId) || null : null
);

// Offer by ID selector factory
export const selectOfferById = (offerId: string) => createSelector(
  [offerDataSelectors.selectById],
  (offer): Offer | null => offer || null
);

// Selectors for Realm.Mixed fields
export const selectOfferOptions = (offerId: string) => createSelector(
  [(state: RootState) => offerDataSelectors.selectById(state, offerId)],
  (offer): OfferOptionsTyped | null => offer ? (offer.options as OfferOptionsTyped) || null : null
);

export const selectOfferPlace = (offerId: string) => createSelector(
  [(state: RootState) => offerDataSelectors.selectById(state, offerId)],
  (offer): OfferPlaceTyped | null => offer ? (offer.place as OfferPlaceTyped) || null : null
);

// Offers by master
export const selectOffersByMaster = (masterId: string) => createSelector(
  [offerDataSelectors.selectAll],
  (offers) => offers.filter(offer => offer.masterId === masterId)
);

// Active offers
export const selectActiveOffers = createSelector(
  [offerDataSelectors.selectAll],
  (offers) => {
    const now = new Date();
    return offers.filter(offer => 
      offer.startTime && offer.endTime && 
      offer.startTime <= now && 
      offer.endTime > now &&
      (offer.visible ?? true)
    );
  }
);

// Offers by kind
export const selectOffersByKind = (kind: string) => createSelector(
  [offerDataSelectors.selectAll],
  (offers) => offers.filter(offer => offer.kind === kind)
);

// Redemptions for an offer
export const selectRedemptionsForOffer = (offerId: string) => createSelector(
  [redemptionSelectors.selectAll],
  (redemptions) => redemptions.filter(redemption => redemption.offerId === offerId)
);
