import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { OfferUIFilters } from '../../types/offer';

export interface OfferUIState {
  selectedOfferId: string | null;
  filters: OfferUIFilters;
  sortBy: 'name' | 'created' | 'startTime' | 'endTime';
  sortOrder: 'asc' | 'desc';
  viewMode: 'list' | 'grid' | 'map';
}

const initialState: OfferUIState = {
  selectedOfferId: null,
  filters: {},
  sortBy: 'created',
  sortOrder: 'desc',
  viewMode: 'list',
};

export const offerUISlice = createSlice({
  name: 'offerUI',
  initialState,
  reducers: {
    selectOffer: (state, action: PayloadAction<string | null>) => {
      state.selectedOfferId = action.payload;
    },
    
    setFilters: (state, action: PayloadAction<Partial<OfferUIFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    clearFilters: (state) => {
      state.filters = {};
    },
    
    setSorting: (state, action: PayloadAction<{ 
      sortBy: OfferUIState['sortBy']; 
      sortOrder: OfferUIState['sortOrder']; 
    }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    setViewMode: (state, action: PayloadAction<OfferUIState['viewMode']>) => {
      state.viewMode = action.payload;
    },
    
    resetUI: (state) => {
      state.selectedOfferId = null;
      state.filters = {};
      state.sortBy = 'created';
      state.sortOrder = 'desc';
      state.viewMode = 'list';
    },
  },
});

export const {
  selectOffer,
  setFilters,
  clearFilters,
  setSorting,
  toggleSortOrder,
  setViewMode,
  resetUI,
} = offerUISlice.actions;

export default offerUISlice.reducer;
