import { configureStore } from '@reduxjs/toolkit';
import {
  personDataSlice,
  personsAdapter,
  createPersonOptimistic,
  updatePersonOptimistic,
  updatePersonPreferencesOptimistic,
  updatePersonAuthenticationOptimistic,
  updatePersonProfileOptimistic
} from '../personDataSlice';
import { PersonService, Person } from '../../../../services/PersonService';

// Mock PersonService
jest.mock('../../../../services/PersonService');
const mockPersonService = PersonService as jest.Mocked<typeof PersonService>;

describe('PersonDataSlice', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        personData: personDataSlice.reducer,
      },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
          serializableCheck: {
            ignoredActions: [
              // Realm sync actions with Date objects
              'personData/syncFromRealm',
              'personData/addPersonFromRealm',
              'personData/updatePersonFromRealm',
              'personData/createOptimistic/fulfilled',
              'personData/updateOptimistic/fulfilled',
              'personData/loadCurrentOptimistic/fulfilled',
              'personData/updatePreferencesOptimistic/fulfilled',
              'personData/updateAuthenticationOptimistic/fulfilled',
              'personData/updateProfileOptimistic/fulfilled',
            ],
            ignoredPaths: [
              // Ignore Date objects in state
              'personData.entities',
              'personData.lastSync',
            ],
          },
        }),
    });
    jest.clearAllMocks();
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().personData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(null);
      expect(state.lastSync).toBe(null);
      expect(state.realmConnected).toBe(false);
      expect(state.currentPersonId).toBe(null);
      expect(state.ids).toEqual([]);
      expect(state.entities).toEqual({});
    });
  });

  describe('syncFromRealm', () => {
    it('should sync persons from Realm', () => {
      const mockPersons: Person[] = [
        {
          id: 'person-1',
          givenName: 'John',
          familyName: 'Doe',
          fullName: 'John Doe',
          gender: 'male',
          createdAt: new Date('2023-01-01'),
          modifiedAt: new Date('2023-01-01'),
        } as Person,
        {
          id: 'person-2',
          givenName: 'Jane',
          familyName: 'Smith',
          fullName: 'Jane Smith',
          gender: 'female',
          createdAt: new Date('2023-01-02'),
          modifiedAt: new Date('2023-01-02'),
        } as Person,
      ];

      store.dispatch(personDataSlice.actions.syncFromRealm(mockPersons));

      const state = store.getState().personData;
      expect(state.ids).toHaveLength(2);
      expect(state.entities['person-1']).toEqual(mockPersons[0]);
      expect(state.entities['person-2']).toEqual(mockPersons[1]);
      expect(state.lastSync).toBeDefined();
      expect(state.realmConnected).toBe(true);
    });
  });

  describe('addPersonFromRealm', () => {
    it('should add a single person from Realm', () => {
      const mockPerson: Person = {
        id: 'person-1',
        givenName: 'John',
        familyName: 'Doe',
        fullName: 'John Doe',
        gender: 'male',
        createdAt: new Date('2023-01-01'),
        modifiedAt: new Date('2023-01-01'),
      } as Person;

      store.dispatch(personDataSlice.actions.addPersonFromRealm(mockPerson));

      const state = store.getState().personData;
      expect(state.ids).toContain('person-1');
      expect(state.entities['person-1']).toEqual(mockPerson);
    });
  });

  describe('updatePersonFromRealm', () => {
    it('should update an existing person from Realm', () => {
      const initialPerson: Person = {
        id: 'person-1',
        givenName: 'John',
        familyName: 'Doe',
        fullName: 'John Doe',
        gender: 'male',
        createdAt: new Date('2023-01-01'),
        modifiedAt: new Date('2023-01-01'),
      } as Person;

      const updatedPerson: Person = {
        ...initialPerson,
        familyName: 'Johnson',
        fullName: 'John Johnson',
        modifiedAt: new Date('2023-01-02'),
      };

      // Add initial person
      store.dispatch(personDataSlice.actions.addPersonFromRealm(initialPerson));
      
      // Update person
      store.dispatch(personDataSlice.actions.updatePersonFromRealm(updatedPerson));

      const state = store.getState().personData;
      expect(state.entities['person-1']?.familyName).toBe('Johnson');
      expect(state.entities['person-1']?.fullName).toBe('John Johnson');
    });
  });

  describe('removePersonFromRealm', () => {
    it('should remove a person from state', () => {
      const mockPerson: Person = {
        id: 'person-1',
        givenName: 'John',
        familyName: 'Doe',
        fullName: 'John Doe',
        gender: 'male',
        createdAt: new Date('2023-01-01'),
        modifiedAt: new Date('2023-01-01'),
      } as Person;

      // Add person
      store.dispatch(personDataSlice.actions.addPersonFromRealm(mockPerson));
      expect(store.getState().personData.ids).toContain('person-1');

      // Remove person
      store.dispatch(personDataSlice.actions.removePersonFromRealm('person-1'));
      
      const state = store.getState().personData;
      expect(state.ids).not.toContain('person-1');
      expect(state.entities['person-1']).toBeUndefined();
    });

    it('should clear current person if removed person was current', () => {
      const mockPerson: Person = {
        id: 'person-1',
        givenName: 'John',
        familyName: 'Doe',
        fullName: 'John Doe',
        gender: 'male',
        createdAt: new Date('2023-01-01'),
        modifiedAt: new Date('2023-01-01'),
      } as Person;

      // Add person and set as current
      store.dispatch(personDataSlice.actions.addPersonFromRealm(mockPerson));
      store.dispatch(personDataSlice.actions.setCurrentPerson('person-1'));
      expect(store.getState().personData.currentPersonId).toBe('person-1');

      // Remove person
      store.dispatch(personDataSlice.actions.removePersonFromRealm('person-1'));
      
      const state = store.getState().personData;
      expect(state.currentPersonId).toBe(null);
    });
  });

  describe('setCurrentPerson', () => {
    it('should set current person ID', () => {
      store.dispatch(personDataSlice.actions.setCurrentPerson('person-1'));
      
      const state = store.getState().personData;
      expect(state.currentPersonId).toBe('person-1');
    });

    it('should clear current person ID', () => {
      store.dispatch(personDataSlice.actions.setCurrentPerson('person-1'));
      store.dispatch(personDataSlice.actions.setCurrentPerson(null));
      
      const state = store.getState().personData;
      expect(state.currentPersonId).toBe(null);
    });
  });

  describe('createPersonOptimistic', () => {
    it('should handle person creation successfully', async () => {
      const createData = {
        givenName: 'John',
        familyName: 'Doe',
        gender: 'male' as const,
      };

      const mockCreatedPerson: Person = {
        id: 'person-new',
        ...createData,
        fullName: 'John Doe',
        createdAt: new Date(),
        modifiedAt: new Date(),
      } as Person;

      mockPersonService.createPerson.mockResolvedValue(mockCreatedPerson);

      await store.dispatch(createPersonOptimistic(createData));

      const state = store.getState().personData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(null);
      expect(mockPersonService.createPerson).toHaveBeenCalledWith(createData);
    });

    it('should handle person creation failure', async () => {
      const createData = {
        givenName: 'John',
        familyName: 'Doe',
        gender: 'male' as const,
      };

      const errorMessage = 'Failed to create person';
      mockPersonService.createPerson.mockRejectedValue(new Error(errorMessage));

      await store.dispatch(createPersonOptimistic(createData));

      const state = store.getState().personData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(errorMessage);
    });
  });

  describe('updatePersonOptimistic', () => {
    it('should handle person update successfully', async () => {
      const updateData = {
        id: 'person-1',
        changes: {
          familyName: 'Johnson',
          fullName: 'John Johnson',
        },
      };

      const mockUpdatedPerson: Person = {
        id: 'person-1',
        givenName: 'John',
        familyName: 'Johnson',
        fullName: 'John Johnson',
        gender: 'male',
        createdAt: new Date('2023-01-01'),
        modifiedAt: new Date(),
      } as Person;

      mockPersonService.updatePerson.mockResolvedValue(mockUpdatedPerson);

      await store.dispatch(updatePersonOptimistic(updateData));

      const state = store.getState().personData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(null);
      expect(mockPersonService.updatePerson).toHaveBeenCalledWith(updateData.id, updateData.changes);
    });

    it('should handle person update failure', async () => {
      const updateData = {
        id: 'person-1',
        changes: {
          familyName: 'Johnson',
        },
      };

      const errorMessage = 'Failed to update person';
      mockPersonService.updatePerson.mockRejectedValue(new Error(errorMessage));

      await store.dispatch(updatePersonOptimistic(updateData));

      const state = store.getState().personData;
      expect(state.loading).toBe(false);
      expect(state.error).toBe(errorMessage);
    });
  });

  describe('clearError', () => {
    it('should clear error state', () => {
      store.dispatch(personDataSlice.actions.clearError());

      const state = store.getState().personData;
      expect(state.error).toBe(null);
    });
  });

  describe('Profile functionality (new thunks)', () => {
    const mockPerson: Person = {
      id: 'person-1',
      profile: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '+1234567890',
      },
      preferences: {
        language: 'en',
        timezone: 'UTC',
        currency: 'USD',
        theme: 'auto',
        biometrics: false,
        notifications: {
          email: true,
          push: true,
          sms: false,
        },
        privacy: {
          shareData: false,
          analytics: true,
          marketing: false,
        },
      },
      authentication: {
        isAuthenticated: true,
        isVerified: false,
        verificationMethod: 'none',
        loginCount: 0,
      },
      lifecycle: {
        state: 'active',
        createdAt: new Date(),
        lastModified: new Date(),
      },
      metadata: {
        source: 'test',
        version: 1,
        tags: [],
      },
    };

    beforeEach(() => {
      // Add a person to the store
      store.dispatch(personDataSlice.actions.addPersonFromRealm(mockPerson));
      store.dispatch(personDataSlice.actions.setCurrentPerson('person-1'));
    });

    describe('updatePersonPreferencesOptimistic', () => {
      it('should update person preferences', async () => {
        const updatedPerson = { ...mockPerson };
        updatedPerson.preferences.theme = 'dark';
        updatedPerson.preferences.biometrics = true;

        mockPersonService.updatePerson.mockResolvedValue(updatedPerson);

        const action = store.dispatch(updatePersonPreferencesOptimistic({
          id: 'person-1',
          preferences: { theme: 'dark', biometrics: true }
        }));

        // Check pending state
        let state = store.getState().personData;
        expect(state.loading).toBe(true);

        // Wait for completion
        await action;

        // Check fulfilled state
        state = store.getState().personData;
        expect(state.loading).toBe(false);
        expect(mockPersonService.updatePerson).toHaveBeenCalledWith('person-1', {
          preferences: { theme: 'dark', biometrics: true }
        });
      });
    });

    describe('updatePersonAuthenticationOptimistic', () => {
      it('should update person authentication', async () => {
        const updatedPerson = { ...mockPerson };
        updatedPerson.authentication.isVerified = true;
        updatedPerson.authentication.verificationMethod = 'email';

        mockPersonService.updatePerson.mockResolvedValue(updatedPerson);

        const action = store.dispatch(updatePersonAuthenticationOptimistic({
          id: 'person-1',
          authentication: { isVerified: true, verificationMethod: 'email' }
        }));

        // Check pending state
        let state = store.getState().personData;
        expect(state.loading).toBe(true);

        // Wait for completion
        await action;

        // Check fulfilled state
        state = store.getState().personData;
        expect(state.loading).toBe(false);
        expect(mockPersonService.updatePerson).toHaveBeenCalledWith('person-1', {
          authentication: { isVerified: true, verificationMethod: 'email' }
        });
      });
    });

    describe('updatePersonProfileOptimistic', () => {
      it('should update person profile', async () => {
        const updatedPerson = { ...mockPerson };
        updatedPerson.profile.phone = '+9876543210';
        updatedPerson.profile.dateOfBirth = new Date('1990-01-01');

        mockPersonService.updatePerson.mockResolvedValue(updatedPerson);

        const action = store.dispatch(updatePersonProfileOptimistic({
          id: 'person-1',
          profile: { phone: '+9876543210', dateOfBirth: new Date('1990-01-01') }
        }));

        // Check pending state
        let state = store.getState().personData;
        expect(state.loading).toBe(true);

        // Wait for completion
        await action;

        // Check fulfilled state
        state = store.getState().personData;
        expect(state.loading).toBe(false);
        expect(mockPersonService.updatePerson).toHaveBeenCalledWith('person-1', {
          profile: { phone: '+9876543210', dateOfBirth: new Date('1990-01-01') }
        });
      });
    });
  });
});
