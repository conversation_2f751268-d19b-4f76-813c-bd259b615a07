import { configureStore } from '@reduxjs/toolkit';
import { personUISlice } from '../personUISlice';

describe('PersonUISlice', () => {
  let store: ReturnType<typeof configureStore>;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        personUI: personUISlice.reducer,
      },
    });
  });

  describe('initial state', () => {
    it('should have correct initial state', () => {
      const state = store.getState().personUI;
      
      // List management
      expect(state.selectedPersonId).toBeNull();
      expect(state.filters).toEqual({});
      expect(state.sortBy).toBe('modified');
      expect(state.sortOrder).toBe('desc');
      expect(state.viewMode).toBe('list');
      expect(state.editMode).toBe(false);
      expect(state.showInactive).toBe(false);
      
      // Profile UI
      expect(state.profile.isEditing).toBe(false);
      expect(state.profile.editingField).toBeNull();
      expect(state.profile.profileViewMode).toBe('view');
      expect(state.profile.activeTab).toBe('profile');
      expect(state.profile.hasUnsavedChanges).toBe(false);
      expect(state.profile.validationErrors).toEqual({});
      expect(state.profile.modals.imageOptions).toBe(false);
      expect(state.profile.operationLoading.savingProfile).toBe(false);
    });
  });

  describe('list management actions', () => {
    it('should select person', () => {
      store.dispatch(personUISlice.actions.selectPerson('person-1'));
      
      const state = store.getState().personUI;
      expect(state.selectedPersonId).toBe('person-1');
    });

    it('should set filters', () => {
      store.dispatch(personUISlice.actions.setFilters({ search: 'John' }));
      
      const state = store.getState().personUI;
      expect(state.filters.search).toBe('John');
    });

    it('should toggle edit mode', () => {
      store.dispatch(personUISlice.actions.toggleEditMode());
      
      const state = store.getState().personUI;
      expect(state.editMode).toBe(true);
    });
  });

  describe('profile-specific actions', () => {
    it('should set profile editing mode', () => {
      store.dispatch(personUISlice.actions.setProfileEditing(true));
      
      const state = store.getState().personUI;
      expect(state.profile.isEditing).toBe(true);
    });

    it('should clear profile edit state when setting editing to false', () => {
      // Set some edit state first
      store.dispatch(personUISlice.actions.setProfileEditing(true));
      store.dispatch(personUISlice.actions.setProfileEditingField('firstName'));
      store.dispatch(personUISlice.actions.setProfileHasUnsavedChanges(true));
      store.dispatch(personUISlice.actions.setProfileValidationError({ field: 'email', error: 'Invalid email' }));

      // Turn off editing
      store.dispatch(personUISlice.actions.setProfileEditing(false));

      const state = store.getState().personUI;
      expect(state.profile.isEditing).toBe(false);
      expect(state.profile.editingField).toBeNull();
      expect(state.profile.hasUnsavedChanges).toBe(false);
      expect(state.profile.validationErrors).toEqual({});
    });

    it('should toggle profile editing mode', () => {
      // Initially false
      expect(store.getState().personUI.profile.isEditing).toBe(false);

      // Toggle to true
      store.dispatch(personUISlice.actions.toggleProfileEditing());
      expect(store.getState().personUI.profile.isEditing).toBe(true);

      // Toggle back to false
      store.dispatch(personUISlice.actions.toggleProfileEditing());
      expect(store.getState().personUI.profile.isEditing).toBe(false);
    });

    it('should set profile editing field and enable editing', () => {
      store.dispatch(personUISlice.actions.setProfileEditingField('email'));

      const state = store.getState().personUI;
      expect(state.profile.editingField).toBe('email');
      expect(state.profile.isEditing).toBe(true);
    });

    it('should set profile view mode', () => {
      store.dispatch(personUISlice.actions.setProfileViewMode('edit'));

      const state = store.getState().personUI;
      expect(state.profile.profileViewMode).toBe('edit');
    });

    it('should reset profile edit state when changing to non-edit view mode', () => {
      // Set edit state
      store.dispatch(personUISlice.actions.setProfileEditing(true));
      store.dispatch(personUISlice.actions.setProfileEditingField('name'));
      store.dispatch(personUISlice.actions.setProfileHasUnsavedChanges(true));

      // Change to view mode
      store.dispatch(personUISlice.actions.setProfileViewMode('view'));

      const state = store.getState().personUI;
      expect(state.profile.profileViewMode).toBe('view');
      expect(state.profile.isEditing).toBe(false);
      expect(state.profile.editingField).toBeNull();
      expect(state.profile.hasUnsavedChanges).toBe(false);
    });

    it('should set profile active tab', () => {
      store.dispatch(personUISlice.actions.setProfileActiveTab('preferences'));

      const state = store.getState().personUI;
      expect(state.profile.activeTab).toBe('preferences');
    });

    it('should set profile validation error', () => {
      store.dispatch(personUISlice.actions.setProfileValidationError({ 
        field: 'email', 
        error: 'Invalid email format' 
      }));

      const state = store.getState().personUI;
      expect(state.profile.validationErrors.email).toBe('Invalid email format');
    });

    it('should clear profile validation error', () => {
      // Set multiple errors
      store.dispatch(personUISlice.actions.setProfileValidationError({ 
        field: 'email', 
        error: 'Invalid email format' 
      }));
      store.dispatch(personUISlice.actions.setProfileValidationError({ 
        field: 'phone', 
        error: 'Invalid phone format' 
      }));

      // Clear one error
      store.dispatch(personUISlice.actions.clearProfileValidationError('email'));

      const state = store.getState().personUI;
      expect(state.profile.validationErrors.email).toBeUndefined();
      expect(state.profile.validationErrors.phone).toBe('Invalid phone format');
    });

    it('should open and close profile modals', () => {
      // Open modal
      store.dispatch(personUISlice.actions.openProfileModal('imageOptions'));

      let state = store.getState().personUI;
      expect(state.profile.modals.imageOptions).toBe(true);

      // Close modal
      store.dispatch(personUISlice.actions.closeProfileModal('imageOptions'));

      state = store.getState().personUI;
      expect(state.profile.modals.imageOptions).toBe(false);
    });

    it('should close all profile modals', () => {
      // Open multiple modals
      store.dispatch(personUISlice.actions.openProfileModal('imageOptions'));
      store.dispatch(personUISlice.actions.openProfileModal('preferences'));
      store.dispatch(personUISlice.actions.openProfileModal('security'));

      // Close all modals
      store.dispatch(personUISlice.actions.closeAllProfileModals());

      const state = store.getState().personUI;
      expect(state.profile.modals.imageOptions).toBe(false);
      expect(state.profile.modals.preferences).toBe(false);
      expect(state.profile.modals.security).toBe(false);
    });

    it('should set profile operation loading', () => {
      store.dispatch(personUISlice.actions.setProfileOperationLoading({ 
        operation: 'savingProfile', 
        loading: true 
      }));

      const state = store.getState().personUI;
      expect(state.profile.operationLoading.savingProfile).toBe(true);
    });

    it('should reset profile UI to initial state', () => {
      // Set various profile UI states
      store.dispatch(personUISlice.actions.setProfileEditing(true));
      store.dispatch(personUISlice.actions.setProfileActiveTab('preferences'));
      store.dispatch(personUISlice.actions.setProfileHasUnsavedChanges(true));
      store.dispatch(personUISlice.actions.openProfileModal('imageOptions'));

      // Reset profile UI
      store.dispatch(personUISlice.actions.resetProfileUI());

      const state = store.getState().personUI;
      expect(state.profile.isEditing).toBe(false);
      expect(state.profile.activeTab).toBe('profile');
      expect(state.profile.hasUnsavedChanges).toBe(false);
      expect(state.profile.modals.imageOptions).toBe(false);
    });

    it('should reset profile edit state only', () => {
      // Set various states
      store.dispatch(personUISlice.actions.setProfileEditing(true));
      store.dispatch(personUISlice.actions.setProfileEditingField('email'));
      store.dispatch(personUISlice.actions.setProfileHasUnsavedChanges(true));
      store.dispatch(personUISlice.actions.setProfileValidationError({ field: 'email', error: 'Invalid' }));
      store.dispatch(personUISlice.actions.setProfileActiveTab('preferences')); // This should remain

      // Reset only edit state
      store.dispatch(personUISlice.actions.resetProfileEditState());

      const state = store.getState().personUI;
      expect(state.profile.isEditing).toBe(false);
      expect(state.profile.editingField).toBeNull();
      expect(state.profile.hasUnsavedChanges).toBe(false);
      expect(state.profile.validationErrors).toEqual({});
      expect(state.profile.activeTab).toBe('preferences'); // Should remain unchanged
    });
  });
});
