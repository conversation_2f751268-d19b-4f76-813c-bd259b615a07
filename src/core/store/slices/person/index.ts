// Person slice exports - clean re-exports to avoid importing entire slices

// Slices
export { personDataSlice, personsAdapter } from './personDataSlice';
export { personUISlice } from './personUISlice';

// Actions from personDataSlice
export {
  clearError,
  syncFromRealm,
  addPersonFromRealm,
  updatePersonFromRealm,
  removePersonFromRealm,
  setCurrentPerson,
  setRealmConnectionStatus,
  createPersonOptimistic,
  updatePersonOptimistic,
  loadCurrentPersonOptimistic,
  updatePersonPreferencesOptimistic,
  updatePersonAuthenticationOptimistic,
  updatePersonProfileOptimistic,
} from './personDataSlice';

// Actions from personUISlice
export {
  // List management actions
  selectPerson,
  setFilters,
  clearFilters,
  setSorting,
  toggleSortOrder,
  setViewMode,
  setEditMode,
  toggleEditMode,
  setShowInactive,
  toggleShowInactive,
  resetUI,

  // Profile-specific actions
  setProfileEditing,
  toggleProfileEditing,
  setProfileEditingField,
  setProfileViewMode,
  setProfileActiveTab,
  setProfileShowImagePicker,
  setProfileShowDeleteConfirmation,
  setProfileShowLogoutConfirmation,
  setProfileHasUnsavedChanges,
  setProfileValidationError,
  clearProfileValidationError,
  clearAllProfileValidationErrors,
  openProfileModal,
  closeProfileModal,
  closeAllProfileModals,
  setProfileOperationLoading,
  resetProfileUI,
  resetProfileEditState,
} from './personUISlice';

// All selectors
export * from './personSelectors';

// Types
export type { PersonDataState } from './personDataSlice';
export type { PersonUIState } from './personUISlice';
