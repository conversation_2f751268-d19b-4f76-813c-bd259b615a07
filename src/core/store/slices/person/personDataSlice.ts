import { createEntityAdapter, createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Person, CreatePersonData, UpdatePersonData, PersonService } from '../../../services/PersonService';
import { RootState } from '../../types';

// Entity adapter for normalized Person state
export const personsAdapter = createEntityAdapter<Person>({
  // Use Realm's primary key
  selectId: (person) => person.id,
  // Sort by modification date (newest first)
  sortComparer: (a, b) => {
    const aTime = a.modifiedAt?.getTime() || a.createdAt?.getTime() || 0;
    const bTime = b.modifiedAt?.getTime() || b.createdAt?.getTime() || 0;
    return bTime - aTime;
  },
});

export interface PersonDataState {
  loading: boolean;
  error: string | null;
  lastSync: string | null;
  realmConnected: boolean;
  currentPersonId: string | null; // Track the current user
}

// Initialize with adapter's initial state
const initialState = personsAdapter.getInitialState<PersonDataState>({
  loading: false,
  error: null,
  lastSync: null,
  realmConnected: false,
  currentPersonId: null,
});

// Async thunks for optimistic operations
export const createPersonOptimistic = createAsyncThunk(
  'personData/createOptimistic',
  async (data: CreatePersonData) => {
    // This will update Realm immediately and queue for sync
    return await PersonService.createPerson(data);
  }
);

export const updatePersonOptimistic = createAsyncThunk(
  'personData/updateOptimistic',
  async ({ id, changes }: { id: string; changes: UpdatePersonData }) => {
    // This will update Realm immediately and queue for sync
    return await PersonService.updatePerson(id, changes);
  }
);

export const loadCurrentPersonOptimistic = createAsyncThunk(
  'personData/loadCurrentOptimistic',
  async (personId: string) => {
    // Load person from Realm/service
    return await PersonService.getPerson(personId);
  }
);

// New thunks for Profile functionality
export const updatePersonPreferencesOptimistic = createAsyncThunk(
  'personData/updatePreferencesOptimistic',
  async ({ id, preferences }: { id: string; preferences: Partial<Person['preferences']> }) => {
    return await PersonService.updatePerson(id, { preferences });
  }
);

export const updatePersonAuthenticationOptimistic = createAsyncThunk(
  'personData/updateAuthenticationOptimistic',
  async ({ id, authentication }: { id: string; authentication: Partial<Person['authentication']> }) => {
    return await PersonService.updatePerson(id, { authentication });
  }
);

export const updatePersonProfileOptimistic = createAsyncThunk(
  'personData/updateProfileOptimistic',
  async ({ id, profile }: { id: string; profile: Partial<Person['profile']> }) => {
    return await PersonService.updatePerson(id, { profile });
  }
);

export const personDataSlice = createSlice({
  name: 'personData',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    
    // Realm integration: Sync from Realm to Redux
    syncFromRealm: (state, action: PayloadAction<Person[]>) => {
      personsAdapter.setAll(state, action.payload);
      state.lastSync = new Date().toISOString();
      state.realmConnected = true;
    },
    
    // Realm integration: Handle individual Realm changes
    addPersonFromRealm: (state, action: PayloadAction<Person>) => {
      personsAdapter.addOne(state, action.payload);
    },
    
    updatePersonFromRealm: (state, action: PayloadAction<Person>) => {
      personsAdapter.updateOne(state, {
        id: action.payload.id,
        changes: action.payload,
      });
    },
    
    removePersonFromRealm: (state, action: PayloadAction<string>) => {
      personsAdapter.removeOne(state, action.payload);
      // Clear current person if it was removed
      if (state.currentPersonId === action.payload) {
        state.currentPersonId = null;
      }
    },
    
    // Current person management
    setCurrentPerson: (state, action: PayloadAction<string | null>) => {
      state.currentPersonId = action.payload;
    },
    
    setRealmConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.realmConnected = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Create person optimistic
    builder
      .addCase(createPersonOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPersonOptimistic.fulfilled, (state) => {
        // Realm listener will handle the actual Redux update
        // This is just for loading state management
        state.loading = false;
      })
      .addCase(createPersonOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create person';
      });

    // Update person optimistic
    builder
      .addCase(updatePersonOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePersonOptimistic.fulfilled, (state) => {
        // Realm listener will handle the actual Redux update
        state.loading = false;
      })
      .addCase(updatePersonOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update person';
      });

    // Load current person optimistic
    builder
      .addCase(loadCurrentPersonOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadCurrentPersonOptimistic.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          // Add to entities if not already there
          personsAdapter.upsertOne(state, action.payload);
          // Set as current person
          state.currentPersonId = action.payload.id;
        }
      })
      .addCase(loadCurrentPersonOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to load current person';
      });

    // Update person preferences optimistic
    builder
      .addCase(updatePersonPreferencesOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePersonPreferencesOptimistic.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          personsAdapter.updateOne(state, {
            id: action.payload.id,
            changes: action.payload,
          });
        }
      })
      .addCase(updatePersonPreferencesOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update preferences';
      });

    // Update person authentication optimistic
    builder
      .addCase(updatePersonAuthenticationOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePersonAuthenticationOptimistic.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          personsAdapter.updateOne(state, {
            id: action.payload.id,
            changes: action.payload,
          });
        }
      })
      .addCase(updatePersonAuthenticationOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update authentication';
      });

    // Update person profile optimistic
    builder
      .addCase(updatePersonProfileOptimistic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePersonProfileOptimistic.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) {
          personsAdapter.updateOne(state, {
            id: action.payload.id,
            changes: action.payload,
          });
        }
      })
      .addCase(updatePersonProfileOptimistic.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update profile';
      });
  },
});

export const {
  clearError,
  syncFromRealm,
  addPersonFromRealm,
  updatePersonFromRealm,
  removePersonFromRealm,
  setCurrentPerson,
  setRealmConnectionStatus,
} = personDataSlice.actions;

export default personDataSlice.reducer;
