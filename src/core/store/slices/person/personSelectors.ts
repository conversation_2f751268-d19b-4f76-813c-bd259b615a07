import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../types';
import { Person } from '../../../services/PersonService';
import { PersonDisplayInfo, PersonMetrics, PersonContactsTyped, PersonPreferencesTyped } from '../../types/person';
import { personsAdapter } from './personDataSlice';

// Base selectors from entity adapter (using actual Realm Person model)
export const personDataSelectors = personsAdapter.getSelectors(
  (state: RootState) => state.personData
);

// UI selectors
export const selectPersonUI = (state: RootState) => state.personUI;
export const selectSelectedPersonId = (state: RootState) => state.personUI.selectedPersonId;
export const selectPersonFilters = (state: RootState) => state.personUI.filters;
export const selectPersonSorting = (state: RootState) => ({
  sortBy: state.personUI.sortBy,
  sortOrder: state.personUI.sortOrder,
});
export const selectPersonViewMode = (state: RootState) => state.personUI.viewMode;
export const selectPersonEditMode = (state: RootState) => state.personUI.editMode;
export const selectShowInactive = (state: RootState) => state.personUI.showInactive;

// Profile-specific UI selectors
export const selectProfileUI = (state: RootState) => state.personUI.profile;
export const selectProfileIsEditing = (state: RootState) => state.personUI.profile.isEditing;
export const selectProfileEditingField = (state: RootState) => state.personUI.profile.editingField;
export const selectProfileViewMode = (state: RootState) => state.personUI.profile.profileViewMode;
export const selectProfileActiveTab = (state: RootState) => state.personUI.profile.activeTab;
export const selectProfileHasUnsavedChanges = (state: RootState) => state.personUI.profile.hasUnsavedChanges;
export const selectProfileValidationErrors = (state: RootState) => state.personUI.profile.validationErrors;
export const selectProfileModals = (state: RootState) => state.personUI.profile.modals;
export const selectProfileOperationLoading = (state: RootState) => state.personUI.profile.operationLoading;

// Data state selectors
export const selectPersonDataState = (state: RootState) => ({
  loading: state.personData.loading,
  error: state.personData.error,
  lastSync: state.personData.lastSync,
  realmConnected: state.personData.realmConnected,
});

// Current person selectors
export const selectCurrentPersonId = (state: RootState) => state.personData.currentPersonId;

export const selectCurrentPerson = createSelector(
  [personDataSelectors.selectAll, selectCurrentPersonId],
  (persons, currentPersonId): Person | null =>
    currentPersonId ? persons.find(person => person.id === currentPersonId) || null : null
);

// Profile-specific computed selectors
export const selectCurrentPersonDisplayInfo = createSelector(
  [selectCurrentPerson],
  (person): {
    displayName: string;
    initials: string;
    email: string;
    phone: string;
    avatar?: string;
    isComplete: boolean;
  } | null => {
    if (!person) return null;

    const displayName = person.profile.fullName ||
      `${person.profile.firstName || ''} ${person.profile.lastName || ''}`.trim() ||
      person.profile.email ||
      'User';

    const initials = person.profile.firstName && person.profile.lastName
      ? `${person.profile.firstName[0]}${person.profile.lastName[0]}`.toUpperCase()
      : displayName.substring(0, 2).toUpperCase();

    const isComplete = !!(
      person.profile.firstName &&
      person.profile.lastName &&
      person.profile.email &&
      person.profile.phone
    );

    return {
      displayName,
      initials,
      email: person.profile.email || '',
      phone: person.profile.phone || '',
      avatar: person.profile.profileImage?.uri || person.profile.profileImage?.url || person.profile.avatar,
      isComplete,
    };
  }
);

// Profile completion status
export const selectCurrentPersonCompletion = createSelector(
  [selectCurrentPerson],
  (person): {
    completionPercentage: number;
    missingFields: string[];
    isComplete: boolean;
  } => {
    if (!person) {
      return {
        completionPercentage: 0,
        missingFields: ['All profile information'],
        isComplete: false,
      };
    }

    const requiredFields = [
      { key: 'firstName', label: 'First Name' },
      { key: 'lastName', label: 'Last Name' },
      { key: 'email', label: 'Email' },
      { key: 'phone', label: 'Phone' },
    ];

    const optionalFields = [
      { key: 'avatar', label: 'Profile Photo' },
      { key: 'dateOfBirth', label: 'Date of Birth' },
    ];

    const allFields = [...requiredFields, ...optionalFields];
    const completedFields = allFields.filter(field =>
      person.profile[field.key as keyof Person['profile']] !== undefined &&
      person.profile[field.key as keyof Person['profile']] !== null &&
      person.profile[field.key as keyof Person['profile']] !== ''
    );

    const missingRequiredFields = requiredFields.filter(field =>
      !person.profile[field.key as keyof Person['profile']]
    );

    const completionPercentage = Math.round((completedFields.length / allFields.length) * 100);
    const isComplete = missingRequiredFields.length === 0;

    return {
      completionPercentage,
      missingFields: missingRequiredFields.map(field => field.label),
      isComplete,
    };
  }
);

// Authentication status
export const selectCurrentPersonAuthenticationStatus = createSelector(
  [selectCurrentPerson],
  (person): {
    isAuthenticated: boolean;
    isVerified: boolean;
    verificationMethod: string;
    lastLogin?: Date;
    loginCount: number;
    needsVerification: boolean;
  } => {
    if (!person) {
      return {
        isAuthenticated: false,
        isVerified: false,
        verificationMethod: 'none',
        loginCount: 0,
        needsVerification: true,
      };
    }

    return {
      isAuthenticated: person.authentication.isAuthenticated,
      isVerified: person.authentication.isVerified,
      verificationMethod: person.authentication.verificationMethod,
      lastLogin: person.authentication.lastLogin,
      loginCount: person.authentication.loginCount,
      needsVerification: person.authentication.isAuthenticated && !person.authentication.isVerified,
    };
  }
);

// Preferences summary
export const selectCurrentPersonPreferencesSummary = createSelector(
  [selectCurrentPerson],
  (person): {
    theme: string;
    language: string;
    timezone: string;
    currency: string;
    notificationsEnabled: boolean;
    privacyLevel: 'high' | 'medium' | 'low';
    biometricsEnabled: boolean;
  } | null => {
    if (!person) return null;

    const { preferences } = person;
    const notificationsEnabled = preferences.notifications.email ||
      preferences.notifications.push ||
      preferences.notifications.sms;

    // Calculate privacy level based on privacy settings
    const privacySettings = preferences.privacy;
    const privacyCount = Object.values(privacySettings).filter(Boolean).length;
    const privacyLevel = privacyCount === 0 ? 'high' :
      privacyCount <= 1 ? 'medium' : 'low';

    return {
      theme: preferences.theme,
      language: preferences.language,
      timezone: preferences.timezone,
      currency: preferences.currency,
      notificationsEnabled,
      privacyLevel,
      biometricsEnabled: preferences.biometrics,
    };
  }
);

// Profile validation
export const selectCurrentPersonValidation = createSelector(
  [selectCurrentPerson, selectProfileValidationErrors],
  (person, validationErrors): {
    isValid: boolean;
    errors: Record<string, string>;
    hasErrors: boolean;
    errorCount: number;
  } => {
    const errors = { ...validationErrors };

    // Add runtime validation errors
    if (person) {
      if (person.profile.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(person.profile.email)) {
        errors.email = 'Invalid email format';
      }

      if (person.profile.phone && !/^\+?[\d\s\-\(\)]+$/.test(person.profile.phone)) {
        errors.phone = 'Invalid phone format';
      }
    }

    const hasErrors = Object.keys(errors).length > 0;

    return {
      isValid: !hasErrors,
      errors,
      hasErrors,
      errorCount: Object.keys(errors).length,
    };
  }
);

// Profile UI state summary
export const selectProfileUIStateSummary = createSelector(
  [selectProfileUI],
  (ui): {
    canSave: boolean;
    canCancel: boolean;
    hasActiveModals: boolean;
    isPerformingOperation: boolean;
    activeOperations: string[];
  } => {
    const hasActiveModals = Object.values(ui.modals).some(Boolean);
    const activeOperations = Object.entries(ui.operationLoading)
      .filter(([_, loading]) => loading)
      .map(([operation]) => operation);
    const isPerformingOperation = activeOperations.length > 0;

    return {
      canSave: ui.hasUnsavedChanges && !isPerformingOperation,
      canCancel: ui.isEditing || ui.hasUnsavedChanges,
      hasActiveModals,
      isPerformingOperation,
      activeOperations,
    };
  }
);

// Combined current person state (replaces Profile state)
export const selectCurrentPersonState = createSelector(
  [
    selectCurrentPerson,
    selectPersonDataState,
    selectProfileIsEditing,
    selectProfileHasUnsavedChanges,
  ],
  (person, dataState, isEditing, hasUnsavedChanges) => ({
    person,
    loading: dataState.loading,
    error: dataState.error,
    initialized: !!person,
    isEditing,
    hasUnsavedChanges,
    hasPerson: !!person,
  })
);

// Computed selectors using actual Realm fields
export const selectPersonMetrics = createSelector(
  [personDataSelectors.selectAll],
  (persons): PersonMetrics => {
    const now = new Date();
    
    return {
      totalPersons: persons.length,
      activePersons: persons.filter(p => !p.deletedAt).length,
      inactivePersons: persons.filter(p => !!p.deletedAt).length,
      recentlyModified: persons.filter(p => {
        const modifiedAt = p.modifiedAt || p.createdAt;
        if (!modifiedAt) return false;
        const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        return modifiedAt > dayAgo;
      }).length,
      personsByGender: persons.reduce((acc, person) => {
        const gender = person.gender || 'unknown';
        acc[gender] = (acc[gender] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      personsWithContacts: persons.filter(p => 
        p.contacts && (
          (p.contacts.emails && p.contacts.emails.length > 0) ||
          (p.contacts.phones && p.contacts.phones.length > 0) ||
          (p.contacts.addresses && p.contacts.addresses.length > 0)
        )
      ).length,
    };
  }
);

// UI-friendly person display info
export const selectPersonDisplayInfo = createSelector(
  [personDataSelectors.selectAll],
  (persons): PersonDisplayInfo[] => persons.map(person => ({
    id: person.id,
    fullName: person.fullName || `${person.givenName || ''} ${person.familyName || ''}`.trim() || 'Unknown',
    givenName: person.givenName,
    familyName: person.familyName,
    alias: person.alias,
    gender: person.gender,
    isActive: !person.deletedAt,
    hasContacts: !!(person.contacts && (
      (person.contacts.emails && person.contacts.emails.length > 0) ||
      (person.contacts.phones && person.contacts.phones.length > 0) ||
      (person.contacts.addresses && person.contacts.addresses.length > 0)
    )),
    hasPreferences: !!(person.preferences),
    createdAt: person.createdAt,
    modifiedAt: person.modifiedAt,
  }))
);

// Filtered persons using actual Realm fields
export const selectFilteredPersons = createSelector(
  [personDataSelectors.selectAll, selectPersonFilters, selectPersonSorting, selectShowInactive],
  (persons, filters, sorting, showInactive) => {
    let filtered = persons;

    // Apply filters using actual Realm fields
    if (!showInactive) {
      filtered = filtered.filter(person => !person.deletedAt);
    }
    
    if (filters.gender) {
      filtered = filtered.filter(person => person.gender === filters.gender);
    }
    
    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(person => 
        (person.fullName || '').toLowerCase().includes(term) ||
        (person.givenName || '').toLowerCase().includes(term) ||
        (person.familyName || '').toLowerCase().includes(term) ||
        (person.alias || '').toLowerCase().includes(term)
      );
    }
    
    if (filters.hasContacts !== undefined) {
      filtered = filtered.filter(person => {
        const hasContacts = !!(person.contacts && (
          (person.contacts.emails && person.contacts.emails.length > 0) ||
          (person.contacts.phones && person.contacts.phones.length > 0) ||
          (person.contacts.addresses && person.contacts.addresses.length > 0)
        ));
        return hasContacts === filters.hasContacts;
      });
    }
    
    if (filters.hasPreferences !== undefined) {
      filtered = filtered.filter(person => !!person.preferences === filters.hasPreferences);
    }

    // Apply sorting using actual Realm fields
    return filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sorting.sortBy) {
        case 'name':
          aValue = a.fullName || `${a.givenName || ''} ${a.familyName || ''}`.trim() || '';
          bValue = b.fullName || `${b.givenName || ''} ${b.familyName || ''}`.trim() || '';
          break;
        case 'givenName':
          aValue = a.givenName || '';
          bValue = b.givenName || '';
          break;
        case 'familyName':
          aValue = a.familyName || '';
          bValue = b.familyName || '';
          break;
        case 'created':
          aValue = a.createdAt || new Date(0);
          bValue = b.createdAt || new Date(0);
          break;
        case 'modified':
          aValue = a.modifiedAt || a.createdAt || new Date(0);
          bValue = b.modifiedAt || b.createdAt || new Date(0);
          break;
        default:
          return 0;
      }
      
      const order = sorting.sortOrder === 'asc' ? 1 : -1;
      return aValue > bValue ? order : -order;
    });
  }
);

// Selected person with proper typing
export const selectSelectedPerson = createSelector(
  [personDataSelectors.selectAll, selectSelectedPersonId],
  (persons, selectedId): Person | null => 
    selectedId ? persons.find(person => person.id === selectedId) || null : null
);

// Person by ID selector factory
export const selectPersonById = (personId: string) => createSelector(
  [personDataSelectors.selectById],
  (person): Person | null => person || null
);

// Selectors for Realm.Mixed fields
export const selectPersonContacts = (personId: string) => createSelector(
  [(state: RootState) => personDataSelectors.selectById(state, personId)],
  (person): PersonContactsTyped | null => person ? (person.contacts as PersonContactsTyped) || null : null
);

export const selectPersonPreferences = (personId: string) => createSelector(
  [(state: RootState) => personDataSelectors.selectById(state, personId)],
  (person): PersonPreferencesTyped | null => person ? (person.preferences as PersonPreferencesTyped) || null : null
);

// Active persons
export const selectActivePersons = createSelector(
  [personDataSelectors.selectAll],
  (persons) => persons.filter(person => !person.deletedAt)
);

// Recently modified persons
export const selectRecentlyModifiedPersons = createSelector(
  [personDataSelectors.selectAll],
  (persons) => {
    const now = new Date();
    const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    return persons.filter(person => {
      const modifiedAt = person.modifiedAt || person.createdAt;
      return modifiedAt && modifiedAt > dayAgo;
    });
  }
);

// Persons by gender
export const selectPersonsByGender = (gender: string) => createSelector(
  [personDataSelectors.selectAll],
  (persons) => persons.filter(person => person.gender === gender)
);

// Persons with contacts
export const selectPersonsWithContacts = createSelector(
  [personDataSelectors.selectAll],
  (persons) => persons.filter(person => 
    person.contacts && (
      (person.contacts.emails && person.contacts.emails.length > 0) ||
      (person.contacts.phones && person.contacts.phones.length > 0) ||
      (person.contacts.addresses && person.contacts.addresses.length > 0)
    )
  )
);
