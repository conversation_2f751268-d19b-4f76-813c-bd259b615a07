import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { PersonUIFilters } from '../../types/person';

export interface PersonUIState {
  // List management
  selectedPersonId: string | null;
  filters: PersonUIFilters;
  sortBy: 'name' | 'created' | 'modified' | 'familyName' | 'givenName';
  sortOrder: 'asc' | 'desc';
  viewMode: 'list' | 'grid' | 'detail';
  editMode: boolean;
  showInactive: boolean;

  // Profile-specific UI state (for current person)
  profile: {
    // Edit modes
    isEditing: boolean;
    editingField: string | null;

    // View modes
    profileViewMode: 'view' | 'edit' | 'settings';
    activeTab: 'profile' | 'preferences' | 'security' | 'privacy';

    // UI state
    showImagePicker: boolean;
    showDeleteConfirmation: boolean;
    showLogoutConfirmation: boolean;

    // Form state
    hasUnsavedChanges: boolean;
    validationErrors: Record<string, string>;

    // Modal states
    modals: {
      imageOptions: boolean;
      preferences: boolean;
      security: boolean;
      privacy: boolean;
      deleteAccount: boolean;
    };

    // Loading states for specific operations
    operationLoading: {
      savingProfile: boolean;
      uploadingImage: boolean;
      deletingImage: boolean;
      changingPassword: boolean;
      deletingAccount: boolean;
    };
  };
}

const initialState: PersonUIState = {
  // List management
  selectedPersonId: null,
  filters: {},
  sortBy: 'modified',
  sortOrder: 'desc',
  viewMode: 'list',
  editMode: false,
  showInactive: false,

  // Profile-specific UI state
  profile: {
    // Edit modes
    isEditing: false,
    editingField: null,

    // View modes
    profileViewMode: 'view',
    activeTab: 'profile',

    // UI state
    showImagePicker: false,
    showDeleteConfirmation: false,
    showLogoutConfirmation: false,

    // Form state
    hasUnsavedChanges: false,
    validationErrors: {},

    // Modal states
    modals: {
      imageOptions: false,
      preferences: false,
      security: false,
      privacy: false,
      deleteAccount: false,
    },

    // Loading states
    operationLoading: {
      savingProfile: false,
      uploadingImage: false,
      deletingImage: false,
      changingPassword: false,
      deletingAccount: false,
    },
  },
};

export const personUISlice = createSlice({
  name: 'personUI',
  initialState,
  reducers: {
    selectPerson: (state, action: PayloadAction<string | null>) => {
      state.selectedPersonId = action.payload;
      // Exit edit mode when selecting a different person
      if (action.payload !== state.selectedPersonId) {
        state.editMode = false;
      }
    },
    
    setFilters: (state, action: PayloadAction<Partial<PersonUIFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    clearFilters: (state) => {
      state.filters = {};
    },
    
    setSorting: (state, action: PayloadAction<{ 
      sortBy: PersonUIState['sortBy']; 
      sortOrder: PersonUIState['sortOrder']; 
    }>) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },
    
    toggleSortOrder: (state) => {
      state.sortOrder = state.sortOrder === 'asc' ? 'desc' : 'asc';
    },
    
    setViewMode: (state, action: PayloadAction<PersonUIState['viewMode']>) => {
      state.viewMode = action.payload;
    },
    
    setEditMode: (state, action: PayloadAction<boolean>) => {
      state.editMode = action.payload;
    },
    
    toggleEditMode: (state) => {
      state.editMode = !state.editMode;
    },
    
    setShowInactive: (state, action: PayloadAction<boolean>) => {
      state.showInactive = action.payload;
    },
    
    toggleShowInactive: (state) => {
      state.showInactive = !state.showInactive;
    },
    
    resetUI: (state) => {
      state.selectedPersonId = null;
      state.filters = {};
      state.sortBy = 'modified';
      state.sortOrder = 'desc';
      state.viewMode = 'list';
      state.editMode = false;
      state.showInactive = false;
    },

    // Profile-specific actions
    setProfileEditing: (state, action: PayloadAction<boolean>) => {
      state.profile.isEditing = action.payload;
      if (!action.payload) {
        state.profile.editingField = null;
        state.profile.hasUnsavedChanges = false;
        state.profile.validationErrors = {};
      }
    },

    toggleProfileEditing: (state) => {
      state.profile.isEditing = !state.profile.isEditing;
      if (!state.profile.isEditing) {
        state.profile.editingField = null;
        state.profile.hasUnsavedChanges = false;
        state.profile.validationErrors = {};
      }
    },

    setProfileEditingField: (state, action: PayloadAction<string | null>) => {
      state.profile.editingField = action.payload;
      if (action.payload) {
        state.profile.isEditing = true;
      }
    },

    setProfileViewMode: (state, action: PayloadAction<PersonUIState['profile']['profileViewMode']>) => {
      state.profile.profileViewMode = action.payload;
      if (action.payload !== 'edit') {
        state.profile.isEditing = false;
        state.profile.editingField = null;
        state.profile.hasUnsavedChanges = false;
      }
    },

    setProfileActiveTab: (state, action: PayloadAction<PersonUIState['profile']['activeTab']>) => {
      state.profile.activeTab = action.payload;
    },

    setProfileShowImagePicker: (state, action: PayloadAction<boolean>) => {
      state.profile.showImagePicker = action.payload;
    },

    setProfileShowDeleteConfirmation: (state, action: PayloadAction<boolean>) => {
      state.profile.showDeleteConfirmation = action.payload;
    },

    setProfileShowLogoutConfirmation: (state, action: PayloadAction<boolean>) => {
      state.profile.showLogoutConfirmation = action.payload;
    },

    setProfileHasUnsavedChanges: (state, action: PayloadAction<boolean>) => {
      state.profile.hasUnsavedChanges = action.payload;
    },

    setProfileValidationError: (state, action: PayloadAction<{ field: string; error: string }>) => {
      state.profile.validationErrors[action.payload.field] = action.payload.error;
    },

    clearProfileValidationError: (state, action: PayloadAction<string>) => {
      delete state.profile.validationErrors[action.payload];
    },

    clearAllProfileValidationErrors: (state) => {
      state.profile.validationErrors = {};
    },

    openProfileModal: (state, action: PayloadAction<keyof PersonUIState['profile']['modals']>) => {
      state.profile.modals[action.payload] = true;
    },

    closeProfileModal: (state, action: PayloadAction<keyof PersonUIState['profile']['modals']>) => {
      state.profile.modals[action.payload] = false;
    },

    closeAllProfileModals: (state) => {
      Object.keys(state.profile.modals).forEach(key => {
        state.profile.modals[key as keyof PersonUIState['profile']['modals']] = false;
      });
    },

    setProfileOperationLoading: (state, action: PayloadAction<{
      operation: keyof PersonUIState['profile']['operationLoading'];
      loading: boolean;
    }>) => {
      state.profile.operationLoading[action.payload.operation] = action.payload.loading;
    },

    resetProfileUI: (state) => {
      state.profile = { ...initialState.profile };
    },

    resetProfileEditState: (state) => {
      state.profile.isEditing = false;
      state.profile.editingField = null;
      state.profile.hasUnsavedChanges = false;
      state.profile.validationErrors = {};
    },
  },
});

export const {
  // List management actions
  selectPerson,
  setFilters,
  clearFilters,
  setSorting,
  toggleSortOrder,
  setViewMode,
  setEditMode,
  toggleEditMode,
  setShowInactive,
  toggleShowInactive,
  resetUI,

  // Profile-specific actions
  setProfileEditing,
  toggleProfileEditing,
  setProfileEditingField,
  setProfileViewMode,
  setProfileActiveTab,
  setProfileShowImagePicker,
  setProfileShowDeleteConfirmation,
  setProfileShowLogoutConfirmation,
  setProfileHasUnsavedChanges,
  setProfileValidationError,
  clearProfileValidationError,
  clearAllProfileValidationErrors,
  openProfileModal,
  closeProfileModal,
  closeAllProfileModals,
  setProfileOperationLoading,
  resetProfileUI,
  resetProfileEditState,
} = personUISlice.actions;

export default personUISlice.reducer;
