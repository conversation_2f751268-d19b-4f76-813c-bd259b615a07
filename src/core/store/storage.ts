/**
 * Perkd v7.0 Enhanced MMKV Storage Configuration
 *
 * High-performance, encrypted storage for Redux state persistence
 * Optimized for Events-Redux architecture with separate storage domains
 */

import { MMKV } from 'react-native-mmkv';

// =============================================================================
// MMKV INSTANCES
// =============================================================================

/**
 * Primary Redux state storage
 * Used for UI state, preferences, and non-business data
 */
export const reduxStorage = new MMKV({
  id: 'perkd-v7-redux-storage',
  encryptionKey: 'perkd-v7-redux-encryption-key-2025',
});

/**
 * Event system storage (for bridge state and metrics)
 * Separate instance for event-related persistence
 */
export const eventStorage = new MMKV({
  id: 'perkd-v7-event-storage',
  encryptionKey: 'perkd-v7-event-encryption-key-2025',
});

/**
 * Cache storage for temporary data
 * Non-encrypted for performance-critical caching
 */
export const cacheStorage = new MMKV({
  id: 'perkd-v7-cache-storage',
  // No encryption for cache data
});

// =============================================================================
// REDUX PERSIST ADAPTER
// =============================================================================

/**
 * Enhanced Redux persist storage adapter with error handling
 */
export const mmkvStorage = {
  setItem: async (key: string, value: string): Promise<void> => {
    try {
      reduxStorage.set(key, value);

      // Log storage metrics in development
      if (__DEV__) {
        const size = new Blob([value]).size;
        if (size > 100 * 1024) { // Warn if > 100KB
          console.warn(`🔶 Large Redux state stored: ${key} (${Math.round(size / 1024)}KB)`);
        }
      }
    } catch (error) {
      console.error('❌ Failed to store Redux state:', key, error);
      throw error;
    }
  },

  getItem: async (key: string): Promise<string | null> => {
    try {
      const value = reduxStorage.getString(key);
      return value ?? null;
    } catch (error) {
      console.error('❌ Failed to retrieve Redux state:', key, error);
      return null;
    }
  },

  removeItem: async (key: string): Promise<void> => {
    try {
      reduxStorage.delete(key);
    } catch (error) {
      console.error('❌ Failed to remove Redux state:', key, error);
      throw error;
    }
  },
};

// =============================================================================
// EVENT BRIDGE STORAGE UTILITIES
// =============================================================================

/**
 * Event bridge state persistence utilities
 * Used for storing bridge configuration and metrics
 */
export const eventBridgeStorage = {
  // Store bridge configuration
  setBridgeConfig: (config: any) => {
    eventStorage.set('bridge-config', JSON.stringify(config));
  },

  getBridgeConfig: (): any => {
    const config = eventStorage.getString('bridge-config');
    return config ? JSON.parse(config) : null;
  },

  // Store bridge metrics
  setBridgeMetrics: (metrics: any) => {
    eventStorage.set('bridge-metrics', JSON.stringify(metrics));
  },

  getBridgeMetrics: (): any => {
    const metrics = eventStorage.getString('bridge-metrics');
    return metrics ? JSON.parse(metrics) : null;
  },

  // Clear bridge data
  clearBridgeData: () => {
    eventStorage.clearAll();
  },
};

// =============================================================================
// CACHE UTILITIES
// =============================================================================

/**
 * High-performance cache utilities for temporary data
 */
export const cacheUtilities = {
  // Set cache with TTL
  setCache: (key: string, value: any, ttlSeconds: number = 3600) => {
    const cacheData = {
      value,
      timestamp: Date.now(),
      ttl: ttlSeconds * 1000,
    };
    cacheStorage.set(key, JSON.stringify(cacheData));
  },

  // Get cache with TTL check
  getCache: (key: string): any => {
    const cached = cacheStorage.getString(key);
    if (!cached) return null;

    try {
      const cacheData = JSON.parse(cached);
      const now = Date.now();

      // Check if cache is expired
      if (now - cacheData.timestamp > cacheData.ttl) {
        cacheStorage.delete(key);
        return null;
      }

      return cacheData.value;
    } catch (error) {
      cacheStorage.delete(key);
      return null;
    }
  },

  // Clear expired cache entries
  clearExpiredCache: () => {
    const keys = cacheStorage.getAllKeys();
    const now = Date.now();

    keys.forEach(key => {
      const cached = cacheStorage.getString(key);
      if (cached) {
        try {
          const cacheData = JSON.parse(cached);
          if (now - cacheData.timestamp > cacheData.ttl) {
            cacheStorage.delete(key);
          }
        } catch (error) {
          cacheStorage.delete(key);
        }
      }
    });
  },
};

// =============================================================================
// STORAGE HEALTH UTILITIES
// =============================================================================

/**
 * Storage health monitoring and cleanup utilities
 */
export const storageHealth = {
  // Get storage statistics
  getStorageStats: () => {
    return {
      redux: {
        keys: reduxStorage.getAllKeys().length,
        size: reduxStorage.size,
      },
      events: {
        keys: eventStorage.getAllKeys().length,
        size: eventStorage.size,
      },
      cache: {
        keys: cacheStorage.getAllKeys().length,
        size: cacheStorage.size,
      },
      total: {
        keys: reduxStorage.getAllKeys().length + eventStorage.getAllKeys().length + cacheStorage.getAllKeys().length,
        size: reduxStorage.size + eventStorage.size + cacheStorage.size,
      },
    };
  },

  // Cleanup old data
  cleanup: () => {
    // Clear expired cache
    cacheUtilities.clearExpiredCache();

    // Log cleanup results
    if (__DEV__) {
      const stats = storageHealth.getStorageStats();
      console.log('🧹 Storage cleanup completed:', stats);
    }
  },

  // Reset all storage (use with caution)
  resetAll: () => {
    reduxStorage.clearAll();
    eventStorage.clearAll();
    cacheStorage.clearAll();
    console.log('🔄 All storage instances reset');
  },
};
