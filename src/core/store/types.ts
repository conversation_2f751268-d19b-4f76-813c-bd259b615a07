/**
 * Store Type Definitions
 * 
 * Comprehensive TypeScript interfaces for the store layer
 * Replaces 'any' types with proper type safety
 */

import type { Action } from '@reduxjs/toolkit';
import type { store } from './index';

// =============================================================================
// STORE TYPES
// =============================================================================

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export type AppStore = typeof store;

// =============================================================================
// ACTION TYPES
// =============================================================================

export interface ReduxAction extends Action {
  type: string;
  payload?: unknown;
  meta?: {
    source?: 'user' | 'realm' | 'event-bridge' | 'service';
    timestamp?: string;
    requestId?: string;
  };
}

export interface AsyncThunkAction extends ReduxAction {
  payload: unknown;
  meta: {
    requestId: string;
    requestStatus: 'pending' | 'fulfilled' | 'rejected';
    arg: unknown;
  };
}

// =============================================================================
// MIDDLEWARE TYPES
// =============================================================================

export interface DispatchFunction {
  (action: ReduxAction): ReduxAction;
  <T extends ReduxAction>(action: T): T;
}

export interface RealmChangeData {
  insertions: number[];
  newModifications: number[];
  deletions: number[];
}

export interface RealmObjectData {
  id?: string; // Optional because embedded objects don't have IDs
  [key: string]: unknown;
}

// =============================================================================
// CARD TYPES
// =============================================================================

export interface CardData {
  id: string;
  masterId: string;
  personId: string;
  credentials?: {
    id: string;
    number?: string;
    barcode?: string;
    barcodeFormat?: string;
    pin?: string;
  };
  display?: {
    id: string;
    name: string;
    customImage?: string;
    backgroundColor?: string;
    textColor?: string;
    logoUrl?: string;
    theme: string;
  };
  lifecycle?: {
    id: string;
    issuedAt: Date;
    activatedAt?: Date;
    expiresAt?: Date;
    lastUsedAt?: Date;
    state: string;
  };
  features?: {
    id: string;
    hasStoredValue: boolean;
    hasLoyaltyProgram: boolean;
    hasOffers: boolean;
    isShareable: boolean;
    supportsNFC: boolean;
    supportsBarcodeScanning: boolean;
    hasCustomization: boolean;
  };
  permissions?: {
    id: string;
    canShare: boolean;
    canModify: boolean;
    canDelete: boolean;
    shareLevel: string;
    accessLevel: string;
  };
  audit?: {
    id: string;
    createdAt: Date;
    modifiedAt: Date;
    deletedAt?: Date;
    version: number;
    createdBy: string;
    modifiedBy: string;
  };
}

export interface CardUpdateData {
  id: string;
  modifiedBy?: string;
  display?: Partial<CardData['display']>;
  lifecycle?: Partial<CardData['lifecycle']>;
  features?: Partial<CardData['features']>;
  permissions?: Partial<CardData['permissions']>;
}

export interface CardDeleteData {
  id: string;
  deletedBy?: string;
}

// =============================================================================
// OFFER TYPES
// =============================================================================

export interface OfferData {
  id: string;
  cardId: string;
  masterId: string;
  personId: string;
  content: {
    title: string;
    description: string;
    category: string;
    priority: number;
  };
  temporal: {
    startsAt: Date;
    endsAt: Date;
    timezone: string;
    isActive: boolean;
    activationCount: number;
  };
  businessLogic: {
    type: string;
    value: number;
    currency: string;
    usageLimit: number;
    rules: any;
  };
  targeting: {
    isPersonalized: boolean;
    segments: string[];
    geofence: any;
  };
  audit: {
    createdAt: Date;
    modifiedAt: Date;
    deletedAt?: Date;
    version: number;
    createdBy: string;
    modifiedBy: string;
  };
}

export interface OfferUpdateData {
  id: string;
  modifiedBy?: string;
  content?: Partial<OfferData['content']>;
  temporal?: Partial<OfferData['temporal']>;
  businessLogic?: Partial<OfferData['businessLogic']>;
  targeting?: Partial<OfferData['targeting']>;
}

export interface OfferDeleteData {
  id: string;
  deletedBy?: string;
}

// =============================================================================
// PERSON TYPES
// =============================================================================

export interface PersonData {
  id: string;
  profile: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    dateOfBirth?: Date;
    avatar?: string;
  };
  preferences: {
    language: string;
    timezone: string;
    currency: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
    privacy: {
      shareData: boolean;
      analytics: boolean;
      marketing: boolean;
    };
  };
  authentication: {
    isVerified: boolean;
    verificationMethod: 'email' | 'phone' | 'both' | 'none';
    lastLogin?: Date;
    loginCount: number;
  };
}

export interface PersonUpdateData {
  id: string;
  profile?: Partial<PersonData['profile']>;
  preferences?: Partial<PersonData['preferences']>;
  authentication?: Partial<PersonData['authentication']>;
}

// =============================================================================
// EVENT TYPES
// =============================================================================

export interface BusinessEvent {
  type: string;
  data: Record<string, unknown>;
  timestamp: string;
  source: 'user' | 'system' | 'service' | 'external';
  metadata?: {
    requestId?: string;
    userId?: string;
    sessionId?: string;
    version?: string;
  };
}

export interface EventBridgeConfig {
  enabled: boolean;
  debounceMs: number;
  batchSize: number;
  errorRetryCount: number;
  performanceMonitoring: boolean;
}

// =============================================================================
// HEALTH CHECK TYPES
// =============================================================================

export interface StoreHealthCheck {
  isHealthy: boolean;
  timestamp: Date;
  issues: string[];
  recommendations: string[];
  statistics: {
    stateSize: number;
    slices: Record<string, unknown>;
    performance: {
      middlewareCount: number;
      devToolsEnabled: boolean;
      persistenceEnabled: boolean;
    };
  };
}

export interface BridgeHealthCheck {
  isInitialized: boolean;
  isHealthy: boolean;
  timestamp: Date;
  errorCount?: number;
  lastActivity?: number;
  metrics?: {
    eventsProcessed: number;
    actionsDispatched: number;
    errorsCount: number;
    averageProcessingTime: number;
    lastActivity: Date;
    queueSize: number;
  };
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// =============================================================================
// LEGACY INTEGRATION TYPES
// =============================================================================

export interface LegacyCardData {
  id: string;
  masterId: string;
  personId: string;
  [key: string]: unknown;
}

export interface LegacyOfferData {
  id: string;
  cardId?: string;
  masterId?: string;
  [key: string]: unknown;
}

export interface LegacyPersonData {
  id: string;
  email: string;
  [key: string]: unknown;
}

// =============================================================================
// ERROR TYPES
// =============================================================================

export interface StoreError {
  message: string;
  code?: string;
  operation: string;
  timestamp: string;
  details?: Record<string, unknown>;
}

export interface AsyncThunkError {
  message: string;
  code?: string;
  stack?: string;
}
