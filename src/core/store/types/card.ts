// Card types based on actual Realm models
import { Card as RealmCard, CardOptions, CardFormData } from '../../../data/models/Card';
import { StoredValue } from '../../../data/models/embedded/StoredValue';

// Use actual Realm interface as base
export type Card = RealmCard;

// UI-friendly derived types from Realm fields
export interface CardDisplayInfo {
  id: string;
  name: string;                    // Mapped from displayName
  number?: string;                 // Direct from Realm
  barcode?: string;                // Direct from Realm
  barcodeType?: string;            // Direct from Realm
  state: string;                   // Direct from Realm
  isVisible: boolean;              // Mapped from visible
  hasStoredValue: boolean;         // Derived from storedValue existence
  masterId: string;                // Direct from Realm
  personId: string;                // Direct from Realm
}

export interface CardCredentials {
  number?: string;                 // From card.number
  barcode?: string;                // From card.barcode
  barcodeFormat?: string;          // From card.barcodeType
}

export interface CardTimestamps {
  createdAt: Date;                 // Direct from Realm
  modifiedAt?: Date;               // Direct from Realm
  deletedAt?: Date;                // Direct from Realm
  startTime?: Date;                // Direct from Realm
  endTime?: Date;                  // Direct from Realm
  activeTime?: Date;               // Direct from Realm
}

// Handle Realm.Mixed types properly
export interface CardOptionsTyped extends CardOptions {
  overrideMaster?: any;
  notification?: any;
  noDelete?: boolean;
  hideZeroBalance?: boolean;
  hideBalance?: boolean;
  [key: string]: any;
}

export interface CardFormDataTyped extends CardFormData {
  [key: string]: any;
}

// Selectors for complex Realm relationships
export interface CardRelationships {
  storedValue?: StoredValue;       // Embedded object
  sharer?: any;                    // Relationship object
  when?: any;                      // Relationship object
  cardImage?: any;                 // Relationship object
  image?: any;                     // CustomImage relationship
  sharings?: any[];                // Sharing[] relationship
}

// Card metrics computed from data
export interface CardMetrics {
  totalCards: number;
  activeCards: number;
  expiredCards: number;
  visibleCards: number;
  cardsWithStoredValue: number;
  cardsByState: Record<string, number>;
}

// Card UI state
export interface CardUIFilters {
  status?: string;
  masterId?: string;
  searchTerm?: string;
  hasStoredValue?: boolean;
  isVisible?: boolean;
}

// Card operations for optimistic updates
export interface CreateCardData {
  masterId: string;
  personId: string;
  displayName?: string;
  options?: CardOptionsTyped;
  formData?: CardFormDataTyped;
}

export interface UpdateCardData {
  id: string;
  changes: Partial<Card>;
}

export interface CardQueryOptions {
  personId?: string;
  masterId?: string;
  state?: string;
  visible?: boolean;
  limit?: number;
  offset?: number;
}
