// Common types used across store slices

export interface BaseState {
  loading: boolean;
  error: string | null;
}

export interface SyncState {
  lastSync: string | null;
  realmConnected: boolean;
}

export interface UIState {
  selectedId: string | null;
  filters: Record<string, any>;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// Sync operation types for Realm-Redux bridge
export interface SyncOperation {
  id: string;
  type: 'CREATE_CARD' | 'UPDATE_CARD' | 'ACCEPT_CARD' | 'DECLINE_CARD' | 
        'CREATE_OFFER' | 'REDEEM_OFFER' | 'UPDATE_OFFER';
  data: any;
  timestamp: string;
  retryCount: number;
  status: 'pending' | 'completed' | 'failed';
}

// Generic query options
export interface QueryOptions {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
}

// Response wrapper for service calls
export interface ServiceResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ServiceResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
