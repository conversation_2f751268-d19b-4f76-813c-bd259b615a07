// Offer types based on actual Realm models
import { Offer as RealmOffer, OfferOptions, OfferPlace, OfferFields } from '../../../data/models/Offer';

// Use actual Realm interface as base
export type Offer = RealmOffer;

// UI-friendly derived types from Realm fields
export interface OfferDisplayInfo {
  id: string;
  masterId: string;
  name?: string;                   // Direct from Realm
  shortName?: string;              // Direct from Realm
  title?: string;                  // Direct from Realm
  description?: string;            // Direct from Realm
  terms?: string;                  // Direct from Realm
  kind?: string;                   // Direct from Realm (discount, voucher, ticket)
  state?: string;                  // Direct from Realm
  isVisible: boolean;              // Mapped from visible
  hasBarcode: boolean;             // Derived from barcode existence
  isActive: boolean;               // Derived from timing
}

export interface OfferTiming {
  startTime?: Date;                // Direct from Realm
  endTime?: Date;                  // Direct from Realm
  activeTime?: Date;               // Direct from Realm
  purgeTime?: Date;                // Direct from Realm
  createdAt: Date;                 // Direct from Realm
  modifiedAt?: Date;               // Direct from Realm
  deletedAt?: Date;                // Direct from Realm
}

export interface OfferCredentials {
  barcode?: string;                // Direct from Realm
  barcodeType?: string;            // Direct from Realm
  orderId?: string;                // Direct from Realm
}

// Handle Realm.Mixed types properly
export interface OfferOptionsTyped extends OfferOptions {
  appOnly?: boolean;
  redeemOnline?: boolean;
  payment?: any;
  hideButton?: boolean;
  buttonLabel?: string;
  buttonLink?: string;
  showVenueName?: boolean;
  [key: string]: any;
}

export interface OfferPlaceTyped extends OfferPlace {
  [key: string]: any;
}

export interface OfferFieldsTyped extends OfferFields {
  [key: string]: any;
}

// Selectors for complex Realm relationships
export interface OfferRelationships {
  code?: any;                      // OfferCode relationship
  images?: any[];                  // OfferImage[] relationship
  discount?: any;                  // OfferDiscount relationship
  redemption?: any;                // Redemption relationship
  sharer?: any;                    // OfferSharer relationship
  globalize?: any;                 // OfferGlobalize relationship
  when?: any;                      // OfferWhen relationship
  applet?: any;                    // AppletWebview relationship
}

// Offer metrics computed from data
export interface OfferMetrics {
  totalOffers: number;
  activeOffers: number;
  expiredOffers: number;
  redeemedOffers: number;
  visibleOffers: number;
  offersByKind: Record<string, number>;
  offersByState: Record<string, number>;
}

// Offer UI state
export interface OfferUIFilters {
  kind?: string;
  state?: string;
  masterId?: string;
  searchTerm?: string;
  isActive?: boolean;
  isVisible?: boolean;
  hasBarcode?: boolean;
}

// Offer operations for optimistic updates
export interface CreateOfferData {
  masterId: string;
  name?: string;
  title?: string;
  description?: string;
  kind?: string;
  options?: OfferOptionsTyped;
  place?: OfferPlaceTyped;
  fields?: OfferFieldsTyped;
}

export interface UpdateOfferData {
  id: string;
  changes: Partial<Offer>;
}

export interface RedeemOfferData {
  offerId: string;
  cardId?: string;
  personId?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
}

export interface OfferQueryOptions {
  masterId?: string;
  kind?: string;
  state?: string;
  visible?: boolean;
  active?: boolean;
  limit?: number;
  offset?: number;
}
