// Person types based on actual Realm models
import { Person as <PERSON><PERSON><PERSON> } from '../../../data/models/Person';

// Use actual Realm interface as base
export type Person = RealmPerson;

// UI-friendly derived types from Realm fields
export interface PersonDisplayInfo {
  id: string;
  name?: string;                   // Direct from Realm
  email?: string;                  // Direct from Realm
  phone?: string;                  // Direct from Realm
  isActive: boolean;               // Derived from state or other fields
}

// Person metrics computed from data
export interface PersonMetrics {
  totalPersons: number;
  activePersons: number;
}

// Person UI state
export interface PersonUIFilters {
  searchTerm?: string;
  isActive?: boolean;
}

// Person operations for optimistic updates
export interface CreatePersonData {
  name?: string;
  email?: string;
  phone?: string;
}

export interface UpdatePersonData {
  id: string;
  changes: Partial<Person>;
}

export interface PersonQueryOptions {
  active?: boolean;
  limit?: number;
  offset?: number;
}
