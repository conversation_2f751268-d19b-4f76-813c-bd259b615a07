/**
 * Core Types
 * Simple, essential types for clean architecture
 */

// User types (based on v6 Person model)
export interface User {
  id: string;
  familyName?: string;
  givenName?: string;
  fullName?: string;
  alias?: string;
  email?: string;
  mobile?: string;
  gender?: string;
  isAuthenticated: boolean;
}

// Profile image type
export interface ProfileImage {
  uri?: string;
  type?: 'camera' | 'library';
  width?: number;
  height?: number;
}

// Card types (simplified from v6)
export interface Card {
  id: string;
  cardNumber?: string;
  balance?: number;
  type: 'STORED_VALUE' | 'LOYALTY' | 'MEMBERSHIP';
  isActive: boolean;
}

// Offer types (simplified from v6)
export interface Offer {
  id: string;
  title: string;
  description?: string;
  discount?: number;
  expiryDate?: string;
  isActive: boolean;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}

// Common state patterns
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}
