import Realm from 'realm';
import { CardSchema } from '../models/Card';
import { OfferSchema } from '../models/Offer';
import { PersonSchema } from '../models/Person';

const schemas = [
    CardSchema,
    OfferSchema,
    PersonSchema,
];

let realmInstance: Realm | null = null;

export class DatabaseManager {
    public static async initialize(): Promise<Realm> {
        if (realmInstance) {
            return realmInstance;
        }

        try {
            const realm = await Realm.open({
                schema: schemas,
                schemaVersion: 1,
            });
            realmInstance = realm;
            return realm;
        } catch (error) {
            console.error("Failed to open Realm database:", error);
            throw error;
        }
    }

    public static getInstance(): Realm {
        if (!realmInstance) {
            throw new Error("Realm database has not been initialized. Call initialize() first.");
        }
        return realmInstance;
    }

    public static close() {
        if (realmInstance && !realmInstance.isClosed) {
            realmInstance.close();
            realmInstance = null;
        }
    }
}
