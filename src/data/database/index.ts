/**
 * Database module exports
 *
 * Re-exports from the core database location
 * Provides clean API for V7 fresh start database operations
 */

import Realm from 'realm';

// Re-export from core database
export {
  allSchemas,
  schemaDefinitions,
  getSchema,
  hasSchema,
  getSchemaNames
} from '../../core/database/schemas';

import { DatabaseManager } from '../../core/database/config';

export {
  DatabaseManager,
  REALM_CONFIG as realmConfig,
  CURRENT_SCHEMA_VERSION as SCHEMA_VERSION,
  DB_PATH
} from '../../core/database/config';

export { runMigrations } from '../../core/database/migrations';

// Legacy compatibility functions
export function getRealm(): Realm {
  return DatabaseManager.getInstance();
}

export function closeRealm(): void {
  DatabaseManager.close();
}

export function databaseExists(): boolean {
  return DatabaseManager.databaseExists();
}

export async function installBundledDB(): Promise<void> {
  // This would copy bundled realm files if they exist
  // Implementation depends on the specific bundling strategy
  return Promise.resolve();
}
