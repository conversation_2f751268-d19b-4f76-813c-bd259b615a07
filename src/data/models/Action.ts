import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance, QueryUtils } from './BaseModel';

/**
 * Action TypeScript interfaces
 */
export interface ActionActions {
  [key: string]: any;
}

export interface ActionData {
  [key: string]: any;
}

export interface ActionTriggers {
  [key: string]: any;
}

/**
 * Main Action interface
 */
export interface Action extends Realm.Object {
  id: string;
  object: string;
  action: string;

  // Modernized mixed type properties (was stringified)
  actions?: Realm.Mixed;  // Contains ActionActions structure
  data?: Realm.Mixed;     // Contains ActionData structure
  triggers?: Realm.Mixed; // Contains ActionTriggers structure

  startTime?: Date;
  endTime?: Date;
  expiresAt?: Date;

  personId?: string;
  cardId?: string;
  masterId?: string;
  offerId?: string;
  rewardId?: string;
  messageId?: string;
  placeId?: string;

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  _model: string;
}

/**
 * Action Realm schema definition
 */
export const ActionSchema: ObjectSchema = {
  name: 'Action',
  primaryKey: 'id',
  properties: {
    id: 'string',
    object: 'string',
    action: 'string',

    // Modernized: Convert from stringified to mixed types
    actions: 'mixed?',  // Was stringified
    data: 'mixed?',     // Was stringified
    triggers: 'mixed?', // Was stringified

    startTime: 'date?',
    endTime: 'date?',
    expiresAt: 'date?',

    personId: 'string?',
    cardId: 'string?',
    masterId: 'string?',
    offerId: 'string?',
    rewardId: 'string?',
    messageId: 'string?',
    placeId: 'string?',

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    _model: { type: 'string', default: 'Action' },
  }
};

/**
 * Action model class
 */
export class ActionModel extends BaseModel {
  static className = 'Action';
  static schema = ActionSchema;

  // No injectId properties
  static injectId: string[] = [];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['actions', 'data', 'triggers'];

  /**
   * Get actions as typed object
   */
  get actionsObject(): ActionActions | null {
    return (this as any).actions as ActionActions || null;
  }

  /**
   * Get data as typed object
   */
  get dataObject(): ActionData | null {
    return (this as any).data as ActionData || null;
  }

  /**
   * Get triggers as typed object
   */
  get triggersObject(): ActionTriggers | null {
    return (this as any).triggers as ActionTriggers || null;
  }

  /**
   * Check if action is expired
   */
  get isExpired(): boolean {
    const { expiresAt } = this as any;
    return expiresAt ? new Date(expiresAt) < new Date() : false;
  }

  /**
   * Check if action is active
   */
  get isActive(): boolean {
    const { startTime, endTime } = this as any;
    const now = new Date();
    
    if (startTime && new Date(startTime) > now) return false;
    if (endTime && new Date(endTime) < now) return false;
    
    return !this.isExpired;
  }
}

/**
 * Action instance class
 */
export class ActionInstance extends BaseInstance<Action> {
  static model = ActionModel;

  get isDefer(): boolean {
    return this.instance.action === 'defer';
  }

  get isDeferImage(): boolean {
    return this.instance.action === 'deferimage';
  }

  get isNotify(): boolean {
    return this.instance.action === 'notify';
  }

  get isEngage(): boolean {
    return this.instance.object === 'engage';
  }

  get isExpired(): boolean {
    const { expiresAt } = this.instance;
    return expiresAt ? new Date(expiresAt) < new Date() : false;
  }

  get isActive(): boolean {
    const { startTime, endTime } = this.instance;
    const now = new Date();
    
    if (startTime && new Date(startTime) > now) return false;
    if (endTime && new Date(endTime) < now) return false;
    
    return !this.isExpired;
  }
}
