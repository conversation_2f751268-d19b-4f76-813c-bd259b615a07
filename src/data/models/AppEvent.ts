import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance } from './BaseModel';

/**
 * AppEvent TypeScript interfaces
 */
export interface AppEventData {
  [key: string]: any;
}

export interface AppEventLocation {
  latitude?: number;
  longitude?: number;
  accuracy?: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  timestamp?: number;
  [key: string]: any;
}

/**
 * Main AppEvent interface
 */
export interface AppEvent extends Realm.Object {
  id: string;
  name: string;
  category?: string;

  // Modernized mixed type properties (was stringified)
  data?: Realm.Mixed;     // Contains AppEventData structure
  location?: Realm.Mixed; // Contains AppEventLocation structure

  personId?: string;
  cardId?: string;
  masterId?: string;
  offerId?: string;
  rewardId?: string;
  messageId?: string;
  placeId?: string;

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  _model: string;
}

/**
 * AppEvent Realm schema definition
 */
export const AppEventSchema: ObjectSchema = {
  name: 'AppEvent',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'string',
    category: 'string?',

    // Modernized: Convert from stringified to mixed types
    data: 'mixed?',     // Was stringified
    location: 'mixed?', // Was stringified

    personId: 'string?',
    cardId: 'string?',
    masterId: 'string?',
    offerId: 'string?',
    rewardId: 'string?',
    messageId: 'string?',
    placeId: 'string?',

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    _model: { type: 'string', default: 'AppEvent' },
  }
};

/**
 * AppEvent model class
 */
export class AppEventModel extends BaseModel {
  static className = 'AppEvent';
  static schema = AppEventSchema;

  // No injectId properties
  static injectId: string[] = [];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['data', 'location'];

  /**
   * Get data as typed object
   */
  get dataObject(): AppEventData | null {
    return (this as any).data as AppEventData || null;
  }

  /**
   * Get location as typed object
   */
  get locationObject(): AppEventLocation | null {
    return (this as any).location as AppEventLocation || null;
  }

  /**
   * Check if event has location data
   */
  get hasLocation(): boolean {
    const location = this.locationObject;
    return !!(location?.latitude && location?.longitude);
  }

  /**
   * Get event type from name
   */
  get eventType(): string {
    return (this as any).name || '';
  }
}

/**
 * AppEvent instance class
 */
export class AppEventInstance extends BaseInstance<AppEvent> {
  static model = AppEventModel;

  get eventData(): AppEventData | null {
    return (this.instance as any).data as AppEventData || null;
  }

  get eventLocation(): AppEventLocation | null {
    return (this.instance as any).location as AppEventLocation || null;
  }

  get hasLocation(): boolean {
    const location = this.eventLocation;
    return !!(location?.latitude && location?.longitude);
  }

  get isUserAction(): boolean {
    const category = this.instance.category;
    return category === 'user' || category === 'interaction';
  }

  get isSystemEvent(): boolean {
    const category = this.instance.category;
    return category === 'system' || category === 'background';
  }

  get coordinates(): { latitude: number; longitude: number } | null {
    const location = this.eventLocation;
    if (location?.latitude && location?.longitude) {
      return {
        latitude: location.latitude,
        longitude: location.longitude
      };
    }
    return null;
  }
}
