import Realm from 'realm';

/**
 * Base model class for all Realm models
 * Provides common functionality without external dependencies
 */
export abstract class BaseModel extends Realm.Object {
  static className: string;
  static injectId: string[] = [];
  static readOnlyProps: string[] = [];
  static globalizeObjects: string[] = [];
  static syncUpQuery: string = '_delta != "[]"';

  /**
   * Generate a unique ID for new instances
   */
  static genId(): string {
    // Simple UUID v4 implementation
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Auto-inject IDs for embedded objects
   */
  static autoInjectId(data: any): any {
    const { injectId } = this;

    for (let i = 0; i < injectId.length; i++) {
      const embed = data[injectId[i]];
      if (!embed) continue;

      if (Array.isArray(embed)) {
        for (let j = 0; j < embed.length; j++) {
          const obj = embed[j];
          if (obj && !obj.id) obj.id = (data.id || data.compositeKey) + j;
        }
        continue;
      }
      if (!embed.id) embed.id = data.id || data.compositeKey;
    }
    return data;
  }

  /**
   * Remove read-only properties from data
   */
  static removeReadOnlyProps(data: any): any {
    const { readOnlyProps } = this;
    const cleaned = { ...data };
    
    for (const prop of readOnlyProps) {
      delete cleaned[prop];
    }
    
    return cleaned;
  }

  /**
   * Check if data has been modified
   */
  static isModified(data: any): boolean {
    const cleaned = this.removeReadOnlyProps(data);
    if (cleaned.id) delete cleaned.id;
    
    return Object.keys(cleaned).length > 0;
  }
}

/**
 * Base instance class for model instances
 */
export abstract class BaseInstance<T extends Realm.Object> {
  static model: typeof BaseModel;

  constructor(public instance: T) {}

  /**
   * Get the underlying Realm object
   */
  get object(): T {
    return this.instance;
  }

  /**
   * Convert instance to JSON
   */
  toJSON(): any {
    return this.instance.toJSON();
  }
}

/**
 * Common query utilities
 */
export class QueryUtils {
  static readonly SORT = {
    recent: 'SORT(modifiedAt DESC, createdAt DESC)',
    oldest: 'SORT(createdAt ASC, modifiedAt ASC)',
    name: 'SORT(displayName ASC, name ASC)',
  };

  static readonly QUERY = {
    notDeleted: 'deletedAt = null',
    notHidden: 'hiddenAt = null',
    notExpired: '(endTime = null || endTime > $0)',
    notPurged: '(purgeTime = null || purgeTime > $0)',
    active: 'state = "active"',
  };

  /**
   * Combine multiple query conditions with AND
   */
  static and(...conditions: string[]): string {
    return conditions.filter(Boolean).join(' && ');
  }

  /**
   * Combine multiple query conditions with OR
   */
  static or(...conditions: string[]): string {
    return `(${conditions.filter(Boolean).join(' || ')})`;
  }
}

// Migration utilities removed - fresh start strategy eliminates need for data migration
