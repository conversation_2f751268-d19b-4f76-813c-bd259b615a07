import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance, QueryUtils } from './BaseModel';
import { StoredValue } from './embedded/StoredValue';

/**
 * Card TypeScript interfaces
 */
export interface CardFormData {
  [key: string]: any;
}

export interface CardFlow {
  [key: string]: any;
}

export interface CardOptions {
  overrideMaster?: any;
  notification?: any;
  noDelete?: boolean;
  hideZeroBalance?: boolean;
  hideBalance?: boolean;
  [key: string]: any;
}

export interface CardCustom {
  [key: string]: any;
}

export interface CardPricing {
  [key: string]: any;
}

export interface CardForms {
  [key: string]: string; // { key: 'formName' } pairs to override corresponding CardMaster form
}

/**
 * Main Card interface
 */
export interface Card extends Realm.Object {
  id: string;
  number?: string;
  barcode?: string;
  barcodeType?: string;
  displayName?: string;

  startTime?: Date;
  endTime?: Date;
  imageIndex?: number;

  // Modernized mixed type properties (was stringified)
  forms?: Realm.Mixed;      // Contains CardForms structure
  formData?: Realm.Mixed;   // Contains CardFormData structure  
  custom?: Realm.Mixed;     // Contains CardCustom structure
  flow?: Realm.Mixed;       // Contains CardFlow structure
  pricing?: Realm.Mixed;    // Contains CardPricing structure
  options?: Realm.Mixed;    // Contains CardOptions structure

  // Embedded object (was in injectId)
  storedValue?: StoredValue;

  preIssued: boolean;
  useCustomImage?: boolean;

  widgets: any[]; // CardWidget[]
  state: string;
  shareModes: string[];
  
  // These remain in injectId (relationship objects)
  sharer: any; // CardSharer
  when: any;   // CardWhen
  cardImage: any; // CardMasterImage

  activeTime?: Date;
  purgeTime?: Date;
  visible?: boolean;

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;
  hiddenAt?: Date;

  personId: string;
  masterId: string;
  principalCardId?: string;
  image: any; // CustomImage
  sharings: any[]; // Sharing[]

  minAppVersion?: string;

  _initials?: string;
  _delta?: Realm.Mixed; // Was stringified
  _upload?: boolean;
  _refresh: boolean;
  _model: string;
}

/**
 * Card Realm schema definition
 */
export const CardSchema: ObjectSchema = {
  name: 'Card',
  primaryKey: 'id',
  properties: {
    id: 'string',
    number: { type: 'string', optional: true, default: '', indexed: true },
    barcode: { type: 'string', optional: true, default: '' },
    barcodeType: { type: 'string', optional: true, default: '' },
    displayName: { type: 'string', optional: true, default: '', indexed: true },

    startTime: 'date?',
    endTime: 'date?',

    imageIndex: 'int?',

    // Modernized: Convert from stringified to mixed types
    forms: 'mixed?',      // Was: { type: 'string', default: '{}' }
    formData: 'mixed?',   // Was: { type: 'string?', default: '{}' }
    custom: 'mixed?',     // Was: { type: 'string?', default: '{}' }
    flow: 'mixed?',       // Was: { type: 'string?', default: '{}' }
    pricing: 'mixed?',    // Was: { type: 'string?', default: '{}' }
    options: 'mixed?',    // Was: { type: 'string?', default: '{}' }

    // Modernized: Convert from injectId to embedded object
    storedValue: 'StoredValue?', // Was: 'StoredValue' in injectId

    preIssued: { type: 'bool', default: false },
    useCustomImage: { type: 'bool', optional: true, default: false },

    widgets: { type: 'list', objectType: 'CardWidget', default: [] },

    state: { type: 'string', default: 'active' },
    shareModes: { type: 'list', objectType: 'string', optional: true, default: [] },

    // These remain in injectId (relationship objects with mixed fields)
    sharer: 'CardSharer',
    when: 'CardWhen',
    cardImage: 'CardMasterImages', // Fixed: was 'CardMasterImage' (singular)

    activeTime: 'date?',
    purgeTime: 'date?',
    visible: { type: 'bool', optional: true, default: true },

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',
    hiddenAt: 'date?',

    personId: 'string',
    masterId: { type: 'string', indexed: true },
    principalCardId: 'string?',
    image: 'CustomImage',
    sharings: { type: 'list', objectType: 'Sharing', default: [] },

    minAppVersion: 'string?',

    _initials: 'string?',
    _delta: 'mixed?',     // Was: { type: 'string?', default: '[]' }
    _upload: { type: 'bool', optional: true, default: false },
    _refresh: { type: 'bool', default: false },
    _model: { type: 'string', default: 'Card' },
  }
};

/**
 * Card model class
 */
export class CardModel extends BaseModel {
  static className = 'Card';
  static schema = CardSchema;

  // Modernized: Updated injectId - removed 'storedValue' (now embedded)
  static injectId = ['cardImage', 'sharer', 'when'];

  static readOnlyProps = [
    'flow', 'storedValue', 'master', '_initials', '_delta', 
    '_refresh', '_upload', '_category'
  ];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['forms', 'formData', 'custom', 'flow', 'pricing', 'options', '_delta'];

  static syncUpQuery = `${QueryUtils.QUERY.notDeleted} && ${QueryUtils.QUERY.notHidden}`;

  /**
   * Get form data as typed object
   */
  get formDataObject(): CardFormData | null {
    return (this as any).formData as CardFormData || null;
  }

  /**
   * Get flow as typed object
   */
  get flowObject(): CardFlow | null {
    return (this as any).flow as CardFlow || null;
  }

  /**
   * Get options as typed object
   */
  get optionsObject(): CardOptions | null {
    return (this as any).options as CardOptions || null;
  }

  /**
   * Get custom properties as typed object
   */
  get customObject(): CardCustom | null {
    return (this as any).custom as CardCustom || null;
  }

  /**
   * Get pricing as typed object
   */
  get pricingObject(): CardPricing | null {
    return (this as any).pricing as CardPricing || null;
  }

  /**
   * Get forms as typed object
   */
  get formsObject(): CardForms | null {
    return (this as any).forms as CardForms || null;
  }
}

/**
 * Card instance class
 */
export class CardInstance extends BaseInstance<Card> {
  static model = CardModel;

  get isPerkdID(): boolean {
    // Implementation would depend on PERKD_ID constant
    return false; // Placeholder
  }

  get isCustomCard(): boolean {
    // Implementation would depend on CUSTOM_CARD constant
    return false; // Placeholder
  }

  get isActiveDigital(): boolean {
    const { deletedAt, hiddenAt } = this.instance;
    return !(deletedAt || hiddenAt);
  }

  get isStoredValue(): boolean {
    return !!this.instance.storedValue?.balance;
  }

  get needsAttention(): boolean {
    return !this.isActiveDigital;
  }
}
