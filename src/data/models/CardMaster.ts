import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance } from './BaseModel';
import { CardMasterBrand } from './embedded/CardMasterBrand';
import { CardMasterFeatures } from './embedded/CardMasterFeatures';
import { CardMasterPolicies } from './embedded/CardMasterPolicies';
import { CardMasterDiscover } from './embedded/CardMasterDiscover';
import { CardMasterReminders } from './embedded/CardMasterReminders';
import { CardMasterStoredValue } from './embedded/CardMasterStoredValue';
import { CardMasterImages } from './embedded/CardMasterImages';

/**
 * CardMaster TypeScript interfaces
 */
export interface CardMasterDetails {
  [key: string]: any;
}

export interface CardMasterCommerce {
  type?: string;
  pricings?: any[];
  taxIncluded?: boolean;
  taxes?: any[];
  [key: string]: any;
}

export interface CardMasterFulfillments {
  deliver?: any;
  pickup?: any[];
  store?: any;
  dinein?: any;
  vending?: any;
  digital?: any;
  [key: string]: any;
}

export interface CardMasterPayments {
  payee?: string;
  currency?: any;
  limits?: any;
  methods?: any;
  taxes?: any[];
  [key: string]: any;
}

/**
 * Main CardMaster interface
 */
export interface CardMaster extends Realm.Object {
  id: string;
  name?: string;
  description?: string;

  // Embedded objects (was in injectId)
  brand?: CardMasterBrand;
  features?: CardMasterFeatures;
  policies?: CardMasterPolicies;
  discover?: CardMasterDiscover;
  reminders?: CardMasterReminders;
  storedValue?: CardMasterStoredValue;
  images?: CardMasterImages;

  barcodeTypes: string[];
  numberFormats: string[];
  numberPatterns: string[];
  barcodePatterns: string[];
  minAppVersion?: string;

  tags: any; // CardMasterTag

  forms?: string;
  widgets: any[]; // Widget[]

  // Modernized mixed type properties (was stringified)
  details?: Realm.Mixed;      // Contains CardMasterDetails structure
  payments?: Realm.Mixed;     // Contains CardMasterPayments structure
  commerce?: Realm.Mixed;     // Contains CardMasterCommerce structure
  fulfillments?: Realm.Mixed; // Contains CardMasterFulfillments structure
  sharePolicies?: Realm.Mixed;
  bagPolicy?: Realm.Mixed;
  pricings?: Realm.Mixed;
  flows?: Realm.Mixed;
  tenant?: Realm.Mixed;

  // These remain in injectId (relationship objects with mixed fields)
  options: any; // CardMasterOptions
  globalize: any; // CardMasterGlobalize

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  _refresh: boolean;
  _model: string;
}

/**
 * CardMaster Realm schema definition
 */
export const CardMasterSchema: ObjectSchema = {
  name: 'CardMaster',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: { type: 'string', optional: true, default: '', indexed: true },
    description: { type: 'string', optional: true, default: '' },

    // Modernized: Convert from injectId to embedded objects
    brand: 'CardMasterBrand?',
    features: 'CardMasterFeatures?',
    policies: 'CardMasterPolicies?',
    discover: 'CardMasterDiscover?',
    reminders: 'CardMasterReminders?',
    storedValue: 'CardMasterStoredValue?',
    images: 'CardMasterImages?',

    barcodeTypes: { type: 'list', objectType: 'string', default: [] },
    numberFormats: { type: 'list', objectType: 'string', optional: true, default: [] },
    numberPatterns: { type: 'list', objectType: 'string', optional: true, default: [] },
    barcodePatterns: { type: 'list', objectType: 'string', optional: true, default: [] },
    minAppVersion: 'string?',

    tags: 'CardMasterTag',

    forms: 'string?',
    widgets: { type: 'list', objectType: 'Widget', default: [] },

    // Modernized: Convert from stringified to mixed types
    details: 'mixed?',        // Was: { type: 'string?', default: '{}' }
    payments: 'mixed?',       // Was: { type: 'string?', default: '{}' }
    commerce: 'mixed?',       // Was: { type: 'string?', default: '{}' }
    fulfillments: 'mixed?',   // Was: { type: 'string?', default: '{}' }
    sharePolicies: 'mixed?',  // Was stringified
    bagPolicy: 'mixed?',      // Was stringified
    pricings: 'mixed?',       // Was stringified
    flows: 'mixed?',          // Was stringified
    tenant: 'mixed?',         // Was stringified

    // These remain in injectId (relationship objects with mixed fields)
    options: 'CardMasterOptions',
    globalize: 'CardMasterGlobalize',

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    _refresh: { type: 'bool', default: false },
    _model: { type: 'string', default: 'CardMaster' },
  }
};

/**
 * CardMaster model class
 */
export class CardMasterModel extends BaseModel {
  static className = 'CardMaster';
  static schema = CardMasterSchema;

  // Modernized: Updated injectId - keep only relationship objects with mixed fields
  static injectId = ['options', 'globalize'];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['details', 'forms', 'sharePolicies', 'commerce', 'fulfillments', 'bagPolicy', 'pricings', 'payments', 'flows', 'tenant'];

  static globalizeObjects = ['forms', 'widgets'];

  /**
   * Get details as typed object
   */
  get detailsObject(): CardMasterDetails | null {
    return (this as any).details as CardMasterDetails || null;
  }

  /**
   * Get commerce as typed object
   */
  get commerceObject(): CardMasterCommerce | null {
    return (this as any).commerce as CardMasterCommerce || null;
  }

  /**
   * Get fulfillments as typed object
   */
  get fulfillmentsObject(): CardMasterFulfillments | null {
    return (this as any).fulfillments as CardMasterFulfillments || null;
  }

  /**
   * Get payments as typed object
   */
  get paymentsObject(): CardMasterPayments | null {
    return (this as any).payments as CardMasterPayments || null;
  }
}

/**
 * CardMaster instance class
 */
export class CardMasterInstance extends BaseInstance<CardMaster> {
  static model = CardMasterModel;

  get brandName(): string {
    return this.instance.brand?.short || this.instance.brand?.long || this.instance.name || '';
  }

  get isStoredValueEnabled(): boolean {
    return this.instance.features?.storedValue === true;
  }

  get isManaged(): boolean {
    return this.instance.features?.managed === true;
  }
}
