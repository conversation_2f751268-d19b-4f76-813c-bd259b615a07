import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance } from './BaseModel';

/**
 * Merchant TypeScript interfaces
 */
export interface MerchantSoldAt {
  [key: string]: any;
}

export interface MerchantLogo {
  [key: string]: any;
}

export interface MerchantStyle {
  [key: string]: any;
}

export interface MerchantSearch {
  [key: string]: any;
}

export interface MerchantPage {
  [key: string]: any;
}

export interface MerchantCrawl {
  [key: string]: any;
}

export interface MerchantShops {
  [key: string]: any;
}

export interface MerchantDynamic {
  [key: string]: any;
}

export interface MerchantCustom {
  [key: string]: any;
}

export interface MerchantAffiliate {
  [key: string]: any;
}

export interface MerchantRateLimit {
  [key: string]: any;
}

export interface MerchantAppOptions {
  [key: string]: any;
}

/**
 * Main Merchant interface
 */
export interface Merchant extends Realm.Object {
  id: string;
  name?: string;
  description?: string;
  website?: string;

  // Modernized mixed type properties (was stringified)
  soldAt?: Realm.Mixed;      // Contains MerchantSoldAt structure
  logo?: Realm.Mixed;        // Contains MerchantLogo structure
  style?: Realm.Mixed;       // Contains MerchantStyle structure
  search?: Realm.Mixed;      // Contains MerchantSearch structure
  page?: Realm.Mixed;        // Contains MerchantPage structure
  crawl?: Realm.Mixed;       // Contains MerchantCrawl structure
  shops?: Realm.Mixed;       // Contains MerchantShops structure
  dynamic?: Realm.Mixed;     // Contains MerchantDynamic structure
  custom?: Realm.Mixed;      // Contains MerchantCustom structure
  affiliate?: Realm.Mixed;   // Contains MerchantAffiliate structure
  rateLimit?: Realm.Mixed;   // Contains MerchantRateLimit structure
  appOptions?: Realm.Mixed;  // Contains MerchantAppOptions structure

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  _model: string;
}

/**
 * Merchant Realm schema definition
 */
export const MerchantSchema: ObjectSchema = {
  name: 'Merchant',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: { type: 'string', optional: true, default: '' },
    description: { type: 'string', optional: true, default: '' },
    website: 'string?',

    // Modernized: Convert from stringified to mixed types
    soldAt: 'mixed?',      // Was stringified
    logo: 'mixed?',        // Was stringified
    style: 'mixed?',       // Was stringified
    search: 'mixed?',      // Was stringified
    page: 'mixed?',        // Was stringified
    crawl: 'mixed?',       // Was stringified
    shops: 'mixed?',       // Was stringified
    dynamic: 'mixed?',     // Was stringified
    custom: 'mixed?',      // Was stringified
    affiliate: 'mixed?',   // Was stringified
    rateLimit: 'mixed?',   // Was stringified
    appOptions: 'mixed?',  // Was stringified

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    _model: { type: 'string', default: 'Merchant' },
  }
};

/**
 * Merchant model class
 */
export class MerchantModel extends BaseModel {
  static className = 'Merchant';
  static schema = MerchantSchema;

  // No injectId properties
  static injectId: string[] = [];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['soldAt', 'logo', 'style', 'search', 'page', 'crawl', 'shops', 'dynamic', 'custom', 'affiliate', 'rateLimit', 'appOptions'];

  /**
   * Get soldAt as typed object
   */
  get soldAtObject(): MerchantSoldAt | null {
    return (this as any).soldAt as MerchantSoldAt || null;
  }

  /**
   * Get logo as typed object
   */
  get logoObject(): MerchantLogo | null {
    return (this as any).logo as MerchantLogo || null;
  }

  /**
   * Get style as typed object
   */
  get styleObject(): MerchantStyle | null {
    return (this as any).style as MerchantStyle || null;
  }

  /**
   * Get search as typed object
   */
  get searchObject(): MerchantSearch | null {
    return (this as any).search as MerchantSearch || null;
  }

  /**
   * Get page as typed object
   */
  get pageObject(): MerchantPage | null {
    return (this as any).page as MerchantPage || null;
  }

  /**
   * Get crawl as typed object
   */
  get crawlObject(): MerchantCrawl | null {
    return (this as any).crawl as MerchantCrawl || null;
  }

  /**
   * Get shops as typed object
   */
  get shopsObject(): MerchantShops | null {
    return (this as any).shops as MerchantShops || null;
  }

  /**
   * Get dynamic as typed object
   */
  get dynamicObject(): MerchantDynamic | null {
    return (this as any).dynamic as MerchantDynamic || null;
  }

  /**
   * Get custom as typed object
   */
  get customObject(): MerchantCustom | null {
    return (this as any).custom as MerchantCustom || null;
  }

  /**
   * Get affiliate as typed object
   */
  get affiliateObject(): MerchantAffiliate | null {
    return (this as any).affiliate as MerchantAffiliate || null;
  }

  /**
   * Get rateLimit as typed object
   */
  get rateLimitObject(): MerchantRateLimit | null {
    return (this as any).rateLimit as MerchantRateLimit || null;
  }

  /**
   * Get appOptions as typed object
   */
  get appOptionsObject(): MerchantAppOptions | null {
    return (this as any).appOptions as MerchantAppOptions || null;
  }
}

/**
 * Merchant instance class
 */
export class MerchantInstance extends BaseInstance<Merchant> {
  static model = MerchantModel;

  get merchantName(): string {
    return this.instance.name || '';
  }

  get hasWebsite(): boolean {
    return !!this.instance.website;
  }

  get logoUrl(): string {
    const logo = (this.instance as any).logo as MerchantLogo;
    return logo?.url || '';
  }

  get hasAffiliate(): boolean {
    const affiliate = (this.instance as any).affiliate as MerchantAffiliate;
    return !!affiliate && Object.keys(affiliate).length > 0;
  }

  get hasCustomization(): boolean {
    const custom = (this.instance as any).custom as MerchantCustom;
    return !!custom && Object.keys(custom).length > 0;
  }
}
