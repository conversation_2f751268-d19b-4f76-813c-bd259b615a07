import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance } from './BaseModel';

/**
 * Message TypeScript interfaces
 */
export interface MessagePersonalize {
  [key: string]: any;
}

export interface MessageLocalStates {
  [key: string]: any;
}

export interface MessageOptions {
  [key: string]: any;
}

/**
 * Main Message interface
 */
export interface Message extends Realm.Object {
  id: string;
  subject: string;
  preview?: string;

  kind?: string;

  // These remain in injectId (relationship objects)
  sender: any; // MessageSender
  cover: any; // MessageCoverWebview
  body: any; // MessageBodyWebview
  globalize: any; // MessageGlobalize
  when: any; // MessageWhen

  // Modernized mixed type properties (was stringified)
  personalize?: Realm.Mixed; // Contains MessagePersonalize structure
  localStates?: Realm.Mixed; // Contains MessageLocalStates structure
  options?: Realm.Mixed;     // Contains MessageOptions structure

  activeTime?: Date;
  purgeTime?: Date;

  visible?: boolean;

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  templateId: string;
  personId: string;
  cardId?: string;
  merchantId?: string;

  minAppVersion?: string;

  _refresh: boolean;
  _model: string;
}

/**
 * Message Realm schema definition
 */
export const MessageSchema: ObjectSchema = {
  name: 'Message',
  primaryKey: 'id',
  properties: {
    id: 'string',
    subject: { type: 'string', default: '' },
    preview: { type: 'string', optional: true, default: '' },

    kind: 'string?',

    // These remain in injectId (relationship objects)
    sender: 'MessageSender',
    cover: 'MessageCoverWebview',
    body: 'MessageBodyWebview',
    globalize: 'MessageGlobalize',
    when: 'MessageWhen',

    // Modernized: Convert from stringified to mixed types
    personalize: 'mixed?',  // Was: { type: 'string?', default: '{}' }
    localStates: 'mixed?',  // Was: { type: 'string?', default: '{}' }
    options: 'mixed?',      // Was: { type: 'string?', default: '{}' }

    activeTime: 'date?',
    purgeTime: 'date?',

    visible: { type: 'bool', optional: true, default: true },

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    templateId: 'string',
    personId: 'string',
    cardId: { type: 'string', optional: true, default: '', indexed: true },
    merchantId: { type: 'string', optional: true, default: '', indexed: true },

    minAppVersion: 'string?',

    _refresh: { type: 'bool', default: false },
    _model: { type: 'string', default: 'Message' },
  }
};

/**
 * Message model class
 */
export class MessageModel extends BaseModel {
  static className = 'Message';
  static schema = MessageSchema;

  // Keep all injectId properties (all are relationship objects)
  static injectId = ['sender', 'cover', 'body', 'globalize', 'when'];

  static readOnlyProps = ['globalize', '_refresh'];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['personalize', 'localStates', 'options'];

  /**
   * Get personalize as typed object
   */
  get personalizeObject(): MessagePersonalize | null {
    return (this as any).personalize as MessagePersonalize || null;
  }

  /**
   * Get local states as typed object
   */
  get localStatesObject(): MessageLocalStates | null {
    return (this as any).localStates as MessageLocalStates || null;
  }

  /**
   * Get options as typed object
   */
  get optionsObject(): MessageOptions | null {
    return (this as any).options as MessageOptions || null;
  }

  /**
   * Get brand name from sender
   */
  get brandName(): string {
    return (this as any).sender?.name || '';
  }

  /**
   * Get preview image from cover
   */
  get previewImage(): string {
    return (this as any).preview || '';
  }

  /**
   * Check if message has body content
   */
  get hasBody(): boolean {
    return !!(this as any).body?.html;
  }
}

/**
 * Message instance class
 */
export class MessageInstance extends BaseInstance<Message> {
  static model = MessageModel;

  get brand(): string {
    return (this.instance as any).sender?.name || '';
  }

  get image(): string {
    return this.instance.preview || '';
  }

  get hasBody(): boolean {
    return !!(this.instance as any).body?.html;
  }

  get isActive(): boolean {
    const { activeTime, purgeTime, deletedAt } = this.instance;
    const now = new Date();
    
    if (deletedAt) return false;
    if (activeTime && new Date(activeTime) > now) return false;
    if (purgeTime && new Date(purgeTime) < now) return false;
    
    return true;
  }

  get isRead(): boolean {
    const when = (this.instance as any).when;
    return !!(when?.viewed || when?.seen);
  }

  get senderName(): string {
    return (this.instance as any).sender?.name || '';
  }

  get senderLogo(): string {
    return (this.instance as any).sender?.logo || '';
  }
}
