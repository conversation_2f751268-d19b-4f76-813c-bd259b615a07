import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance } from './BaseModel';

/**
 * Offer TypeScript interfaces
 */
export interface OfferOptions {
  appOnly?: boolean;
  redeemOnline?: boolean;
  payment?: any;
  hideButton?: boolean;
  buttonLabel?: string;
  buttonLink?: string;
  showVenueName?: boolean;
  [key: string]: any;
}

export interface OfferPlace {
  [key: string]: any;
}

export interface OfferFields {
  [key: string]: any;
}

export interface OfferVenue {
  [key: string]: any;
}

export interface OfferStyle {
  [key: string]: any;
}

export interface OfferCheckin {
  [key: string]: any;
}

export interface OfferItems {
  id?: string;
  variantId?: string;
  sku?: string;
  quantity?: number;
  [key: string]: any;
}

export interface OfferLocalStates {
  [key: string]: any;
}

/**
 * Main Offer interface
 */
export interface Offer extends Realm.Object {
  id: string;
  masterId: string;
  name?: string;
  shortName?: string;
  brand?: string;
  title?: string;
  description?: string;
  terms?: string;

  kind?: string; // [ discount, voucher, ticket ]

  // These remain in injectId (relationship objects)
  code: any; // OfferCode
  images: any[]; // OfferImage[]
  discount: any; // OfferDiscount
  redemption: any; // Redemption
  sharer: any; // OfferSharer
  globalize: any; // OfferGlobalize
  when: any; // OfferWhen

  barcodeType?: string;
  barcode?: string;

  // Modernized mixed type properties (was stringified)
  options?: Realm.Mixed;     // Contains OfferOptions structure
  place?: Realm.Mixed;       // Contains OfferPlace structure
  fields?: Realm.Mixed;      // Contains OfferFields structure
  venue?: Realm.Mixed;       // Contains OfferVenue structure
  style?: Realm.Mixed;       // Contains OfferStyle structure
  checkin?: Realm.Mixed;     // Contains OfferCheckin structure
  items?: Realm.Mixed;       // Contains OfferItems[] structure
  _localStates?: Realm.Mixed; // Contains OfferLocalStates structure

  orderId?: string;
  applet: any; // AppletWebview

  state?: string;
  shareModes: string[];

  startTime?: Date;
  endTime?: Date;
  activeTime?: Date;
  purgeTime?: Date;

  visible?: boolean;

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  personId: string;
  cardId?: string;
  merchantId?: string;
  bookingId?: string;
  sharings: any[]; // Sharing[]

  minAppVersion?: string;

  _refresh: boolean;
  _model: string;
}

/**
 * Offer Realm schema definition
 */
export const OfferSchema: ObjectSchema = {
  name: 'Offer',
  primaryKey: 'id',
  properties: {
    id: 'string',
    masterId: 'string',
    name: { type: 'string', optional: true, default: '' },
    shortName: { type: 'string', optional: true, default: '' },
    brand: { type: 'string', optional: true, default: '' },
    title: { type: 'string', optional: true, default: '' },
    description: { type: 'string', optional: true, default: '' },
    terms: { type: 'string', optional: true, default: '' },

    kind: 'string?', // [ discount, voucher, ticket ]

    // These remain in injectId (relationship objects)
    code: 'OfferCode',
    images: { type: 'list', objectType: 'OfferImage', default: [] },
    discount: 'OfferDiscount',
    redemption: 'Redemption',
    sharer: 'OfferSharer',
    globalize: 'OfferGlobalize',
    when: 'OfferWhen',

    barcodeType: { type: 'string', optional: true, default: 'CODE128' },
    barcode: 'string?',

    // Modernized: Convert from stringified to mixed types
    options: 'mixed?',      // Was: { type: 'string?', default: '{}' }
    place: 'mixed?',        // Was stringified
    fields: 'mixed?',       // Was: { type: 'string?', default: '[]' }
    venue: 'mixed?',        // Was: { type: 'string?', default: '{}' }
    style: 'mixed?',        // Was: { type: 'string?', default: '{}' }
    checkin: 'mixed?',      // Was: { type: 'string?', default: '{}' }
    items: 'mixed?',        // Was: { type: 'string?', default: '[]' }
    _localStates: 'mixed?', // Was: { type: 'string?', default: '{}' }

    orderId: 'string?',
    applet: 'AppletWebview',

    state: { type: 'string', optional: true, default: 'pending' },
    shareModes: { type: 'list', objectType: 'string', optional: true, default: [] },

    startTime: 'date?',
    endTime: 'date?',
    activeTime: 'date?',
    purgeTime: 'date?',

    visible: { type: 'bool', optional: true, default: true },

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    personId: 'string',
    cardId: { type: 'string', optional: true, indexed: true },
    merchantId: { type: 'string', optional: true, indexed: true },
    bookingId: { type: 'string', optional: true, indexed: true },
    sharings: { type: 'list', objectType: 'Sharing', default: [] },

    minAppVersion: 'string?',

    _refresh: { type: 'bool', default: false },
    _model: { type: 'string', default: 'Offer' },
  }
};

/**
 * Offer model class
 */
export class OfferModel extends BaseModel {
  static className = 'Offer';
  static schema = OfferSchema;

  // Keep all injectId properties (all are relationship objects)
  static injectId = ['code', 'images', 'discount', 'redemption', 'sharer', 'globalize', 'when'];

  static readOnlyProps = ['sharings', 'globalize', '_refresh', '_localStates'];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['options', 'place', 'fields', 'venue', 'style', 'checkin', 'items', '_localStates'];

  /**
   * Get options as typed object
   */
  get optionsObject(): OfferOptions | null {
    return (this as any).options as OfferOptions || null;
  }

  /**
   * Get place as typed object
   */
  get placeObject(): OfferPlace | null {
    return (this as any).place as OfferPlace || null;
  }

  /**
   * Get fields as typed object
   */
  get fieldsObject(): OfferFields | null {
    return (this as any).fields as OfferFields || null;
  }

  /**
   * Get venue as typed object
   */
  get venueObject(): OfferVenue | null {
    return (this as any).venue as OfferVenue || null;
  }

  /**
   * Get style as typed object
   */
  get styleObject(): OfferStyle | null {
    return (this as any).style as OfferStyle || null;
  }

  /**
   * Get checkin as typed object
   */
  get checkinObject(): OfferCheckin | null {
    return (this as any).checkin as OfferCheckin || null;
  }

  /**
   * Get items as typed array
   */
  get itemsArray(): OfferItems[] | null {
    return (this as any).items as OfferItems[] || null;
  }

  /**
   * Get local states as typed object
   */
  get localStatesObject(): OfferLocalStates | null {
    return (this as any)._localStates as OfferLocalStates || null;
  }
}

/**
 * Offer instance class
 */
export class OfferInstance extends BaseInstance<Offer> {
  static model = OfferModel;

  get isActive(): boolean {
    const { state, startTime, endTime } = this.instance;
    const now = new Date();
    
    if (state !== 'active') return false;
    if (startTime && new Date(startTime) > now) return false;
    if (endTime && new Date(endTime) < now) return false;
    
    return true;
  }

  get isExpired(): boolean {
    const { endTime } = this.instance;
    return endTime ? new Date(endTime) < new Date() : false;
  }

  get canRedeem(): boolean {
    return this.isActive && !this.isExpired;
  }

  get brandName(): string {
    return this.instance.brand || this.instance.name || '';
  }
}
