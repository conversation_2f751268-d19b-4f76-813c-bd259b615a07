import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance } from './BaseModel';
import { PersonName } from './embedded/PersonName';

/**
 * Person TypeScript interfaces
 */
export interface PersonPreference {
  [key: string]: any;
}

/**
 * Main Person interface
 */
export interface Person extends Realm.Object {
  id: string;
  familyName?: string;
  givenName?: string;
  fullName?: string;
  alias?: string;

  // Embedded object (was in injectId)
  name?: PersonName;

  gender?: string; // { "enum": ["m", "f"] }
  ethnicity?: number;
  religion?: number;

  dateList: any[]; // PersonDate[]
  phoneList: any[]; // PersonPhone[]
  emailList: any[]; // PersonEmail[]
  addressList: any[]; // PersonAddress[]

  image: any; // ProfileImage

  // These remain in injectId (relationship objects)
  brands: any; // PersonBrands
  products: any; // PersonProducts
  tags: string[];

  // Modernized mixed type properties (was stringified)
  preference?: Realm.Mixed; // Contains PersonPreference structure

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  _delta?: Realm.Mixed; // Was stringified
  _upload?: boolean;
  _refresh: boolean;
  _model: string;
}

/**
 * Person Realm schema definition
 */
export const PersonSchema: ObjectSchema = {
  name: 'Person',
  primaryKey: 'id',
  properties: {
    id: 'string',
    familyName: { type: 'string', optional: true, default: '', },
    givenName: { type: 'string', optional: true, default: '', },
    fullName: { type: 'string', optional: true, default: '', },
    alias: { type: 'string', optional: true, default: '', },

    // Modernized: Convert from injectId to embedded object
    name: 'PersonName?', // Was: 'Name' in injectId

    gender: 'string?', // { "enum": ["m", "f"] }
    ethnicity: 'int?',
    religion: 'int?',

    dateList: { type: 'list', objectType: 'PersonDate', default: [] },
    phoneList: { type: 'list', objectType: 'PersonPhone', default: [] },
    emailList: { type: 'list', objectType: 'PersonEmail', default: [] },
    addressList: { type: 'list', objectType: 'PersonAddress', default: [] },

    image: 'ProfileImage',

    // These remain in injectId (relationship objects)
    brands: 'PersonBrands',
    products: 'PersonProducts',
    tags: { type: 'list', objectType: 'string', optional: true, default: [] },

    // Modernized: Convert from stringified to mixed type
    preference: 'mixed?', // Was: { type: 'string?', default: '{}' }

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    _delta: 'mixed?', // Was: { type: 'string?', default: '[]' }
    _upload: { type: 'bool', optional: true, default: false },
    _refresh: { type: 'bool', default: false },
    _model: { type: 'string', default: 'Person' },
  }
};

/**
 * Person model class
 */
export class PersonModel extends BaseModel {
  static className = 'Person';
  static schema = PersonSchema;

  // Modernized: Updated injectId - removed 'name' (now embedded)
  static injectId = ['brands', 'products'];

  static readOnlyProps = ['_delta', '_upload', '_refresh'];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['preference', '_delta'];

  /**
   * Get preference as typed object
   */
  get preferenceObject(): PersonPreference | null {
    return (this as any).preference as PersonPreference || null;
  }

  /**
   * Get display name based on name configuration
   */
  get displayName(): string {
    const person = this as any;
    const { givenName, familyName, alias, name } = person;
    const { order } = name || {};

    // Simple implementation - would need the actual getDisplayName function
    if (alias) return alias;
    if (order === 'familygiven') return `${familyName} ${givenName}`.trim();
    return `${givenName} ${familyName}`.trim();
  }

  /**
   * Get primary phone number
   */
  get primaryPhone(): string {
    const phoneList = (this as any).phoneList || [];
    const phone = phoneList[0];
    return phone?.fullNumber || '';
  }

  /**
   * Get primary email address
   */
  get primaryEmail(): string {
    const emailList = (this as any).emailList || [];
    const email = emailList[0];
    return email?.address || '';
  }

  /**
   * Get primary address
   */
  get primaryAddress(): any {
    const addressList = (this as any).addressList || [];
    return addressList.find((addr: any) => addr) || null;
  }

  /**
   * Calculate age from birth date
   */
  get age(): number {
    const dateList = (this as any).dateList || [];
    const birthday = dateList.find((d: any) => d.name === 'birth');
    
    if (!birthday) return 0;
    
    // Simple age calculation - would need proper date handling
    const birthYear = birthday.year;
    if (!birthYear) return 0;
    
    const currentYear = new Date().getFullYear();
    return currentYear - birthYear;
  }
}

/**
 * Person instance class
 */
export class PersonInstance extends BaseInstance<Person> {
  static model = PersonModel;

  get fullDisplayName(): string {
    return (this.instance as any).displayName || this.instance.fullName || '';
  }

  get hasProfileImage(): boolean {
    return !!(this.instance.image as any)?.original;
  }

  get isComplete(): boolean {
    const { givenName, familyName, phoneList, emailList } = this.instance;
    return !!(givenName && familyName && phoneList.length && emailList.length);
  }
}
