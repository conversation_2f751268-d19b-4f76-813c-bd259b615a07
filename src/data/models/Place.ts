import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance } from './BaseModel';

/**
 * Place TypeScript interfaces
 */
export interface PlaceOpeningHours {
  [key: string]: any;
}

export interface PlaceExternal {
  provider?: string;
  crmId?: string;
  crm?: {
    storeId?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

export interface PlacePickup {
  available?: boolean;
  hours?: any;
  timeslot?: number; // Time interval (minutes) for pickup schedule
  [key: string]: any;
}

export interface PlaceDeliver {
  available?: boolean;
  hours?: any;
  radius?: number;
  [key: string]: any;
}

export interface PlaceDinein {
  available?: boolean;
  hours?: any;
  [key: string]: any;
}

export interface PlaceBooking {
  available?: boolean;
  [key: string]: any;
}

export interface PlaceQueuing {
  available?: boolean;
  [key: string]: any;
}

export interface PlaceSettingList {
  [key: string]: any;
}

/**
 * Main Place interface
 */
export interface Place extends Realm.Object {
  id: string;
  type?: string; // https://developers.google.com/places/supported_types
  name?: string;

  // These remain in injectId (relationship objects)
  brand: any; // PlaceBrand
  geo: any; // PlaceGeometry
  locale: any; // PlaceLocale
  urls: any[]; // PlaceUrl[]
  tags: any; // PlaceTag
  globalize: any; // PlaceGlobalize
  photoImages: any[]; // PlaceImage[]

  // Modernized mixed type properties (was stringified)
  openingHours?: Realm.Mixed; // Contains PlaceOpeningHours structure
  external?: Realm.Mixed;     // Contains PlaceExternal structure
  pickup?: Realm.Mixed;       // Contains PlacePickup structure
  deliver?: Realm.Mixed;      // Contains PlaceDeliver structure
  dinein?: Realm.Mixed;       // Contains PlaceDinein structure
  booking?: Realm.Mixed;      // Contains PlaceBooking structure
  queuing?: Realm.Mixed;      // Contains PlaceQueuing structure
  settingList?: Realm.Mixed;  // Contains PlaceSettingList structure

  startTime?: Date;
  endTime?: Date;

  custom: boolean;
  visible?: boolean;

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  // Props required for custom Place:
  personId?: string;
  cardId?: string;
  officialId?: string;

  phoneList: any[]; // PlacePhone[]
  addressList: any[]; // PlaceAddress[]

  lat?: number;
  lng?: number;

  _delta?: Realm.Mixed; // Was stringified
  _refresh: boolean;
  _model: string;
}

/**
 * Place Realm schema definition
 */
export const PlaceSchema: ObjectSchema = {
  name: 'Place',
  primaryKey: 'id',
  properties: {
    id: 'string',
    type: 'string?', // https://developers.google.com/places/supported_types
    name: { type: 'string', optional: true, indexed: true },

    // These remain in injectId (relationship objects)
    brand: 'PlaceBrand',
    geo: 'PlaceGeometry',
    locale: 'PlaceLocale',
    urls: { type: 'list', objectType: 'PlaceUrl', default: [] },
    tags: 'PlaceTag',
    globalize: 'PlaceGlobalize',
    photoImages: { type: 'list', objectType: 'PlaceImage', default: [] },

    // Modernized: Convert from stringified to mixed types
    openingHours: 'mixed?', // Was: { type: 'string?', default: '{}' }
    external: 'mixed?',     // Was: { type: 'string?', default: '{}' }
    pickup: 'mixed?',       // Was: { type: 'string?', default: '{}' }
    deliver: 'mixed?',      // Was: { type: 'string?', default: '{}' }
    dinein: 'mixed?',       // Was: { type: 'string?', default: '{}' }
    booking: 'mixed?',      // Was: { type: 'string?', default: '{}' }
    queuing: 'mixed?',      // Was: { type: 'string?', default: '{}' }
    settingList: 'mixed?',  // Was: { type: 'string?', default: '[]' }

    startTime: 'date?',
    endTime: 'date?',

    custom: { type: 'bool', default: false },
    visible: { type: 'bool', optional: true, default: true },

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    // Props required for custom Place:
    personId: 'string?',
    cardId: { type: 'string', optional: true, indexed: true },
    officialId: 'string?',

    phoneList: { type: 'list', objectType: 'PlacePhone', default: [] },
    addressList: { type: 'list', objectType: 'PlaceAddress', default: [] },

    lat: 'double?',
    lng: 'double?',

    _delta: 'mixed?', // Was: { type: 'string?', default: '[]' }
    _refresh: { type: 'bool', default: false },
    _model: { type: 'string', default: 'Place' },
  }
};

/**
 * Place model class
 */
export class PlaceModel extends BaseModel {
  static className = 'Place';
  static schema = PlaceSchema;

  // Keep all injectId properties (all are relationship objects)
  static injectId = ['brand', 'geo', 'locale', 'urls', 'tags', 'globalize', 'photoImages'];

  static readOnlyProps = ['globalize', '_delta', '_refresh'];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['openingHours', 'external', 'pickup', 'deliver', 'dinein', 'booking', 'queuing', 'settingList', '_delta'];

  /**
   * Get opening hours as typed object
   */
  get openingHoursObject(): PlaceOpeningHours | null {
    return (this as any).openingHours as PlaceOpeningHours || null;
  }

  /**
   * Get external as typed object
   */
  get externalObject(): PlaceExternal | null {
    return (this as any).external as PlaceExternal || null;
  }

  /**
   * Get pickup as typed object
   */
  get pickupObject(): PlacePickup | null {
    return (this as any).pickup as PlacePickup || null;
  }

  /**
   * Get deliver as typed object
   */
  get deliverObject(): PlaceDeliver | null {
    return (this as any).deliver as PlaceDeliver || null;
  }

  /**
   * Get dinein as typed object
   */
  get dineinObject(): PlaceDinein | null {
    return (this as any).dinein as PlaceDinein || null;
  }

  /**
   * Get booking as typed object
   */
  get bookingObject(): PlaceBooking | null {
    return (this as any).booking as PlaceBooking || null;
  }

  /**
   * Get queuing as typed object
   */
  get queuingObject(): PlaceQueuing | null {
    return (this as any).queuing as PlaceQueuing || null;
  }

  /**
   * Get setting list as typed object
   */
  get settingListObject(): PlaceSettingList | null {
    return (this as any).settingList as PlaceSettingList || null;
  }

  /**
   * Check if place is custom
   */
  get isCustom(): boolean {
    return (this as any).custom === true;
  }

  /**
   * Check if place is visible
   */
  get isVisible(): boolean {
    return (this as any).visible === true;
  }

  /**
   * Get coordinates
   */
  get coordinates(): { lat: number; lng: number } | null {
    const lat = (this as any).lat || (this as any).geo?.coordinates?.[0];
    const lng = (this as any).lng || (this as any).geo?.coordinates?.[1];
    
    if (lat && lng) {
      return { lat, lng };
    }
    return null;
  }

  /**
   * Get brand name
   */
  get brandName(): string {
    return (this as any).brand?.short || (this as any).brand?.long || '';
  }

  /**
   * Get primary address
   */
  get primaryAddress(): any {
    const addressList = (this as any).addressList || [];
    return addressList.find((addr: any) => addr.type === 'store') || addressList[0] || null;
  }

  /**
   * Get primary phone
   */
  get primaryPhone(): string {
    const phoneList = (this as any).phoneList || [];
    const phone = phoneList[0];
    return phone?.fullNumber || '';
  }
}

/**
 * Place instance class
 */
export class PlaceInstance extends BaseInstance<Place> {
  static model = PlaceModel;

  get placeName(): string {
    return this.instance.name || '';
  }

  get brandName(): string {
    return (this.instance.brand as any)?.short || (this.instance.brand as any)?.long || '';
  }

  get isCustom(): boolean {
    return this.instance.custom === true;
  }

  get isVisible(): boolean {
    return this.instance.visible === true;
  }

  get coordinates(): { lat: number; lng: number } | null {
    const lat = this.instance.lat || (this.instance.geo as any)?.coordinates?.[0];
    const lng = this.instance.lng || (this.instance.geo as any)?.coordinates?.[1];
    
    if (lat && lng) {
      return { lat, lng };
    }
    return null;
  }

  get hasPickup(): boolean {
    const pickup = (this.instance as any).pickup as PlacePickup;
    return pickup?.available === true;
  }

  get hasDelivery(): boolean {
    const deliver = (this.instance as any).deliver as PlaceDeliver;
    return deliver?.available === true;
  }

  get hasDinein(): boolean {
    const dinein = (this.instance as any).dinein as PlaceDinein;
    return dinein?.available === true;
  }

  get hasBooking(): boolean {
    const booking = (this.instance as any).booking as PlaceBooking;
    return booking?.available === true;
  }

  get hasQueuing(): boolean {
    const queuing = (this.instance as any).queuing as PlaceQueuing;
    return queuing?.available === true;
  }

  get primaryAddress(): any {
    const addressList = this.instance.addressList || [];
    return addressList.find((addr: any) => addr.type === 'store') || addressList[0] || null;
  }

  get primaryPhone(): string {
    const phoneList = this.instance.phoneList || [];
    const phone = phoneList[0];
    return phone?.fullNumber || '';
  }

  get logoUrl(): string {
    return (this.instance.brand as any)?.logo?.url || '';
  }
}
