import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance } from './BaseModel';

/**
 * Preference TypeScript interfaces
 */
export interface PreferencePayments {
  [key: string]: any;
}

export interface PreferenceSocial {
  [key: string]: any;
}

export interface PreferenceSettings {
  [key: string]: any;
}

/**
 * Main Preference interface
 */
export interface Preference extends Realm.Object {
  id: string;

  // These remain in injectId (relationship objects)
  reminders: any; // PreferenceReminders
  discover: any;  // PreferenceDiscover
  logs: any;      // PreferenceLogs
  card: any;      // PreferenceCard

  bioAuth?: boolean;
  scan?: boolean;

  // Modernized mixed type properties (was stringified)
  payments?: Realm.Mixed;  // Contains PreferencePayments structure
  social?: Realm.Mixed;    // Contains PreferenceSocial structure
  settings?: Realm.Mixed;  // Contains PreferenceSettings structure

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  _delta?: Realm.Mixed; // Was stringified
  _refresh: boolean;
  _model: string;
}

/**
 * Preference Realm schema definition
 */
export const PreferenceSchema: ObjectSchema = {
  name: 'Preference',
  primaryKey: 'id',
  properties: {
    id: 'string',

    // These remain in injectId (relationship objects)
    reminders: 'PreferenceReminders',
    discover: 'PreferenceDiscover',
    logs: 'PreferenceLogs',
    card: 'PreferenceCard',

    bioAuth: { type: 'bool', optional: true, default: true },
    scan: { type: 'bool', optional: true, default: true },

    // Modernized: Convert from stringified to mixed types
    payments: 'mixed?',  // Was: { type: 'string?', default: '{}' }
    social: 'mixed?',    // Was: { type: 'string?', default: '{}' }
    settings: 'mixed?',  // Was: { type: 'string?', default: '{}' }

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    _delta: 'mixed?',    // Was: { type: 'string?', default: '[]' }
    _refresh: { type: 'bool', default: false },
    _model: { type: 'string', default: 'Preference' },
  }
};

/**
 * Preference model class
 */
export class PreferenceModel extends BaseModel {
  static className = 'Preference';
  static schema = PreferenceSchema;

  // Keep all injectId properties (all are relationship objects)
  static injectId = ['reminders', 'discover', 'logs', 'card'];

  static readOnlyProps = ['_delta', '_refresh'];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['payments', 'social', 'settings', '_delta'];

  /**
   * Get payments as typed object
   */
  get paymentsObject(): PreferencePayments | null {
    return (this as any).payments as PreferencePayments || null;
  }

  /**
   * Get social as typed object
   */
  get socialObject(): PreferenceSocial | null {
    return (this as any).social as PreferenceSocial || null;
  }

  /**
   * Get settings as typed object
   */
  get settingsObject(): PreferenceSettings | null {
    return (this as any).settings as PreferenceSettings || null;
  }

  /**
   * Check if biometric authentication is enabled
   */
  get isBioAuthEnabled(): boolean {
    return (this as any).bioAuth === true;
  }

  /**
   * Check if scanning is enabled
   */
  get isScanEnabled(): boolean {
    return (this as any).scan === true;
  }

  /**
   * Get card view preference
   */
  get cardView(): string {
    return (this as any).card?.view || 'big';
  }

  /**
   * Get card order preference
   */
  get cardOrder(): string {
    return (this as any).card?.order || 'custom';
  }
}

/**
 * Preference instance class
 */
export class PreferenceInstance extends BaseInstance<Preference> {
  static model = PreferenceModel;

  get paymentsData(): PreferencePayments | null {
    return (this.instance as any).payments as PreferencePayments || null;
  }

  get socialData(): PreferenceSocial | null {
    return (this.instance as any).social as PreferenceSocial || null;
  }

  get settingsData(): PreferenceSettings | null {
    return (this.instance as any).settings as PreferenceSettings || null;
  }

  get isBioAuthEnabled(): boolean {
    return this.instance.bioAuth === true;
  }

  get isScanEnabled(): boolean {
    return this.instance.scan === true;
  }

  get cardViewPreference(): string {
    return (this.instance.card as any)?.view || 'big';
  }

  get cardOrderPreference(): string {
    return (this.instance.card as any)?.order || 'custom';
  }

  get hasCardReminders(): boolean {
    return !!(this.instance.reminders as any)?.card;
  }

  get hasOfferReminders(): boolean {
    return !!(this.instance.reminders as any)?.offer;
  }

  get discoverCardsEnabled(): boolean {
    return (this.instance.discover as any)?.cards === true;
  }
}
