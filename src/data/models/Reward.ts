import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance, QueryUtils } from './BaseModel';

/**
 * Reward TypeScript interfaces
 */
export interface RewardQualifiers {
  issuestamps?: {
    totalPrice?: {
      $gte?: number;
    };
  };
  [key: string]: any;
}

/**
 * Main Reward interface
 */
export interface Reward extends Realm.Object {
  id: string;
  masterId: string;
  name?: string;
  brand?: string;

  kind?: string;

  startTime?: Date;
  endTime?: Date;

  // These remain in injectId (relationship objects)
  logoImage: any; // RewardLogo
  levels: any[]; // RewardLevel[]
  options: any; // RewardOptions
  globalize: any; // RewardGlobalize
  when: any; // RewardWhen

  transactions: any[]; // RewardTransaction[]

  channels: string[]; // Where reward stamp can be issued
  state?: string;

  // Modernized mixed type properties (was stringified)
  qualifiers?: Realm.Mixed; // Contains RewardQualifiers structure

  activeTime?: Date;
  purgeTime?: Date;

  visible?: boolean;

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  personId: string;
  cardId?: string;
  merchantId?: string;

  minAppVersion?: string;

  _refresh: boolean;
  _model: string;
}

/**
 * Reward Realm schema definition
 */
export const RewardSchema: ObjectSchema = {
  name: 'Reward',
  primaryKey: 'id',
  properties: {
    id: 'string',
    masterId: 'string',
    name: { type: 'string', optional: true, default: '' },
    brand: { type: 'string', optional: true, default: '' },

    kind: 'string?',

    startTime: 'date?',
    endTime: 'date?',

    // These remain in injectId (relationship objects)
    logoImage: 'RewardLogo',
    levels: { type: 'list', objectType: 'RewardLevel', default: [] },
    options: 'RewardOptions',
    globalize: 'RewardGlobalize',
    when: 'RewardWhen',

    transactions: { type: 'list', objectType: 'RewardTransaction', default: [] },

    channels: { type: 'list', objectType: 'string', optional: true, default: [] }, // Where reward stamp can be issued
    state: { type: 'string', optional: true, default: 'pending' },

    // Modernized: Convert from stringified to mixed type
    qualifiers: 'mixed?', // Was: 'string?' - { issuestamps: { totalPrice: { $gte: 50 } } }

    activeTime: 'date?',
    purgeTime: 'date?',

    visible: { type: 'bool', optional: true, default: true },

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    personId: 'string',
    cardId: { type: 'string', optional: true, indexed: true },
    merchantId: { type: 'string', optional: true, indexed: true },

    minAppVersion: 'string?',

    _refresh: { type: 'bool', default: false },
    _model: { type: 'string', default: 'Reward' },
  }
};

/**
 * Reward model class
 */
export class RewardModel extends BaseModel {
  static className = 'Reward';
  static schema = RewardSchema;

  // Keep all injectId properties (all are relationship objects)
  static injectId = ['logoImage', 'levels', 'options', 'globalize', 'when'];

  static readOnlyProps = ['globalize', '_refresh'];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['qualifiers'];

  /**
   * Get qualifiers as typed object
   */
  get qualifiersObject(): RewardQualifiers | null {
    return (this as any).qualifiers as RewardQualifiers || null;
  }

  /**
   * Get current level index
   */
  get currentLevel(): number {
    const levels = (this as any).levels || [];
    
    for (let i = levels.length - 1; i >= 0; i--) {
      if (levels[i].completedAt !== null || levels[i].stamps[0]?.stamped) {
        return i;
      }
    }
    return 0;
  }

  /**
   * Get reward images from levels
   */
  get images(): string[] {
    const levels = (this as any).levels || [];
    return levels.map((level: any) => {
      const { images, imageNdx } = level;
      return images[imageNdx]?.url || '';
    });
  }

  /**
   * Get current reward image
   */
  get currentImage(): string {
    const images = this.images;
    const currentLevel = this.currentLevel;
    return images[currentLevel] || '';
  }

  /**
   * Check if reward is completed
   */
  get isCompleted(): boolean {
    const levels = (this as any).levels || [];
    return levels.every((level: any) => level.completedAt !== null);
  }

  /**
   * Get reward state
   */
  get rewardState(): string {
    const now = new Date();
    
    if (this.isCompleted) return 'COMPLETED';
    if ((this as any).endTime && new Date((this as any).endTime) < now) return 'EXPIRED';
    if ((this as any).startTime && new Date((this as any).startTime) > now) return 'NOT_STARTED';
    
    return 'ACTIVE';
  }
}

/**
 * Reward instance class
 */
export class RewardInstance extends BaseInstance<Reward> {
  static model = RewardModel;

  get state(): string {
    const now = new Date();
    const { endTime, startTime } = this.instance;
    
    if ((this.instance as any).isCompleted) return 'COMPLETED';
    if (endTime && new Date(endTime) < now) return 'EXPIRED';
    if (startTime && new Date(startTime) > now) return 'NOT_STARTED';
    
    return 'ACTIVE';
  }

  get atLevel(): number {
    const levels = (this.instance as any).levels || [];
    
    for (let i = levels.length - 1; i >= 0; i--) {
      if (levels[i].completedAt !== null || levels[i].stamps[0]?.stamped) {
        return i;
      }
    }
    return 0;
  }

  get images(): string[] {
    const levels = (this.instance as any).levels || [];
    return levels.map((level: any) => {
      const { images, imageNdx } = level;
      return images[imageNdx]?.url || '';
    });
  }

  get image(): string {
    const images = this.images;
    const atLevel = this.atLevel;
    return images[atLevel] || '';
  }

  get brandName(): string {
    return this.instance.brand || this.instance.name || '';
  }

  get isActive(): boolean {
    return this.state === 'ACTIVE';
  }

  get isCompleted(): boolean {
    return this.state === 'COMPLETED';
  }

  get isExpired(): boolean {
    return this.state === 'EXPIRED';
  }
}
