import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance } from './BaseModel';

/**
 * Settings TypeScript interfaces
 */
export interface SettingsData {
  [key: string]: any;
}

/**
 * Main Settings interface
 */
export interface Settings extends Realm.Object {
  id: string;
  kind: string;

  // Modernized mixed type properties (was stringified)
  data?: Realm.Mixed; // Contains SettingsData structure

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  _refresh: boolean;
  _model: string;
}

/**
 * Settings Realm schema definition
 */
export const SettingsSchema: ObjectSchema = {
  name: 'Settings',
  primaryKey: 'id',
  properties: {
    id: 'string',
    kind: 'string',

    // Modernized: Convert from stringified to mixed type
    data: 'mixed?', // Was: { type: 'string?', default: '{}' }

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    _refresh: { type: 'bool', default: false },
    _model: { type: 'string', default: 'Settings' },
  }
};

/**
 * Settings model class
 */
export class SettingsModel extends BaseModel {
  static className = 'Settings';
  static schema = SettingsSchema;

  // No injectId properties
  static injectId: string[] = [];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['data'];

  /**
   * Get data as typed object
   */
  get dataObject(): SettingsData | null {
    return (this as any).data as SettingsData || null;
  }
}

/**
 * Settings instance class
 */
export class SettingsInstance extends BaseInstance<Settings> {
  static model = SettingsModel;

  get settingsData(): SettingsData | null {
    return (this.instance as any).data as SettingsData || null;
  }

  get isCredentials(): boolean {
    return this.instance.kind === 'Credentials';
  }
}
