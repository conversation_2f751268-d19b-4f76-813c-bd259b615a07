import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance } from './BaseModel';

/**
 * Watchdog TypeScript interfaces
 */
export interface WatchdogError {
  message?: string;
  stack?: string;
  code?: string | number;
  name?: string;
  [key: string]: any;
}

export interface WatchdogData {
  [key: string]: any;
}

/**
 * Main Watchdog interface
 */
export interface Watchdog extends Realm.Object {
  id: string;
  name: string;
  level?: string; // error, warn, info, debug
  message?: string;

  // Modernized mixed type properties (was stringified)
  error?: Realm.Mixed; // Contains WatchdogError structure
  data?: Realm.Mixed;  // Contains WatchdogData structure

  personId?: string;
  cardId?: string;
  masterId?: string;
  offerId?: string;
  rewardId?: string;
  messageId?: string;
  placeId?: string;

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  _model: string;
}

/**
 * Watchdog Realm schema definition
 */
export const WatchdogSchema: ObjectSchema = {
  name: 'Watchdog',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'string',
    level: { type: 'string', optional: true, default: 'info' }, // error, warn, info, debug
    message: 'string?',

    // Modernized: Convert from stringified to mixed types
    error: 'mixed?', // Was stringified
    data: 'mixed?',  // Was stringified

    personId: 'string?',
    cardId: 'string?',
    masterId: 'string?',
    offerId: 'string?',
    rewardId: 'string?',
    messageId: 'string?',
    placeId: 'string?',

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    _model: { type: 'string', default: 'Watchdog' },
  }
};

/**
 * Watchdog model class
 */
export class WatchdogModel extends BaseModel {
  static className = 'Watchdog';
  static schema = WatchdogSchema;

  // No injectId properties
  static injectId: string[] = [];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['error', 'data'];

  /**
   * Get error as typed object
   */
  get errorObject(): WatchdogError | null {
    return (this as any).error as WatchdogError || null;
  }

  /**
   * Get data as typed object
   */
  get dataObject(): WatchdogData | null {
    return (this as any).data as WatchdogData || null;
  }

  /**
   * Check if this is an error level log
   */
  get isError(): boolean {
    return (this as any).level === 'error';
  }

  /**
   * Check if this is a warning level log
   */
  get isWarning(): boolean {
    return (this as any).level === 'warn';
  }

  /**
   * Get error message
   */
  get errorMessage(): string {
    const error = this.errorObject;
    return error?.message || (this as any).message || '';
  }
}

/**
 * Watchdog instance class
 */
export class WatchdogInstance extends BaseInstance<Watchdog> {
  static model = WatchdogModel;

  get errorData(): WatchdogError | null {
    return (this.instance as any).error as WatchdogError || null;
  }

  get logData(): WatchdogData | null {
    return (this.instance as any).data as WatchdogData || null;
  }

  get isError(): boolean {
    return this.instance.level === 'error';
  }

  get isWarning(): boolean {
    return this.instance.level === 'warn';
  }

  get isInfo(): boolean {
    return this.instance.level === 'info';
  }

  get isDebug(): boolean {
    return this.instance.level === 'debug';
  }

  get errorMessage(): string {
    const error = this.errorData;
    return error?.message || this.instance.message || '';
  }

  get errorStack(): string {
    const error = this.errorData;
    return error?.stack || '';
  }

  get severity(): 'high' | 'medium' | 'low' {
    switch (this.instance.level) {
      case 'error': return 'high';
      case 'warn': return 'medium';
      default: return 'low';
    }
  }
}
