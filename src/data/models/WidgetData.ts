import Realm, { ObjectSchema } from 'realm';
import { BaseModel, BaseInstance } from './BaseModel';

/**
 * WidgetData TypeScript interfaces
 */
export interface WidgetDataData {
  [key: string]: any;
}

export interface WidgetDataTenant {
  [key: string]: any;
}

/**
 * Main WidgetData interface
 */
export interface WidgetData extends Realm.Object {
  id: string; // Server ID, can be null for locally created data
  compositeKey?: string; // cardId + "_" + key
  key: string;

  // Modernized mixed type properties (was stringified)
  data?: Realm.Mixed;   // Contains WidgetDataData structure
  tenant?: Realm.Mixed; // Contains WidgetDataTenant structure

  // These remain in injectId (relationship objects)
  badge: any; // WidgetDataBadge
  when: any;  // WidgetDataWhen

  merge?: string; // 'shallow' | 'deep'
  value?: string;
  visible?: boolean;
  local?: boolean;

  createdAt: Date;
  modifiedAt?: Date;
  deletedAt?: Date;

  personId: string;
  cardId: string;
  cardMasterId?: string;

  _delta?: Realm.Mixed; // Was stringified
  _refresh: boolean;
  _model: string;
}

/**
 * WidgetData Realm schema definition
 */
export const WidgetDataSchema: ObjectSchema = {
  name: 'WidgetData',
  primaryKey: 'id',
  properties: {
    id: 'string', // Server ID, can be null for locally created data
    compositeKey: { type: 'string', optional: true, default: null }, // cardId + "_" + key
    key: { type: 'string', indexed: true },

    // Modernized: Convert from stringified to mixed types
    data: 'mixed?',   // Was: { type: 'string?', default: '{}' }
    tenant: 'mixed?', // Was: { type: 'string?', default: '{}' }

    // These remain in injectId (relationship objects)
    badge: 'WidgetDataBadge',
    when: 'WidgetDataWhen',

    merge: { type: 'string', optional: true, default: 'shallow' },
    value: { type: 'string', optional: true, default: '' },
    visible: { type: 'bool', optional: true, default: true },
    local: { type: 'bool', optional: true, default: false },

    createdAt: 'date',
    modifiedAt: 'date?',
    deletedAt: 'date?',

    personId: 'string',
    cardId: { type: 'string', indexed: true },
    cardMasterId: { type: 'string', optional: true, indexed: true },

    _delta: 'mixed?', // Was: { type: 'string?', default: '[]' }
    _refresh: { type: 'bool', default: false },
    _model: { type: 'string', default: 'WidgetData' },
  }
};

/**
 * WidgetData model class
 */
export class WidgetDataModel extends BaseModel {
  static className = 'WidgetData';
  static schema = WidgetDataSchema;

  // Keep all injectId properties (all are relationship objects)
  static injectId = ['badge', 'when'];

  static readOnlyProps = ['_delta', '_refresh'];

  // Modernized: Removed stringifyObjects - no longer needed!
  // static stringifyObjects = ['data', 'tenant', '_delta'];

  /**
   * Get data as typed object
   */
  get dataObject(): WidgetDataData | null {
    return (this as any).data as WidgetDataData || null;
  }

  /**
   * Get tenant as typed object
   */
  get tenantObject(): WidgetDataTenant | null {
    return (this as any).tenant as WidgetDataTenant || null;
  }

  /**
   * Check if widget data is visible
   */
  get isVisible(): boolean {
    return (this as any).visible === true;
  }

  /**
   * Check if widget data is local only
   */
  get isLocal(): boolean {
    return (this as any).local === true;
  }

  /**
   * Get unread badge count
   */
  get unreadCount(): number {
    return (this as any).badge?.unread || 0;
  }

  /**
   * Get valid badge count
   */
  get validCount(): number {
    return (this as any).badge?.valid || 0;
  }

  /**
   * Check if has unread items
   */
  get hasUnread(): boolean {
    return this.unreadCount > 0;
  }

  /**
   * Check if has valid items
   */
  get hasValid(): boolean {
    return this.validCount > 0;
  }
}

/**
 * WidgetData instance class
 */
export class WidgetDataInstance extends BaseInstance<WidgetData> {
  static model = WidgetDataModel;

  get widgetData(): WidgetDataData | null {
    return (this.instance as any).data as WidgetDataData || null;
  }

  get tenantData(): WidgetDataTenant | null {
    return (this.instance as any).tenant as WidgetDataTenant || null;
  }

  get isVisible(): boolean {
    return this.instance.visible === true;
  }

  get isLocal(): boolean {
    return this.instance.local === true;
  }

  get unreadCount(): number {
    return (this.instance.badge as any)?.unread || 0;
  }

  get validCount(): number {
    return (this.instance.badge as any)?.valid || 0;
  }

  get hasUnread(): boolean {
    return this.unreadCount > 0;
  }

  get hasValid(): boolean {
    return this.validCount > 0;
  }

  get widgetKey(): string {
    return this.instance.key;
  }

  get compositeKey(): string {
    return this.instance.compositeKey || `${this.instance.cardId}_${this.instance.key}`;
  }
}
