import { ObjectSchema } from 'realm';

/**
 * CardMasterBrand embedded schema
 * Converted from injectId to embedded object as per modernization guide
 */
export const CardMasterBrandSchema: ObjectSchema = {
  name: 'CardMasterBrand',
  embedded: true, // This is now an embedded object, not a separate entity
  properties: {
    // Note: No 'id' property for embedded objects
    short: { type: 'string', optional: true, default: '', indexed: true },
    long: { type: 'string', optional: true, default: '', indexed: true },
    color: 'string?',
    style: 'string?',
    logos: { type: 'list', objectType: 'CardMasterLogo', default: [] }, // This will still reference the separate entity
  }
};

/**
 * TypeScript interface for CardMasterBrand
 */
export interface CardMasterBrand {
  short?: string;
  long?: string;
  color?: string;
  style?: string;
  logos: any[]; // CardMasterLogo[]
}
