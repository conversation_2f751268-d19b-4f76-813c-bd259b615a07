import { ObjectSchema } from 'realm';

/**
 * CardMasterDiscover embedded schema
 * Converted from injectId to embedded object as per modernization guide
 */
export const CardMasterDiscoverSchema: ObjectSchema = {
  name: 'CardMasterDiscover',
  embedded: true,
  properties: {
    enabled: { type: 'bool', default: true }, // List in Card Library
    regions: { type: 'list', objectType: 'CardMasterRegion', default: [] }, // This will still reference the separate entity
    applet: 'Applet', // This will still reference the separate entity
  }
};

/**
 * TypeScript interface for CardMasterDiscover
 */
export interface CardMasterDiscover {
  enabled: boolean;
  regions: any[]; // CardMasterRegion[]
  applet: any; // Applet
}
