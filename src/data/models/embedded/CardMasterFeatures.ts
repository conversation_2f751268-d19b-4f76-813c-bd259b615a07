import { ObjectSchema } from 'realm';

/**
 * CardMasterFeatures embedded schema
 * Converted from injectId to embedded object as per modernization guide
 */
export const CardMasterFeaturesSchema: ObjectSchema = {
  name: 'CardMasterFeatures',
  embedded: true,
  properties: {
    managed: 'bool?', // Is managed by CRM
    BLE: 'bool?', // Enable BLE card recognition
    pay2Join: 'bool?', // Enable payment before registration
    storedValue: 'bool?', // Enable Stored Value
    photo: 'bool?', // Enable profile photo on card
  }
};

/**
 * TypeScript interface for CardMasterFeatures
 */
export interface CardMasterFeatures {
  managed?: boolean;
  BLE?: boolean;
  pay2Join?: boolean;
  storedValue?: boolean;
  photo?: boolean;
}
