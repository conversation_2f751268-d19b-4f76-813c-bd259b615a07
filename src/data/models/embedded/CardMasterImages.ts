import { ObjectSchema } from 'realm';

/**
 * CardMasterImages embedded schema
 * Converted from injectId to embedded object as per modernization guide
 * Note: This represents the 'images' property that was in CardMaster's injectId
 */
export const CardMasterImagesSchema: ObjectSchema = {
  name: 'CardMasterImages',
  embedded: true,
  properties: {
    front: { type: 'string', optional: true, default: '' },
    thumbnail: { type: 'string', optional: true, default: '' },
    irregularShape: { type: 'bool', optional: true, default: false },
    width: 'int?',
    height: 'int?',
    pHash: 'string?',
    transparency: { type: 'bool', optional: true, default: false },
  }
};

/**
 * TypeScript interface for CardMasterImages
 */
export interface CardMasterImages {
  front?: string;
  thumbnail?: string;
  irregularShape?: boolean;
  width?: number;
  height?: number;
  pHash?: string;
  transparency?: boolean;
}
