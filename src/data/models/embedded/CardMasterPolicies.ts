import { ObjectSchema } from 'realm';

/**
 * CardMasterPolicies embedded schema
 * Converted from injectId to embedded object as per modernization guide
 */
export const CardMasterPoliciesSchema: ObjectSchema = {
  name: 'CardMasterPolicies',
  embedded: true,
  properties: {
    qualifiers: 'mixed?', // Was stringified: { join: {}, renew: { before: 'P30D', after: 'P14D' }}
    perUser: { type: 'int', default: 1 },
    perOrder: { type: 'int', default: 1 },
  }
};

/**
 * TypeScript interface for CardMasterPolicies
 */
export interface CardMasterPolicies {
  qualifiers?: any; // Dynamic object structure
  perUser: number;
  perOrder: number;
}
