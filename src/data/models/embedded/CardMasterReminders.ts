import { ObjectSchema } from 'realm';

/**
 * CardMasterReminders embedded schema
 * Converted from injectId to embedded object as per modernization guide
 */
export const CardMasterRemindersSchema: ObjectSchema = {
  name: 'CardMasterReminders',
  embedded: true,
  properties: {
    registration: 'int?', // (in days)
    expiry: 'int?', // (in days)
    location: 'int?', // (in days)
  }
};

/**
 * TypeScript interface for CardMasterReminders
 */
export interface CardMasterReminders {
  registration?: number;
  expiry?: number;
  location?: number;
}
