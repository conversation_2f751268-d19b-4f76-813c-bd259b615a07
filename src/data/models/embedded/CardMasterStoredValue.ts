import { ObjectSchema } from 'realm';

/**
 * CardMasterStoredValue embedded schema
 * Converted from injectId to embedded object as per modernization guide
 */
export const CardMasterStoredValueSchema: ObjectSchema = {
  name: 'CardMasterStoredValue',
  embedded: true,
  properties: {
    kind: { type: 'string', default: 'giftcard' },
    name: { type: 'string', optional: true, default: '', indexed: true },
    enabled: { type: 'bool', default: true },
    currency: 'string?',
    singleUse: { type: 'bool', default: false },
    sharedOnly: { type: 'bool', default: false },
    style: 'CardMasterStoredValueBalance', // This will still reference the separate entity
  }
};

/**
 * TypeScript interface for CardMasterStoredValue
 */
export interface CardMasterStoredValue {
  kind: string;
  name?: string;
  enabled: boolean;
  currency?: string;
  singleUse: boolean;
  sharedOnly: boolean;
  style: any; // CardMasterStoredValueBalance
}
