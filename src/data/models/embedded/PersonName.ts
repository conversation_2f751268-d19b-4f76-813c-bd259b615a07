import { ObjectSchema } from 'realm';

/**
 * PersonName embedded schema
 * Converted from injectId to embedded object as per modernization guide
 */
export const PersonNameSchema: ObjectSchema = {
  name: 'PersonName',
  embedded: true, // This is now an embedded object, not a separate entity
  properties: {
    // Note: No 'id' property for embedded objects
    displayAs: 'string?', // { "enum": ["familygiven", "givenfamily", "given", "family", "alias", "aliasfamily", "familygivenalias", "givenfamilyalias", "aliasfamilygiven"] }
    order: 'string?',     // { "enum": ["familygiven", "givenfamily"] }
    language: 'string?',  // based on Apple Language ID (see ISO 639-1, 639-2, ISO 15924)
    salutation: 'string?', // { "enum": ["mr", "mrs", "ms", "mdm"] }
  }
};

/**
 * TypeScript interface for PersonName
 */
export interface PersonName {
  displayAs?: string;
  order?: string;
  language?: string;
  salutation?: string;
}
