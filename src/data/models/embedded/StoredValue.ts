import { ObjectSchema } from 'realm';

/**
 * StoredValue embedded schema
 * Converted from injectId to embedded object as per modernization guide
 */
export const StoredValueSchema: ObjectSchema = {
  name: 'StoredValue',
  embedded: true, // This is now an embedded object, not a separate entity
  properties: {
    // Note: No 'id' property for embedded objects
    balance: 'double?',
    currency: 'string?', // { "code": "SGD", "precision": 2 (ie. 2 => amount of 1000 = $10.00) }
  }
};

/**
 * TypeScript interface for StoredValue
 */
export interface StoredValue {
  balance?: number;
  currency?: string;
}
