/**
 * Central export file for all models
 * This file will be updated as we add new TypeScript models
 */

// Base classes
export { BaseModel, BaseInstance, QueryUtils } from './BaseModel';

// Core Models (Phase 1 - Completed)
export { CardModel, CardSchema, CardInstance } from './Card';
export { CardMasterModel, CardMasterSchema, CardMasterInstance } from './CardMaster';
export { PersonModel, PersonSchema, PersonInstance } from './Person';

// Content Models (Phase 2 - Completed)
export { OfferModel, OfferSchema, OfferInstance } from './Offer';
export { MessageModel, MessageSchema, MessageInstance } from './Message';
export { RewardModel, RewardSchema, RewardInstance } from './Reward';

// System Models (Phase 3 - Completed)
export { SettingsModel, SettingsSchema, SettingsInstance } from './Settings';
export { ActionModel, ActionSchema, ActionInstance } from './Action';
export { AppEventModel, AppEventSchema, AppEventInstance } from './AppEvent';
export { WatchdogModel, WatchdogSchema, WatchdogInstance } from './Watchdog';
export { PlaceModel, PlaceSchema, PlaceInstance } from './Place';
export { PreferenceModel, PreferenceSchema, PreferenceInstance } from './Preference';
export { WidgetDataModel, WidgetDataSchema, WidgetDataInstance } from './WidgetData';
export { MerchantModel, MerchantSchema, MerchantInstance } from './Merchant';

// Embedded Schemas (Phase 1 - Completed)
export { StoredValueSchema } from './embedded/StoredValue';
export { PersonNameSchema } from './embedded/PersonName';
export { CardMasterBrandSchema } from './embedded/CardMasterBrand';
export { CardMasterFeaturesSchema } from './embedded/CardMasterFeatures';
export { CardMasterPoliciesSchema } from './embedded/CardMasterPolicies';
export { CardMasterDiscoverSchema } from './embedded/CardMasterDiscover';
export { CardMasterRemindersSchema } from './embedded/CardMasterReminders';
export { CardMasterStoredValueSchema } from './embedded/CardMasterStoredValue';
export { CardMasterImagesSchema } from './embedded/CardMasterImages';

/**
 * Type definitions for common interfaces
 */
export interface ModelFormData {
  [key: string]: any;
}

export interface ModelOptions {
  [key: string]: any;
}

export interface ModelFlow {
  [key: string]: any;
}

export interface ModelCustom {
  [key: string]: any;
}

/**
 * Common model properties interface
 */
export interface BaseModelProperties {
  id: string;
  createdAt?: Date;
  modifiedAt?: Date;
  deletedAt?: Date;
  _refresh?: boolean;
  _model?: string;
}
