import { ObjectSchema } from 'realm';

/**
 * Card ecosystem relationship schemas
 * These are complex objects that remain as separate entities (not embedded)
 * Based on v6 definitions and migration guide rules
 */

// =============================================================================
// CARD LIFECYCLE & SHARING
// =============================================================================

export const CardSharerSchema: ObjectSchema = {
  name: 'CardSharer',
  primaryKey: 'id',
  properties: {
    id: 'string',
    personId: 'string?',
    name: 'string?',
    imageUrl: 'string?',
    mode: 'string?',
    originId: 'string?',
    sharingId: 'string?',
    generation: 'int?',
  }
};

export const CardWhenSchema: ObjectSchema = {
  name: '<PERSON><PERSON>hen',
  primaryKey: 'id',
  properties: {
    id: 'string',
    issued: { type: 'date', optional: true, default: null },
    notified: { type: 'date', optional: true, default: null },
    declined: { type: 'date', optional: true, default: null },
    accepted: { type: 'date', optional: true, default: null },
    paid: { type: 'date', optional: true, default: null },
    registered: { type: 'date', optional: true, default: null },
    activated: { type: 'date', optional: true, default: null },
    transferred: { type: 'date', optional: true, default: null },
    revoked: { type: 'date', optional: true, default: null },
    cancelled: { type: 'date', optional: true, default: null },
    terminated: { type: 'date', optional: true, default: null },
    updated: { type: 'date', optional: true, default: null },
    received: { type: 'date', optional: true, default: null },
  }
};

export const SharingSchema: ObjectSchema = {
  name: 'Sharing',
  primaryKey: 'id',
  properties: {
    id: 'string',
    mode: 'string?',
    recipientId: 'string?',
    recipientName: 'string?',
    recipientEmail: 'string?',
    recipientPhone: 'string?',
    message: 'string?',
    status: 'string?',
    createdAt: 'date?',
    sentAt: 'date?',
    viewedAt: 'date?',
    acceptedAt: 'date?',
    when: 'SharingWhen?',
    recipient: 'SharingRecipient?',
  }
};

export const SharingWhenSchema: ObjectSchema = {
  name: 'SharingWhen',
  primaryKey: 'id',
  properties: {
    id: 'string',
    created: 'date?',
    sent: 'date?',
    viewed: 'date?',
    accepted: 'date?',
    declined: 'date?',
    expired: 'date?',
  }
};

export const SharingRecipientSchema: ObjectSchema = {
  name: 'SharingRecipient',
  primaryKey: 'id',
  properties: {
    id: 'string',
    personId: 'string?',
    name: 'string?',
    email: 'string?',
    phone: 'string?',
    imageUrl: 'string?',
  }
};

// =============================================================================
// CARD UI & CUSTOMIZATION
// =============================================================================

export const CardWidgetSchema: ObjectSchema = {
  name: 'CardWidget',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'string?',
    type: 'string?',
    position: 'int?',
    visible: { type: 'bool', optional: true, default: true },
    config: 'mixed?', // Widget configuration data
  }
};

export const CustomImageSchema: ObjectSchema = {
  name: 'CustomImage',
  primaryKey: 'id',
  properties: {
    id: 'string',
    original: 'string?',
    thumbnail: 'string?',
    width: 'int?',
    height: 'int?',
    format: 'string?',
    size: 'int?',
  }
};
