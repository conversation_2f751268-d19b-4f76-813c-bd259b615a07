import { ObjectSchema } from 'realm';

/**
 * CardMaster ecosystem relationship schemas
 * These are complex objects that remain as separate entities (not embedded)
 * Based on v6 definitions and migration guide rules
 */

// =============================================================================
// CARDMASTER CONFIGURATION & METADATA
// =============================================================================

export const CardMasterTagSchema: ObjectSchema = {
  name: 'CardMasterTag',
  primaryKey: 'id',
  properties: {
    id: 'string',
    categories: { type: 'list', objectType: 'string', default: [] },
    brands: { type: 'list', objectType: 'string', default: [] },
    keywords: { type: 'list', objectType: 'string', default: [] },
    industries: { type: 'list', objectType: 'string', default: [] },
  }
};

export const CardMasterOptionsSchema: ObjectSchema = {
  name: 'CardMasterOptions',
  primaryKey: 'id',
  properties: {
    id: 'string',
    toAccept: { type: 'bool', optional: true, default: false }, // Require user to accept card in app
    useCardName: { type: 'bool', optional: true, default: false }, // Show Card.formData.cardName/Master.name in push/reminder
    noAutoFlip: { type: 'bool', optional: true, default: false }, // Do not auto flip card in app
    noCustomPlace: { type: 'bool', optional: true, default: false }, // Disable add custom place to card in app
    noDefaultPlaceList: { type: 'bool', optional: true, default: false }, // Do not set a default place list in app
    noCardNumber: { type: 'bool', optional: true, default: false }, // Do not show card number in app
    noBarcode: { type: 'bool', optional: true, default: false }, // Do not show barcode in app
    noShare: { type: 'bool', optional: true, default: false }, // Disable sharing in app
    noDelete: { type: 'bool', optional: true, default: false }, // Disable delete in app
    noEdit: { type: 'bool', optional: true, default: false }, // Disable edit in app
    noTransfer: { type: 'bool', optional: true, default: false }, // Disable transfer in app
    noRename: { type: 'bool', optional: true, default: false }, // Disable rename in app
    noReorder: { type: 'bool', optional: true, default: false }, // Disable reorder in app
    noArchive: { type: 'bool', optional: true, default: false }, // Disable archive in app
  }
};

export const CardMasterGlobalizeSchema: ObjectSchema = {
  name: 'CardMasterGlobalize',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'mixed?', // Localized names
    description: 'mixed?', // Localized descriptions
    forms: 'mixed?', // Localized forms
    widgets: 'mixed?', // Localized widgets
    policies: 'mixed?', // Localized policies
    reminders: 'mixed?', // Localized reminders
  }
};

// =============================================================================
// CARDMASTER WIDGETS & UI
// =============================================================================

export const WidgetSchema: ObjectSchema = {
  name: 'Widget',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'string?',
    type: 'string?',
    position: 'int?',
    visible: { type: 'bool', optional: true, default: true },
    required: { type: 'bool', optional: true, default: false },
    config: 'mixed?', // Widget configuration
    globalize: 'WidgetGlobalize?',
  }
};

export const WidgetGlobalizeSchema: ObjectSchema = {
  name: 'WidgetGlobalize',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'mixed?', // Localized names
    config: 'mixed?', // Localized configuration
  }
};
