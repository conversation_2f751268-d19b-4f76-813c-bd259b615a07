import { ObjectSchema } from 'realm';

/**
 * Offer ecosystem relationship schemas
 * These are complex objects that remain as separate entities (not embedded)
 * Based on v6 definitions and migration guide rules
 */

// =============================================================================
// OFFER CORE COMPONENTS
// =============================================================================

export const OfferCodeSchema: ObjectSchema = {
  name: 'OfferCode',
  primaryKey: 'id',
  properties: {
    id: 'string',
    code: 'string?',
    type: 'string?', // barcode, qr, text, etc.
    format: 'string?',
    value: 'string?',
  }
};

export const OfferImageSchema: ObjectSchema = {
  name: 'OfferImage',
  primaryKey: 'id',
  properties: {
    id: 'string',
    original: 'string?',
    thumbnail: 'string?',
    banner: 'string?',
    width: 'int?',
    height: 'int?',
    format: 'string?',
    size: 'int?',
  }
};

export const OfferDiscountSchema: ObjectSchema = {
  name: 'OfferDiscount',
  primaryKey: 'id',
  properties: {
    id: 'string',
    type: 'string?', // percentage, fixed, bogo, etc.
    value: 'double?',
    currency: 'string?',
    minPurchase: 'double?',
    maxDiscount: 'double?',
    conditions: 'mixed?',
  }
};

export const RedemptionSchema: ObjectSchema = {
  name: 'Redemption',
  primaryKey: 'id',
  properties: {
    id: 'string',
    offerId: 'string?',
    personId: 'string?',
    cardId: 'string?',
    redeemedAt: 'date?',
    amount: 'double?',
    currency: 'string?',
    location: 'string?',
    status: 'string?', // pending, completed, failed, cancelled
  }
};

// =============================================================================
// OFFER SHARING & LIFECYCLE
// =============================================================================

export const OfferSharerSchema: ObjectSchema = {
  name: 'OfferSharer',
  primaryKey: 'id',
  properties: {
    id: 'string',
    personId: 'string?',
    name: 'string?',
    imageUrl: 'string?',
    mode: 'string?',
    originId: 'string?',
    sharingId: 'string?',
    generation: 'int?',
  }
};

export const OfferGlobalizeSchema: ObjectSchema = {
  name: 'OfferGlobalize',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'mixed?', // Localized names
    description: 'mixed?', // Localized descriptions
    terms: 'mixed?', // Localized terms and conditions
    images: 'mixed?', // Localized images
  }
};

export const OfferWhenSchema: ObjectSchema = {
  name: 'OfferWhen',
  primaryKey: 'id',
  properties: {
    id: 'string',
    created: 'date?',
    published: 'date?',
    activated: 'date?',
    expires: 'date?',
    redeemed: 'date?',
    cancelled: 'date?',
    viewed: 'date?',
    shared: 'date?',
  }
};

// =============================================================================
// OFFER INTEGRATION & APPS
// =============================================================================

export const AppletWebviewSchema: ObjectSchema = {
  name: 'AppletWebview',
  primaryKey: 'id',
  properties: {
    id: 'string',
    url: 'string?',
    title: 'string?',
    width: 'int?',
    height: 'int?',
    fullscreen: { type: 'bool', optional: true, default: false },
    config: 'mixed?', // Webview configuration
  }
};
