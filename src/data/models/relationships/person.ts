import { ObjectSchema } from 'realm';

/**
 * Person ecosystem relationship schemas
 * These are complex objects that remain as separate entities (not embedded)
 * Based on v6 definitions and migration guide rules
 */

// =============================================================================
// PERSON CONTACT INFORMATION
// =============================================================================

export const PersonDateSchema: ObjectSchema = {
  name: 'PersonDate',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'string', // birth, wedding, graduation
    year: 'int?',
    month: 'int?',
    day: 'int?',
  }
};

export const PersonPhoneSchema: ObjectSchema = {
  name: 'PersonPhone',
  primaryKey: 'id',
  properties: {
    id: 'string',
    fullNumber: 'string',	// 6590677991
    type: 'string', 		// mobile, home, work, fax, others
    countryCode: 'string?',	// 65
    number: 'string?', 		// 90677991
    valid: 'bool?',
  }
};

export const PersonEmailSchema: ObjectSchema = {
  name: 'PersonEmail',
  primaryKey: 'id',
  properties: {
    id: 'string',
    address: 'string',
    type: 'string', // work, home, others
    valid: 'bool?',
  }
};

export const PersonAddressSchema: ObjectSchema = {
  name: 'PersonAddress',
  primaryKey: 'id',
  properties: {
    id: 'string',
    type: 'string', // work, home, others
    label: 'string?',
    house: 'string?',
    level: 'string?',
    unit: 'string?',
    street: 'string?',
    city: 'string?',
    state: 'string?',
    postCode: 'string?',
    country: 'string?', // ISO 3166-1 alpha-2, e.g. SG
    formatted: 'string?',
    short: 'string?',
    geo: 'PersonAddressGeometry?',
    optIn: 'bool?',
    valid: 'bool?',
  }
};

export const PersonAddressGeometrySchema: ObjectSchema = {
  name: 'PersonAddressGeometry',
  primaryKey: 'id',
  properties: {
    id: 'string',
    type: { type: 'string', default: 'Point' },
    coordinates: { type: 'list', objectType: 'double', default: [] }, // [lat, long]
  }
};

// =============================================================================
// PERSON PREFERENCES & PROFILE
// =============================================================================

export const ProfileImageSchema: ObjectSchema = {
  name: 'ProfileImage',
  primaryKey: 'id',
  properties: {
    id: 'string',
    original: 'string?',
    thumbnail: 'string?',
    width: 'int?',
    height: 'int?',
    format: 'string?',
    size: 'int?',
  }
};

export const PersonBrandsSchema: ObjectSchema = {
  name: 'PersonBrands',
  primaryKey: 'id',
  properties: {
    id: 'string',
    favorites: { type: 'list', objectType: 'string', default: [] },
    nointerest: { type: 'list', objectType: 'string', default: [] },
    recommend: { type: 'list', objectType: 'string', default: [] },
  }
};

export const PersonProductsSchema: ObjectSchema = {
  name: 'PersonProducts',
  primaryKey: 'id',
  properties: {
    id: 'string',
    favorites: { type: 'list', objectType: 'PersonProduct', default: [] },
    viewed: { type: 'list', objectType: 'PersonProduct', default: [] },
  }
};

export const PersonProductSchema: ObjectSchema = {
  name: 'PersonProduct',
  primaryKey: 'id',
  properties: {
    id: 'string',
    brand: 'string?',
    gtin: 'string?',
    image: 'string?',
    at: 'date?',
  }
};
