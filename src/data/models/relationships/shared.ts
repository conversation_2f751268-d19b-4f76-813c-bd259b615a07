import { ObjectSchema } from 'realm';

/**
 * Shared relationship schemas
 * Cross-domain objects used by multiple entities
 * Based on v6 definitions and migration guide rules
 */

// =============================================================================
// WIDGET DATA & ANALYTICS
// =============================================================================

export const WidgetDataBadgeSchema: ObjectSchema = {
  name: 'WidgetDataBadge',
  primaryKey: 'id',
  properties: {
    id: 'string',
    count: 'int?',
    text: 'string?',
    color: 'string?',
    visible: { type: 'bool', optional: true, default: true },
  }
};

export const WidgetDataWhenSchema: ObjectSchema = {
  name: 'WidgetDataWhen',
  primaryKey: 'id',
  properties: {
    id: 'string',
    created: 'date?',
    updated: 'date?',
    viewed: 'date?',
    clicked: 'date?',
  }
};

// =============================================================================
// MESSAGING & COMMUNICATION
// =============================================================================

export const MessageSenderSchema: ObjectSchema = {
  name: 'MessageSender',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'string?',
    email: 'string?',
    imageUrl: 'string?',
    type: 'string?', // system, merchant, admin, user
  }
};

export const MessageCoverWebviewSchema: ObjectSchema = {
  name: 'MessageCoverWebview',
  primaryKey: 'id',
  properties: {
    id: 'string',
    url: 'string?',
    title: 'string?',
    width: 'int?',
    height: 'int?',
    config: 'mixed?',
  }
};

export const MessageBodyWebviewSchema: ObjectSchema = {
  name: 'MessageBodyWebview',
  primaryKey: 'id',
  properties: {
    id: 'string',
    url: 'string?',
    content: 'string?',
    width: 'int?',
    height: 'int?',
    config: 'mixed?',
  }
};

export const MessageGlobalizeSchema: ObjectSchema = {
  name: 'MessageGlobalize',
  primaryKey: 'id',
  properties: {
    id: 'string',
    title: 'mixed?', // Localized titles
    body: 'mixed?', // Localized body content
    actions: 'mixed?', // Localized action buttons
  }
};

export const MessageWhenSchema: ObjectSchema = {
  name: 'MessageWhen',
  primaryKey: 'id',
  properties: {
    id: 'string',
    created: 'date?',
    sent: 'date?',
    delivered: 'date?',
    read: 'date?',
    clicked: 'date?',
    archived: 'date?',
    deleted: 'date?',
  }
};

// =============================================================================
// REWARDS & LOYALTY
// =============================================================================

export const RewardLogoSchema: ObjectSchema = {
  name: 'RewardLogo',
  primaryKey: 'id',
  properties: {
    id: 'string',
    standard: 'string?',
    thumbnail: 'string?',
    banner: 'string?',
    width: 'int?',
    height: 'int?',
  }
};

export const RewardLevelSchema: ObjectSchema = {
  name: 'RewardLevel',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'string?',
    threshold: 'double?',
    benefits: 'mixed?', // Level benefits configuration
    color: 'string?',
    icon: 'string?',
  }
};

export const RewardOptionsSchema: ObjectSchema = {
  name: 'RewardOptions',
  primaryKey: 'id',
  properties: {
    id: 'string',
    autoRedeem: { type: 'bool', optional: true, default: false },
    notifications: { type: 'bool', optional: true, default: true },
    sharing: { type: 'bool', optional: true, default: true },
    expiration: 'int?', // Days until expiration
    config: 'mixed?',
  }
};

export const RewardGlobalizeSchema: ObjectSchema = {
  name: 'RewardGlobalize',
  primaryKey: 'id',
  properties: {
    id: 'string',
    name: 'mixed?', // Localized names
    description: 'mixed?', // Localized descriptions
    levels: 'mixed?', // Localized level names
    terms: 'mixed?', // Localized terms
  }
};

export const RewardWhenSchema: ObjectSchema = {
  name: 'RewardWhen',
  primaryKey: 'id',
  properties: {
    id: 'string',
    earned: 'date?',
    redeemed: 'date?',
    expires: 'date?',
    notified: 'date?',
    viewed: 'date?',
  }
};

export const RewardTransactionSchema: ObjectSchema = {
  name: 'RewardTransaction',
  primaryKey: 'id',
  properties: {
    id: 'string',
    type: 'string?', // earn, redeem, expire, adjust
    points: 'double?',
    amount: 'double?',
    currency: 'string?',
    description: 'string?',
    timestamp: 'date?',
    location: 'string?',
    reference: 'string?',
  }
};
