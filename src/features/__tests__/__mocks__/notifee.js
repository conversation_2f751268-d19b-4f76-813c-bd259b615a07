/**
 * Mock for @notifee/react-native
 */

export const EventType = {
  UNKNOWN: 0,
  DISMISSED: 1,
  PRESS: 2,
  ACTION_PRESS: 3,
  DELIVERED: 4,
  APP_BLOCKED: 5,
  CHANNEL_BLOCKED: 6,
  CHANNEL_GROUP_BLOCKED: 7,
  TRIGGER_NOTIFICATION_CREATED: 8,
  FG_ALREADY_EXIST: 9,
};

export const AndroidImportance = {
  NONE: 0,
  MIN: 1,
  LOW: 2,
  DEFAULT: 3,
  HIGH: 4,
};

export const AndroidVisibility = {
  PRIVATE: 0,
  PUBLIC: 1,
  SECRET: -1,
};

export const AuthorizationStatus = {
  NOT_DETERMINED: -1,
  DENIED: 0,
  AUTHORIZED: 1,
  PROVISIONAL: 2,
  EPHEMERAL: 3,
};

export const displayNotification = jest.fn().mockResolvedValue('notification-id');
export const createChannel = jest.fn().mockResolvedValue(undefined);
export const createChannelGroup = jest.fn().mockResolvedValue(undefined);
export const deleteChannel = jest.fn().mockResolvedValue(undefined);
export const getChannels = jest.fn().mockResolvedValue([]);
export const cancelNotification = jest.fn().mockResolvedValue(undefined);
export const cancelAllNotifications = jest.fn().mockResolvedValue(undefined);
export const getDisplayedNotifications = jest.fn().mockResolvedValue([]);
export const getTriggerNotifications = jest.fn().mockResolvedValue([]);
export const requestPermission = jest.fn().mockResolvedValue({
  authorizationStatus: AuthorizationStatus.AUTHORIZED,
});
export const getNotificationSettings = jest.fn().mockResolvedValue({
  authorizationStatus: AuthorizationStatus.AUTHORIZED,
});
export const openNotificationSettings = jest.fn().mockResolvedValue(undefined);
export const onForegroundEvent = jest.fn().mockReturnValue(() => {});
export const onBackgroundEvent = jest.fn().mockReturnValue(() => {});
export const getInitialNotification = jest.fn().mockResolvedValue(null);
export const setBadgeCount = jest.fn().mockResolvedValue(undefined);
export const getBadgeCount = jest.fn().mockResolvedValue(0);
export const incrementBadgeCount = jest.fn().mockResolvedValue(undefined);
export const decrementBadgeCount = jest.fn().mockResolvedValue(undefined);

// Mock parse function for notification data
export const parse = jest.fn().mockImplementation((detail) => ({
  id: detail?.notification?.id || 'mock-id',
  title: detail?.notification?.title || 'Mock Title',
  body: detail?.notification?.body || 'Mock Body',
  data: detail?.notification?.data || {},
}));

export default {
  EventType,
  AndroidImportance,
  AndroidVisibility,
  AuthorizationStatus,
  displayNotification,
  createChannel,
  createChannelGroup,
  deleteChannel,
  getChannels,
  cancelNotification,
  cancelAllNotifications,
  getDisplayedNotifications,
  getTriggerNotifications,
  requestPermission,
  getNotificationSettings,
  openNotificationSettings,
  onForegroundEvent,
  onBackgroundEvent,
  getInitialNotification,
  setBadgeCount,
  getBadgeCount,
  incrementBadgeCount,
  decrementBadgeCount,
  parse,
};
