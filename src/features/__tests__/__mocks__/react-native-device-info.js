/**
 * Mock for react-native-device-info
 */

export const getUniqueId = jest.fn().mockResolvedValue('mock-device-123');
export const getDeviceName = jest.fn().mockResolvedValue('Mock Device');
export const getBrand = jest.fn().mockResolvedValue('Apple');
export const getManufacturer = jest.fn().mockResolvedValue('Apple Inc.');
export const getModel = jest.fn().mockResolvedValue('iPhone 15 Pro');
export const isEmulator = jest.fn().mockResolvedValue(false);
export const getIpAddress = jest.fn().mockResolvedValue('*************');
export const getSystemVersion = jest.fn().mockResolvedValue('17.0');
export const getVersion = jest.fn().mockResolvedValue('1.0.0');
export const getBuildNumber = jest.fn().mockResolvedValue('1');
export const getBundleId = jest.fn().mockResolvedValue('com.test.app');
export const getCarrier = jest.fn().mockResolvedValue('Mock Carrier');
export const getDeviceCountry = jest.fn().mockResolvedValue('US');
export const getTimezone = jest.fn().mockResolvedValue('America/New_York');
export const getLocales = jest.fn().mockResolvedValue(['en_US']);
export const getCurrency = jest.fn().mockResolvedValue('USD');

export default {
  getUniqueId,
  getDeviceName,
  getBrand,
  getManufacturer,
  getModel,
  isEmulator,
  getIpAddress,
  getSystemVersion,
  getVersion,
  getBuildNumber,
  getBundleId,
  getCarrier,
  getDeviceCountry,
  getTimezone,
  getLocales,
  getCurrency,
};
