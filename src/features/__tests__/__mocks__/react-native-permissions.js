/**
 * Mock for react-native-permissions
 */

export const PERMISSIONS = {
  IOS: {
    CAMERA: 'ios.permission.CAMERA',
    PHOTO_LIBRARY: 'ios.permission.PHOTO_LIBRARY',
    CONTACTS: 'ios.permission.CONTACTS',
    CALENDARS: 'ios.permission.CALENDARS',
    LOCATION_WHEN_IN_USE: 'ios.permission.LOCATION_WHEN_IN_USE',
    FACE_ID: 'ios.permission.FACE_ID',
    BLUETOOTH: 'ios.permission.BLUETOOTH',
  },
  ANDROID: {
    CAMERA: 'android.permission.CAMERA',
    READ_EXTERNAL_STORAGE: 'android.permission.READ_EXTERNAL_STORAGE',
    READ_MEDIA_IMAGES: 'android.permission.READ_MEDIA_IMAGES',
    READ_CONTACTS: 'android.permission.READ_CONTACTS',
    WRITE_CALENDAR: 'android.permission.WRITE_CALENDAR',
    ACCESS_FINE_LOCATION: 'android.permission.ACCESS_FINE_LOCATION',
    BLUETOOTH_SCAN: 'android.permission.BLUETOOTH_SCAN',
  },
};

export const RESULTS = {
  DENIED: 'denied',
  LIMITED: 'limited',
  GRANTED: 'granted',
  BLOCKED: 'blocked',
  UNAVAILABLE: 'unavailable',
};

export const check = jest.fn().mockResolvedValue(RESULTS.GRANTED);
export const request = jest.fn().mockResolvedValue(RESULTS.GRANTED);
export const requestMultiple = jest.fn().mockResolvedValue({});
export const checkMultiple = jest.fn().mockResolvedValue({});
export const openSettings = jest.fn().mockResolvedValue(true);

export default {
  PERMISSIONS,
  RESULTS,
  check,
  request,
  requestMultiple,
  checkMultiple,
  openSettings,
};
