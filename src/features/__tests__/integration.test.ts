/**
 * V7 Critical Components Integration Tests
 * 
 * Tests component interactions, store integration, cross-slice dependencies, and middleware interactions
 * Validates that installations, permissions, and notifications work together correctly
 */

import { configureStore } from '@reduxjs/toolkit';
import installationReducer, { initializeInstallation, setNotificationToken } from '../installations/store/installationSlice';
import permissionsReducer, { checkPermission, requestPermission, refreshPermissions } from '../permissions/store/permissionsSlice';
import notificationsReducer, { initializeNotifications, processNotification, updateCardBadge } from '../notifications/store/notificationsSlice';
import { InstallationState } from '../installations/types';
import { PermissionsState, PermissionFeature, PermissionStatus } from '../permissions/types';
import { NotificationsState, NotificationProvider, NotificationData } from '../notifications/types';

// =============================================================================
// INTEGRATION STORE SETUP
// =============================================================================

interface IntegrationRootState {
  installation: InstallationState;
  permissions: PermissionsState;
  notifications: NotificationsState;
}

function createIntegrationStore() {
  return configureStore({
    reducer: {
      installation: installationReducer,
      permissions: permissionsReducer,
      notifications: notificationsReducer,
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: [
            'installation/initialize/pending',
            'installation/initialize/fulfilled',
            'installation/setToken/pending',
            'installation/setToken/fulfilled',
            'permissions/checkPermission/pending',
            'permissions/checkPermission/fulfilled',
            'permissions/requestPermission/pending',
            'permissions/requestPermission/fulfilled',
            'notifications/initializeNotifications/pending',
            'notifications/initializeNotifications/fulfilled',
            'notifications/processNotification/pending',
            'notifications/processNotification/fulfilled',
          ],
        },
      }),
  });
}

// =============================================================================
// MOCK FUNCTIONS
// =============================================================================

// Mock all helper functions
global.restoreCachedState = jest.fn().mockResolvedValue({});
global.initializeDevice = jest.fn().mockResolvedValue({
  uuid: 'test-device-123',
  name: 'Test Device',
  brand: 'Apple',
  manufacturer: 'Apple Inc.',
  model: 'iPhone 15 Pro',
  simulator: false,
});
global.initializeApp = jest.fn().mockResolvedValue({});
global.initializeCarrier = jest.fn().mockResolvedValue({
  name: 'Verizon',
  country: 'US',
  mobileCountryCode: '310',
});
global.initializeLocale = jest.fn().mockResolvedValue({});
global.initializeCapabilities = jest.fn().mockResolvedValue([]);
global.initializeTokens = jest.fn().mockResolvedValue({});
global.initializePermissions = jest.fn().mockResolvedValue([]);
global.initializePayments = jest.fn().mockResolvedValue({});
global.persistInstallationState = jest.fn().mockResolvedValue(undefined);
global.syncTokenWithBackend = jest.fn().mockResolvedValue(undefined);
global.isNetworkError = jest.fn().mockReturnValue(false);

global.checkPermissionStatus = jest.fn().mockResolvedValue({
  status: PermissionStatus.ALLOWED,
  options: [],
});
global.checkAndRequestPermission = jest.fn().mockResolvedValue(true);
global.detectPermissionChanges = jest.fn().mockReturnValue([]);

global.selectNotificationProvider = jest.fn().mockImplementation((platform: string, region: string) => {
  if (platform === 'ios') return NotificationProvider.APNS;
  return (region === 'CN' || region === 'China') ? NotificationProvider.JPUSH : NotificationProvider.FCM;
});
global.loadNotificationProvider = jest.fn().mockResolvedValue({
  init: jest.fn().mockResolvedValue(undefined),
  register: jest.fn().mockResolvedValue({ alert: true }),
  getToken: jest.fn().mockResolvedValue('test-token-123'),
  setToken: jest.fn(),
});
global.createNotificationChannels = jest.fn().mockResolvedValue(undefined);
global.handleNotificationSync = jest.fn().mockResolvedValue(undefined);
global.handleNotificationEngagement = jest.fn().mockResolvedValue(undefined);
global.isChineseMarket = jest.fn().mockReturnValue(false);

// =============================================================================
// INTEGRATION TESTS
// =============================================================================

describe('V7 Critical Components Integration Tests', () => {
  let store: ReturnType<typeof createIntegrationStore>;
  
  beforeEach(() => {
    store = createIntegrationStore();
    jest.clearAllMocks();
  });
  
  // =============================================================================
  // STORE INTEGRATION TESTS
  // =============================================================================
  
  describe('Store Integration', () => {
    test('should have all three slices integrated correctly', () => {
      const state = store.getState();
      
      expect(state.installation).toBeDefined();
      expect(state.permissions).toBeDefined();
      expect(state.notifications).toBeDefined();
      
      // Check initial states
      expect(state.installation.isInitialized).toBe(false);
      expect(state.permissions.isInitialized).toBe(false);
      expect(state.notifications.isInitialized).toBe(false);
    });
    
    test('should handle concurrent initialization of all components', async () => {
      // Initialize all components concurrently
      const [installationResult, permissionsResult, notificationsResult] = await Promise.all([
        store.dispatch(initializeInstallation()),
        store.dispatch(refreshPermissions({})),
        store.dispatch(initializeNotifications({ platform: 'ios', region: 'US' })),
      ]);
      
      expect(installationResult.type).toBe('installation/initialize/fulfilled');
      expect(permissionsResult.type).toBe('permissions/refresh/fulfilled');
      expect(notificationsResult.type).toBe('notifications/initialize/fulfilled');
      
      const state = store.getState();
      expect(state.installation.isInitialized).toBe(true);
      expect(state.notifications.isInitialized).toBe(true);
    });
    
    test('should maintain state isolation between slices', async () => {
      // Trigger error in one slice
      global.initializeDevice = jest.fn().mockRejectedValue(new Error('Device init failed'));
      
      await store.dispatch(initializeInstallation());
      await store.dispatch(initializeNotifications({ platform: 'ios', region: 'US' }));
      
      const state = store.getState();
      
      // Installation should have error
      expect(state.installation.error).toBeTruthy();
      expect(state.installation.isInitialized).toBe(false);
      
      // Notifications should be successful
      expect(state.notifications.error).toBeNull();
      expect(state.notifications.isInitialized).toBe(true);
    });
  });
  
  // =============================================================================
  // CROSS-SLICE DEPENDENCY TESTS
  // =============================================================================
  
  describe('Cross-Slice Dependencies', () => {
    test('should handle notification token flow between installations and notifications', async () => {
      // Initialize notifications first
      await store.dispatch(initializeNotifications({ platform: 'android', region: 'US' }));
      
      let state = store.getState();
      expect(state.notifications.activeProvider).toBe(NotificationProvider.FCM);
      
      // Set token in installation slice
      await store.dispatch(setNotificationToken({
        provider: NotificationProvider.FCM,
        token: 'integration-token-123',
      }));
      
      state = store.getState();
      
      // Token should be stored in installation
      expect(state.installation.tokens[NotificationProvider.FCM]).toBeDefined();
      expect(state.installation.tokens[NotificationProvider.FCM].token).toBe('integration-token-123');
      expect(state.installation.notifyToken).toBe('integration-token-123');
    });
    
    test('should handle permission-dependent notification features', async () => {
      // Check notification permission
      await store.dispatch(checkPermission(PermissionFeature.NOTIFICATIONS));
      
      let state = store.getState();
      expect(state.permissions.permissions).toHaveLength(1);
      expect(state.permissions.permissions[0].feature).toBe(PermissionFeature.NOTIFICATIONS);
      expect(state.permissions.permissions[0].status).toBe(PermissionStatus.ALLOWED);
      
      // Initialize notifications (should succeed with permission granted)
      await store.dispatch(initializeNotifications({ platform: 'ios', region: 'US' }));
      
      state = store.getState();
      expect(state.notifications.isInitialized).toBe(true);
    });
    
    test('should handle device capability detection affecting notifications', async () => {
      // Reset mocks to successful state
      global.initializeDevice = jest.fn().mockResolvedValue({
        uuid: 'test-device-123',
        name: 'Test Device',
        brand: 'Apple',
        manufacturer: 'Apple Inc.',
        model: 'iPhone 15 Pro',
        simulator: false,
      });

      // Initialize installation with capabilities
      await store.dispatch(initializeInstallation());
      
      let state = store.getState();
      expect(state.installation.isInitialized).toBe(true);
      
      // Initialize notifications (should use device capabilities)
      await store.dispatch(initializeNotifications({ platform: 'ios', region: 'US' }));
      
      state = store.getState();
      expect(state.notifications.activeProvider).toBe(NotificationProvider.APNS); // iOS
    });
  });
  
  // =============================================================================
  // NOTIFICATION PROCESSING INTEGRATION TESTS
  // =============================================================================
  
  describe('Notification Processing Integration', () => {
    test('should process notification and update badges', async () => {
      // Initialize notifications
      await store.dispatch(initializeNotifications({ platform: 'ios', region: 'US' }));
      
      // Process notification
      const notificationData: NotificationData = {
        id: 'integration-notif-1',
        title: 'Test Integration Notification',
        body: 'Testing integration',
        data: { cardId: 'card-123', badgeCount: 5 },
        options: { banner: true },
      };
      
      await store.dispatch(processNotification({
        notification: notificationData,
        tapped: false,
        inApp: true,
      }));
      
      // Update badge based on notification data
      await store.dispatch(updateCardBadge({
        cardId: 'card-123',
        count: 5,
      }));
      
      const state = store.getState();
      
      // Check notification was processed
      expect(state.notifications.handledNotifications).toContain('integration-notif-1');
      expect(state.notifications.history).toHaveLength(1);
      
      // Check badge was updated
      expect(state.notifications.badges.cardBadges['card-123']).toBe(5);
      expect(state.notifications.badges.appBadge).toBe(5);
    });
    
    test('should handle notification sync triggering data refresh', async () => {
      // Initialize all components
      await store.dispatch(initializeInstallation());
      await store.dispatch(initializeNotifications({ platform: 'ios', region: 'US' }));
      
      // Process notification with sync requirement
      const syncNotification: NotificationData = {
        id: 'sync-notif-1',
        title: 'Sync Notification',
        body: 'Requires data sync',
        data: {},
        options: { sync: 'cache' },
      };
      
      await store.dispatch(processNotification({
        notification: syncNotification,
        tapped: false,
        inApp: true,
      }));
      
      // Verify sync was triggered
      expect(global.handleNotificationSync).toHaveBeenCalledWith('cache');
      
      const state = store.getState();
      expect(state.notifications.handledNotifications).toContain('sync-notif-1');
    });
  });
  
  // =============================================================================
  // ERROR HANDLING INTEGRATION TESTS
  // =============================================================================
  
  describe('Error Handling Integration', () => {
    test('should handle cascading errors gracefully', async () => {
      // Simulate network error in installation
      global.syncTokenWithBackend = jest.fn().mockRejectedValue(new Error('Network Error'));
      global.isNetworkError = jest.fn().mockReturnValue(true);
      
      // Try to set token (should fail)
      await store.dispatch(setNotificationToken({
        provider: NotificationProvider.FCM,
        token: 'failing-token',
      }));
      
      // Initialize notifications (should still work)
      await store.dispatch(initializeNotifications({ platform: 'android', region: 'US' }));
      
      const state = store.getState();
      
      // Installation should have error
      expect(state.installation.error).toBeTruthy();
      
      // Notifications should still work
      expect(state.notifications.isInitialized).toBe(true);
      expect(state.notifications.error).toBeNull();
    });
    
    test('should handle permission denial affecting notification registration', async () => {
      // Mock permission denial
      global.checkAndRequestPermission = jest.fn().mockResolvedValue(false);
      global.checkPermissionStatus = jest.fn().mockResolvedValue({
        status: PermissionStatus.DENIED,
        options: [],
      });
      
      // Request notification permission (should fail)
      await store.dispatch(requestPermission({
        feature: PermissionFeature.NOTIFICATIONS,
        options: {},
      }));
      
      let state = store.getState();
      expect(state.permissions.permissions[0].status).toBe(PermissionStatus.DENIED);
      
      // Initialize notifications (should handle gracefully)
      await store.dispatch(initializeNotifications({ platform: 'ios', region: 'US' }));
      
      state = store.getState();
      expect(state.notifications.isInitialized).toBe(true);
    });
  });
  
  // =============================================================================
  // PERFORMANCE INTEGRATION TESTS
  // =============================================================================
  
  describe('Performance Integration', () => {
    test('should handle high-frequency operations efficiently', async () => {
      // Initialize all components
      await store.dispatch(initializeInstallation());
      await store.dispatch(initializeNotifications({ platform: 'ios', region: 'US' }));
      
      const startTime = performance.now();
      
      // Simulate high-frequency badge updates
      const badgeUpdates = Array.from({ length: 100 }, (_, i) => 
        store.dispatch(updateCardBadge({ cardId: `card-${i}`, count: i % 10 }))
      );
      
      await Promise.all(badgeUpdates);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete within reasonable time (< 1 second)
      expect(duration).toBeLessThan(1000);
      
      const state = store.getState();
      expect(Object.keys(state.notifications.badges.cardBadges)).toHaveLength(100);
    });
    
    test('should handle concurrent permission checks efficiently', async () => {
      const startTime = performance.now();
      
      // Check multiple permissions concurrently
      const permissionChecks = [
        PermissionFeature.CAMERA,
        PermissionFeature.PHOTO,
        PermissionFeature.CONTACTS,
        PermissionFeature.LOCATION,
        PermissionFeature.NOTIFICATIONS,
      ].map(feature => store.dispatch(checkPermission(feature)));
      
      await Promise.all(permissionChecks);
      
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Should complete efficiently
      expect(duration).toBeLessThan(500);
      
      const state = store.getState();
      expect(state.permissions.permissions).toHaveLength(5);
    });
  });
});
