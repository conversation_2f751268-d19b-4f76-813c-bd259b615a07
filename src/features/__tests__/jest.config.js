/**
 * Jest Configuration for V7 Critical Components Tests
 * 
 * Optimized configuration for testing installations, permissions, and notifications
 * with proper mocking and coverage reporting
 */

module.exports = {
  preset: 'react-native',
  
  // Test file patterns
  testMatch: [
    '<rootDir>/src/features/**/__tests__/**/*.test.{js,ts,tsx}',
    '<rootDir>/src/features/**/*.test.{js,ts,tsx}',
  ],
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  
  // Transform configuration
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
    '^.+\\.(js|jsx)$': 'babel-jest',
  },
  
  // Module name mapping
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@features/(.*)$': '<rootDir>/src/features/$1',
    '^@store/(.*)$': '<rootDir>/src/store/$1',
    '^@types/(.*)$': '<rootDir>/src/types/$1',
  },
  
  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/src/features/__tests__/setup.ts',
  ],
  
  // Mock configuration
  moduleNameMapping: {
    // Mock React Native modules
    '^react-native$': '<rootDir>/node_modules/react-native',
    '^react-native-permissions$': '<rootDir>/src/features/__tests__/__mocks__/react-native-permissions.js',
    '^react-native-device-info$': '<rootDir>/src/features/__tests__/__mocks__/react-native-device-info.js',
    '^@notifee/react-native$': '<rootDir>/src/features/__tests__/__mocks__/notifee.js',

  },
  
  // Coverage configuration
  collectCoverage: true,
  collectCoverageFrom: [
    'src/features/installations/**/*.{ts,tsx}',
    'src/features/permissions/**/*.{ts,tsx}',
    'src/features/notifications/**/*.{ts,tsx}',
    '!src/features/**/__tests__/**',
    '!src/features/**/*.test.{ts,tsx}',
    '!src/features/**/types/**',
    '!**/*.d.ts',
  ],
  
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
  ],
  
  coverageDirectory: '<rootDir>/coverage/features',
  
  // Coverage thresholds for critical business logic
  coverageThreshold: {
    global: {
      branches: 90,
      functions: 95,
      lines: 95,
      statements: 95,
    },
    // Specific thresholds for business logic files
    'src/features/installations/store/*.ts': {
      branches: 95,
      functions: 100,
      lines: 100,
      statements: 100,
    },
    'src/features/permissions/store/*.ts': {
      branches: 95,
      functions: 100,
      lines: 100,
      statements: 100,
    },
    'src/features/notifications/store/*.ts': {
      branches: 95,
      functions: 100,
      lines: 100,
      statements: 100,
    },
  },
  
  // Test environment
  testEnvironment: 'jsdom',
  
  // Global setup
  globals: {
    'ts-jest': {
      tsconfig: {
        jsx: 'react-jsx',
        esModuleInterop: true,
        allowSyntheticDefaultImports: true,
      },
    },
  },
  
  // Timeout configuration
  testTimeout: 10000,
  
  // Verbose output for debugging
  verbose: true,
  
  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Watch mode configuration
  watchPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/coverage/',
  ],
};
