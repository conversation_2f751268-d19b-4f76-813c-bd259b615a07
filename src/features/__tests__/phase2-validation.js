#!/usr/bin/env node

/**
 * V7 Phase 2 Validation Script
 * 
 * Validates service layer implementation and Redux integration
 * Simple, focused validation for Phase 2 completion
 */

const fs = require('fs');
const path = require('path');

class Phase2Validator {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      errors: [],
    };
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',
      success: '\x1b[32m',
      warning: '\x1b[33m',
      error: '\x1b[31m',
      reset: '\x1b[0m',
    };
    console.log(`${colors[type]}${message}${colors.reset}`);
  }

  test(name, fn) {
    try {
      fn();
      this.log(`  ✅ ${name}`, 'success');
      this.results.passed++;
    } catch (error) {
      this.log(`  ❌ ${name}`, 'error');
      this.log(`     ${error.message}`, 'error');
      this.results.failed++;
      this.results.errors.push({ test: name, error: error.message });
    }
  }

  expect(actual) {
    return {
      toExist: () => {
        if (!fs.existsSync(actual)) {
          throw new Error(`File does not exist: ${actual}`);
        }
      },
      toContain: (expected) => {
        if (!actual.includes(expected)) {
          throw new Error(`Expected content to contain: ${expected}`);
        }
      },
    };
  }

  async validate() {
    this.log('\n🚀 V7 Phase 2 Validation Starting...\n', 'info');

    this.validateServiceLayer();
    this.validateReduxIntegration();
    this.validateBusinessLogic();

    this.printResults();
  }

  validateServiceLayer() {
    this.log('📋 Service Layer Implementation', 'info');

    // Installation Service
    this.test('InstallationService exists', () => {
      const servicePath = path.join(__dirname, '../installations/services/InstallationService.ts');
      this.expect(servicePath).toExist();
    });

    this.test('InstallationService has required functions', () => {
      const servicePath = path.join(__dirname, '../installations/services/InstallationService.ts');
      const content = fs.readFileSync(servicePath, 'utf8');
      this.expect(content).toContain('getDeviceInfo');
      this.expect(content).toContain('getCarrierInfo');
      this.expect(content).toContain('detectCapabilities');
      this.expect(content).toContain('persistToken');
    });

    // Permissions Service
    this.test('PermissionsService exists', () => {
      const servicePath = path.join(__dirname, '../permissions/services/PermissionsService.ts');
      this.expect(servicePath).toExist();
    });

    this.test('PermissionsService has react-native-permissions integration', () => {
      const servicePath = path.join(__dirname, '../permissions/services/PermissionsService.ts');
      const content = fs.readFileSync(servicePath, 'utf8');
      this.expect(content).toContain('react-native-permissions');
      this.expect(content).toContain('checkPermissionStatus');
      this.expect(content).toContain('requestPermission');
    });

    // Notification Service
    this.test('NotificationService exists', () => {
      const servicePath = path.join(__dirname, '../notifications/services/NotificationService.ts');
      this.expect(servicePath).toExist();
    });

    this.test('NotificationService has notifee integration', () => {
      const servicePath = path.join(__dirname, '../notifications/services/NotificationService.ts');
      const content = fs.readFileSync(servicePath, 'utf8');
      this.expect(content).toContain('@notifee/react-native');
      this.expect(content).toContain('getNotificationToken');
      this.expect(content).toContain('setBadgeCount');
    });
  }

  validateReduxIntegration() {
    this.log('\n📋 Redux Service Integration', 'info');

    // Installation slice integration
    this.test('Installation slice uses service layer', () => {
      const slicePath = path.join(__dirname, '../installations/store/installationSlice.ts');
      const content = fs.readFileSync(slicePath, 'utf8');
      this.expect(content).toContain('import * as InstallationService');
      this.expect(content).toContain('InstallationService.getDeviceInfo');
    });

    // Permissions slice integration
    this.test('Permissions slice uses service layer', () => {
      const slicePath = path.join(__dirname, '../permissions/store/permissionsSlice.ts');
      const content = fs.readFileSync(slicePath, 'utf8');
      this.expect(content).toContain('import * as PermissionsService');
      this.expect(content).toContain('PermissionsService.checkPermissionStatus');
    });

    // Notifications slice integration
    this.test('Notifications slice uses service layer', () => {
      const slicePath = path.join(__dirname, '../notifications/store/notificationsSlice.ts');
      const content = fs.readFileSync(slicePath, 'utf8');
      this.expect(content).toContain('import * as NotificationService');
    });
  }

  validateBusinessLogic() {
    this.log('\n📋 Business Logic Preservation', 'info');

    // Carrier change detection
    this.test('Carrier change detection preserved', () => {
      const servicePath = path.join(__dirname, '../installations/services/InstallationService.ts');
      const content = fs.readFileSync(servicePath, 'utf8');
      this.expect(content).toContain('detectCarrierChange');
      this.expect(content).toContain('Multi-factor carrier validation');
    });

    // Progressive permission flow
    this.test('Progressive permission flow preserved', () => {
      const servicePath = path.join(__dirname, '../permissions/services/PermissionsService.ts');
      const content = fs.readFileSync(servicePath, 'utf8');
      this.expect(content).toContain('shouldRequestPermission');
      this.expect(content).toContain('Progressive permission flow');
    });

    // Notification deduplication
    this.test('Notification deduplication preserved', () => {
      const servicePath = path.join(__dirname, '../notifications/services/NotificationService.ts');
      const content = fs.readFileSync(servicePath, 'utf8');
      this.expect(content).toContain('deduplicateNotifications');
      this.expect(content).toContain('Sophisticated deduplication');
    });

    // Android 13+ compliance
    this.test('Android 13+ compliance preserved', () => {
      const servicePath = path.join(__dirname, '../installations/services/InstallationService.ts');
      const content = fs.readFileSync(servicePath, 'utf8');
      this.expect(content).toContain('shouldClearNotificationsForAndroid13');
    });

    // Badge calculation
    this.test('Badge calculation logic preserved', () => {
      const servicePath = path.join(__dirname, '../notifications/services/NotificationService.ts');
      const content = fs.readFileSync(servicePath, 'utf8');
      this.expect(content).toContain('calculateBadgeCount');
      this.expect(content).toContain('Sophisticated badge calculation');
    });
  }

  printResults() {
    this.log('\n📊 Phase 2 Validation Results:', 'info');
    this.log(`✅ Passed: ${this.results.passed}`, 'success');
    this.log(`❌ Failed: ${this.results.failed}`, this.results.failed > 0 ? 'error' : 'success');

    if (this.results.failed > 0) {
      this.log('\n🔍 Failures:', 'warning');
      this.results.errors.forEach(({ test, error }) => {
        this.log(`  • ${test}: ${error}`, 'error');
      });
    }

    const total = this.results.passed + this.results.failed;
    const percentage = total > 0 ? Math.round((this.results.passed / total) * 100) : 0;
    this.log(`\n🎯 Success Rate: ${percentage}%`, 'info');

    if (this.results.failed === 0) {
      this.log('\n🎉 Phase 2 Implementation Complete!', 'success');
      this.log('✅ Service layer implemented and integrated', 'success');
      this.log('✅ Redux slices updated with service calls', 'success');
      this.log('✅ V6 business logic preserved', 'success');
      this.log('\n🚀 Ready for Phase 3: Platform Integration Testing', 'success');
      this.log('\nNext Steps:', 'info');
      this.log('1. Install platform dependencies (react-native-permissions, @notifee/react-native)', 'info');
      this.log('2. Test on physical devices', 'info');
      this.log('3. Validate end-to-end functionality', 'info');
    } else {
      this.log('\n⚠️  Phase 2 incomplete. Please address failures before proceeding.', 'warning');
    }
  }
}

// Run validation
const validator = new Phase2Validator();
validator.validate().catch(console.error);
