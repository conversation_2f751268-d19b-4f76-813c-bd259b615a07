#!/usr/bin/env node

/**
 * V7 Critical Components Test Runner
 * 
 * Runs comprehensive test suites for installations, permissions, and notifications
 * with coverage reporting and validation
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// =============================================================================
// TEST CONFIGURATION
// =============================================================================

const TEST_CONFIG = {
  // Test suites to run
  suites: [
    {
      name: 'Installation Business Logic',
      pattern: 'src/features/installations/__tests__/businessLogic.test.ts',
      description: 'Tests V6 business rules: carrier change, Android 13+, capabilities, tokens',
    },
    {
      name: 'Installation Redux Slice',
      pattern: 'src/features/installations/__tests__/installationSlice.test.ts',
      description: 'Tests Redux Toolkit slice: actions, reducers, async thunks, state transitions',
    },
    {
      name: 'Permissions Business Logic',
      pattern: 'src/features/permissions/__tests__/businessLogic.test.ts',
      description: 'Tests V6 business rules: progressive flows, cross-platform mapping, status normalization',
    },
    {
      name: 'Permissions Redux Slice',
      pattern: 'src/features/permissions/__tests__/permissionsSlice.test.ts',
      description: 'Tests Redux Toolkit slice: actions, reducers, async thunks, state transitions',
    },
    {
      name: 'Notifications Business Logic',
      pattern: 'src/features/notifications/__tests__/businessLogic.test.ts',
      description: 'Tests V6 business rules: multi-provider, deduplication, badge management, processing',
    },
    {
      name: 'Notifications Redux Slice',
      pattern: 'src/features/notifications/__tests__/notificationsSlice.test.ts',
      description: 'Tests Redux Toolkit slice: actions, reducers, async thunks, state transitions',
    },
    {
      name: 'Integration Tests',
      pattern: 'src/features/__tests__/integration.test.ts',
      description: 'Tests component interactions, store integration, cross-slice dependencies',
    },
    {
      name: 'V6 Parity Validation',
      pattern: 'src/features/__tests__/v6-parity-validation.test.ts',
      description: 'Validates V6 vs V7 business logic parity and edge cases',
    },
    {
      name: 'Type Safety Tests',
      pattern: 'src/features/__tests__/type-safety.test.ts',
      description: 'Verifies TypeScript implementation and runtime type validation',
    },
  ],
  
  // Coverage requirements
  coverage: {
    statements: 95,
    branches: 90,
    functions: 95,
    lines: 95,
  },
  
  // Jest configuration
  jestConfig: path.join(__dirname, 'jest.config.js'),
};

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const colors = {
    info: '\x1b[36m',    // Cyan
    success: '\x1b[32m', // Green
    warning: '\x1b[33m', // Yellow
    error: '\x1b[31m',   // Red
    reset: '\x1b[0m',    // Reset
  };
  
  console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
}

function runCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      encoding: 'utf8',
      stdio: options.silent ? 'pipe' : 'inherit',
      ...options,
    });
    return { success: true, output: result };
  } catch (error) {
    return { success: false, error: error.message, output: error.stdout };
  }
}

function checkTestFiles() {
  log('Checking test files exist...', 'info');
  
  const missingFiles = [];
  
  TEST_CONFIG.suites.forEach(suite => {
    const filePath = path.resolve(suite.pattern);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(suite.pattern);
    }
  });
  
  if (missingFiles.length > 0) {
    log(`Missing test files: ${missingFiles.join(', ')}`, 'error');
    return false;
  }
  
  log('All test files found ✓', 'success');
  return true;
}

function runTestSuite(suite) {
  log(`Running ${suite.name}...`, 'info');
  log(`Description: ${suite.description}`, 'info');
  
  const command = `npx jest --config=${TEST_CONFIG.jestConfig} --testPathPattern="${suite.pattern}" --verbose`;
  
  const result = runCommand(command);
  
  if (result.success) {
    log(`${suite.name} PASSED ✓`, 'success');
    return true;
  } else {
    log(`${suite.name} FAILED ✗`, 'error');
    log(`Error: ${result.error}`, 'error');
    return false;
  }
}

function runAllTests() {
  log('Running all test suites...', 'info');
  
  const command = `npx jest --config=${TEST_CONFIG.jestConfig} --coverage --verbose`;
  
  const result = runCommand(command);
  
  if (result.success) {
    log('All tests PASSED ✓', 'success');
    return true;
  } else {
    log('Some tests FAILED ✗', 'error');
    return false;
  }
}

function validateCoverage() {
  log('Validating test coverage...', 'info');
  
  const coverageFile = path.join(__dirname, '../../../coverage/features/coverage-summary.json');
  
  if (!fs.existsSync(coverageFile)) {
    log('Coverage file not found, running tests with coverage...', 'warning');
    return runAllTests();
  }
  
  try {
    const coverage = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
    const total = coverage.total;
    
    const checks = [
      { name: 'Statements', actual: total.statements.pct, required: TEST_CONFIG.coverage.statements },
      { name: 'Branches', actual: total.branches.pct, required: TEST_CONFIG.coverage.branches },
      { name: 'Functions', actual: total.functions.pct, required: TEST_CONFIG.coverage.functions },
      { name: 'Lines', actual: total.lines.pct, required: TEST_CONFIG.coverage.lines },
    ];
    
    let allPassed = true;
    
    checks.forEach(check => {
      if (check.actual >= check.required) {
        log(`${check.name}: ${check.actual}% (required: ${check.required}%) ✓`, 'success');
      } else {
        log(`${check.name}: ${check.actual}% (required: ${check.required}%) ✗`, 'error');
        allPassed = false;
      }
    });
    
    return allPassed;
  } catch (error) {
    log(`Error reading coverage file: ${error.message}`, 'error');
    return false;
  }
}

function generateReport() {
  log('Generating test report...', 'info');
  
  const report = {
    timestamp: new Date().toISOString(),
    suites: TEST_CONFIG.suites.length,
    coverage: TEST_CONFIG.coverage,
    status: 'completed',
  };
  
  const reportPath = path.join(__dirname, '../../../test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log(`Test report generated: ${reportPath}`, 'success');
}

// =============================================================================
// MAIN EXECUTION
// =============================================================================

function main() {
  log('Starting V7 Critical Components Test Suite', 'info');
  log('='.repeat(60), 'info');
  
  // Check prerequisites
  if (!checkTestFiles()) {
    process.exit(1);
  }
  
  // Parse command line arguments
  const args = process.argv.slice(2);
  const runSingle = args.includes('--single');
  const suiteName = args.find(arg => arg.startsWith('--suite='))?.split('=')[1];
  const skipCoverage = args.includes('--skip-coverage');
  
  let success = true;
  
  if (runSingle && suiteName) {
    // Run single test suite
    const suite = TEST_CONFIG.suites.find(s => s.name.toLowerCase().includes(suiteName.toLowerCase()));
    if (suite) {
      success = runTestSuite(suite);
    } else {
      log(`Test suite not found: ${suiteName}`, 'error');
      success = false;
    }
  } else if (runSingle) {
    // Run each suite individually
    for (const suite of TEST_CONFIG.suites) {
      if (!runTestSuite(suite)) {
        success = false;
        break;
      }
    }
  } else {
    // Run all tests together
    success = runAllTests();
  }
  
  // Validate coverage
  if (success && !skipCoverage) {
    success = validateCoverage();
  }
  
  // Generate report
  generateReport();
  
  // Final result
  log('='.repeat(60), 'info');
  if (success) {
    log('🎉 ALL TESTS PASSED! V7 Critical Components are ready for Phase 2', 'success');
    process.exit(0);
  } else {
    log('❌ TESTS FAILED! Please fix issues before proceeding', 'error');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  runTestSuite,
  runAllTests,
  validateCoverage,
  TEST_CONFIG,
};
