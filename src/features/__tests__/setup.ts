/**
 * Jest Setup for V7 Critical Components Tests
 * 
 * Global test setup, mocks, and utilities for installations, permissions, and notifications tests
 */

import 'react-native-gesture-handler/jestSetup';

// =============================================================================
// GLOBAL MOCKS
// =============================================================================

// Mock React Native Platform
jest.mock('react-native/Libraries/Utilities/Platform', () => ({
  OS: 'ios',
  Version: 17,
  select: jest.fn((config) => config.ios || config.default),
}));

// Mock React Native AppState
jest.mock('react-native/Libraries/AppState/AppState', () => ({
  currentState: 'active',
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
}));

// Mock React Native Dimensions
jest.mock('react-native/Libraries/Utilities/Dimensions', () => ({
  get: jest.fn(() => ({ width: 375, height: 812 })),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
}));

// =============================================================================
// REDUX TOOLKIT MOCKS
// =============================================================================

// Mock Redux Toolkit Query
jest.mock('@reduxjs/toolkit/query/react', () => ({
  ...jest.requireActual('@reduxjs/toolkit/query/react'),
  createApi: jest.fn(() => ({
    reducerPath: 'api',
    reducer: jest.fn(),
    middleware: jest.fn(),
  })),
}));

// =============================================================================
// BUSINESS LOGIC FUNCTION MOCKS
// =============================================================================

// Installation helper function mocks
global.restoreCachedState = jest.fn().mockResolvedValue({});
global.initializeDevice = jest.fn().mockResolvedValue({
  uuid: 'mock-device-123',
  name: 'Mock Device',
  brand: 'Apple',
  manufacturer: 'Apple Inc.',
  model: 'iPhone 15 Pro',
  simulator: false,
});
global.initializeApp = jest.fn().mockResolvedValue({
  version: '1.0.0',
  build: '1',
  environment: 'test',
  bundleId: 'com.test.app',
});
global.initializeCarrier = jest.fn().mockResolvedValue({
  name: 'Mock Carrier',
  country: 'US',
  mobileCountryCode: '310',
});
global.initializeLocale = jest.fn().mockResolvedValue({
  languages: ['en'],
  country: 'US',
  currency: 'USD',
  timezone: 'America/New_York',
});
global.initializeCapabilities = jest.fn().mockResolvedValue([]);
global.initializeTokens = jest.fn().mockResolvedValue({});
global.initializePermissions = jest.fn().mockResolvedValue([]);
global.initializePayments = jest.fn().mockResolvedValue({});
global.persistInstallationState = jest.fn().mockResolvedValue(undefined);
global.syncTokenWithBackend = jest.fn().mockResolvedValue(undefined);
global.isNetworkError = jest.fn().mockReturnValue(false);

// Permissions helper function mocks
global.checkPermissionStatus = jest.fn().mockResolvedValue({
  status: 'granted',
  options: [],
});
global.checkAndRequestPermission = jest.fn().mockResolvedValue(true);
global.requestNativePermission = jest.fn().mockResolvedValue(true);
global.showRationaleDialog = jest.fn().mockResolvedValue(true);
global.showSettingsDialog = jest.fn().mockResolvedValue(false);
global.detectPermissionChanges = jest.fn().mockReturnValue([]);

// Notifications helper function mocks
global.selectNotificationProvider = jest.fn().mockReturnValue('fcm');
global.loadNotificationProvider = jest.fn().mockResolvedValue({
  init: jest.fn().mockResolvedValue(undefined),
  register: jest.fn().mockResolvedValue({ alert: true }),
  getToken: jest.fn().mockResolvedValue('mock-token-123'),
  setToken: jest.fn(),
  initialNotification: jest.fn().mockResolvedValue(null),
});
global.createNotificationChannels = jest.fn().mockResolvedValue(undefined);
global.handleNotificationSync = jest.fn().mockResolvedValue(undefined);
global.handleNotificationEngagement = jest.fn().mockResolvedValue(undefined);
global.isChineseMarket = jest.fn().mockReturnValue(false);

// =============================================================================
// CONSOLE MOCKS
// =============================================================================

// Suppress console warnings in tests unless explicitly needed
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

beforeEach(() => {
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterEach(() => {
  console.warn = originalConsoleWarn;
  console.error = originalConsoleError;
});

// =============================================================================
// PERFORMANCE MOCKS
// =============================================================================

// Mock performance.now for performance tests
global.performance = {
  ...global.performance,
  now: jest.fn(() => Date.now()),
};

// =============================================================================
// ASYNC STORAGE MOCK
// =============================================================================

const mockAsyncStorage = {
  getItem: jest.fn().mockResolvedValue(null),
  setItem: jest.fn().mockResolvedValue(undefined),
  removeItem: jest.fn().mockResolvedValue(undefined),
  clear: jest.fn().mockResolvedValue(undefined),
  getAllKeys: jest.fn().mockResolvedValue([]),
  multiGet: jest.fn().mockResolvedValue([]),
  multiSet: jest.fn().mockResolvedValue(undefined),
  multiRemove: jest.fn().mockResolvedValue(undefined),
};

// Using MMKV instead of AsyncStorage - mock is in setupTests.js

// =============================================================================
// TEST UTILITIES
// =============================================================================

// Utility to create mock Redux store state
export const createMockStoreState = (overrides = {}) => ({
  installation: {
    id: '',
    device: null,
    os: null,
    app: null,
    carrier: null,
    locale: null,
    geo: null,
    capabilities: [],
    tokens: {},
    permissions: [],
    payments: {},
    appList: [],
    isInitialized: false,
    lastUpdated: null,
    error: null,
    isLoading: false,
    ...overrides.installation,
  },
  permissions: {
    permissions: [],
    requestQueue: [],
    activeRequest: null,
    isInitialized: false,
    lastSync: null,
    lastRefresh: null,
    error: null,
    isLoading: false,
    isInBackground: false,
    ...overrides.permissions,
  },
  notifications: {
    activeProvider: null,
    providers: {},
    tokens: [],
    currentToken: null,
    handledNotifications: [],
    receivedNotifications: [],
    processingQueue: [],
    badges: {
      cardBadges: {},
      appBadge: 0,
      lastUpdated: new Date().toISOString(),
    },
    history: [],
    channels: [],
    isActive: false,
    isInitialized: false,
    lastSync: null,
    error: null,
    isLoading: false,
    bannerEnabled: true,
    soundEnabled: true,
    ...overrides.notifications,
  },
});

// Utility to wait for async operations
export const waitForAsync = (ms = 0) => new Promise(resolve => setTimeout(resolve, ms));

// Utility to create mock notification data
export const createMockNotification = (overrides = {}) => ({
  id: 'mock-notif-123',
  title: 'Mock Notification',
  subtitle: 'Mock Subtitle',
  body: 'Mock notification body',
  data: { key: 'value' },
  options: {
    sync: 'cache',
    banner: true,
  },
  ...overrides,
});

// Utility to create mock permission data
export const createMockPermission = (overrides = {}) => ({
  feature: 'camera',
  status: 'allowed',
  options: [],
  grantedAt: '2024-01-01T00:00:00Z',
  revokedAt: null,
  lastChecked: '2024-01-01T00:00:00Z',
  ...overrides,
});

// Utility to create mock device info
export const createMockDevice = (overrides = {}) => ({
  uuid: 'mock-device-123',
  name: 'Mock Device',
  brand: 'Apple',
  manufacturer: 'Apple Inc.',
  model: 'iPhone 15 Pro',
  simulator: false,
  ip: '*************',
  ...overrides,
});

// =============================================================================
// GLOBAL TEST CONFIGURATION
// =============================================================================

// Increase timeout for integration tests
jest.setTimeout(10000);

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// =============================================================================
// CLEANUP
// =============================================================================

afterEach(() => {
  // Clear all mocks after each test
  jest.clearAllMocks();
  
  // Reset mock implementations
  global.restoreCachedState.mockResolvedValue({});
  global.checkPermissionStatus.mockResolvedValue({ status: 'granted', options: [] });
  global.selectNotificationProvider.mockReturnValue('fcm');
  global.isNetworkError.mockReturnValue(false);
  global.isChineseMarket.mockReturnValue(false);
});

afterAll(() => {
  // Restore original implementations
  jest.restoreAllMocks();
});
