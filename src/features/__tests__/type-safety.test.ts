/**
 * V7 Type Safety Tests
 * 
 * Verifies TypeScript implementation with type checking and runtime validation
 * Tests all interfaces, action payloads, error types, and critical data structures
 */

// Installation types
import {
  InstallationState,
  CarrierInfo,
  DeviceInfo,
  DeviceCapability,
  NotificationToken,
  SetTokenPayload,
  UpdateGeoPayload,
  InstallationError,
  InstallationErrorCodes,
  CapabilityTypes,
  NotificationProviders,
} from '../installations/types';

// Permissions types
import {
  PermissionsState,
  Permission,
  PermissionFeature,
  PermissionStatus,
  PermissionOptions,
  RequestPermissionPayload,
  UpdatePermissionPayload,
  PermissionError,
  PermissionErrorCodes,
  PLATFORM_PERMISSION_MAP,
  NATIVE_STATUS_MAP,
} from '../permissions/types';

// Notifications types
import {
  NotificationsState,
  NotificationProvider,
  NotificationData,
  ProcessedNotification,
  NotificationChannel,
  BadgeState,
  NotificationHistory,
  InitializeNotificationsPayload,
  ProcessNotificationPayload,
  UpdateBadgePayload,
  NotificationError,
  NotificationErrorCodes,
} from '../notifications/types';

// =============================================================================
// TYPE VALIDATION HELPERS
// =============================================================================

function validateInstallationState(state: any): state is InstallationState {
  return (
    typeof state === 'object' &&
    typeof state.id === 'string' &&
    (state.device === null || typeof state.device === 'object') &&
    (state.os === null || typeof state.os === 'object') &&
    (state.app === null || typeof state.app === 'object') &&
    (state.carrier === null || typeof state.carrier === 'object') &&
    (state.locale === null || typeof state.locale === 'object') &&
    (state.geo === null || typeof state.geo === 'object') &&
    Array.isArray(state.capabilities) &&
    typeof state.tokens === 'object' &&
    Array.isArray(state.permissions) &&
    typeof state.payments === 'object' &&
    Array.isArray(state.appList) &&
    typeof state.isInitialized === 'boolean' &&
    (state.lastUpdated === null || typeof state.lastUpdated === 'string') &&
    (state.error === null || typeof state.error === 'string') &&
    typeof state.isLoading === 'boolean'
  );
}

function validatePermissionsState(state: any): state is PermissionsState {
  return (
    typeof state === 'object' &&
    Array.isArray(state.permissions) &&
    Array.isArray(state.requestQueue) &&
    (state.activeRequest === null || typeof state.activeRequest === 'object') &&
    typeof state.isInitialized === 'boolean' &&
    (state.lastSync === null || typeof state.lastSync === 'string') &&
    (state.lastRefresh === null || typeof state.lastRefresh === 'string') &&
    (state.error === null || typeof state.error === 'string') &&
    typeof state.isLoading === 'boolean' &&
    typeof state.isInBackground === 'boolean'
  );
}

function validateNotificationsState(state: any): state is NotificationsState {
  return (
    typeof state === 'object' &&
    (state.activeProvider === null || typeof state.activeProvider === 'string') &&
    typeof state.providers === 'object' &&
    Array.isArray(state.tokens) &&
    (state.currentToken === null || typeof state.currentToken === 'string') &&
    Array.isArray(state.handledNotifications) &&
    Array.isArray(state.receivedNotifications) &&
    Array.isArray(state.processingQueue) &&
    typeof state.badges === 'object' &&
    Array.isArray(state.history) &&
    Array.isArray(state.channels) &&
    typeof state.isActive === 'boolean' &&
    typeof state.isInitialized === 'boolean' &&
    (state.lastSync === null || typeof state.lastSync === 'string') &&
    (state.error === null || typeof state.error === 'string') &&
    typeof state.isLoading === 'boolean' &&
    typeof state.bannerEnabled === 'boolean' &&
    typeof state.soundEnabled === 'boolean'
  );
}

// =============================================================================
// TYPE SAFETY TESTS
// =============================================================================

describe('V7 Type Safety Tests', () => {
  
  // =============================================================================
  // INSTALLATION TYPES TESTS
  // =============================================================================
  
  describe('Installation Types', () => {
    test('should validate InstallationState interface', () => {
      const validState: InstallationState = {
        id: 'test-installation',
        device: null,
        os: null,
        app: null,
        carrier: null,
        locale: null,
        geo: null,
        capabilities: [],
        tokens: {},
        permissions: [],
        payments: {},
        appList: [],
        isInitialized: false,
        lastUpdated: null,
        error: null,
        isLoading: false,
      };
      
      expect(validateInstallationState(validState)).toBe(true);
      
      // Test with populated data
      const populatedState: InstallationState = {
        ...validState,
        device: {
          uuid: 'device-123',
          name: 'Test Device',
          brand: 'Apple',
          manufacturer: 'Apple Inc.',
          model: 'iPhone 15 Pro',
          simulator: false,
          ip: '*************',
        },
        carrier: {
          name: 'Verizon',
          country: 'US',
          mobileCountryCode: '310',
        },
        capabilities: [
          { name: CapabilityTypes.NFC, support: true },
          { name: CapabilityTypes.BIOMETRICS, support: ['FaceID'] },
        ],
        tokens: {
          [NotificationProviders.FCM]: {
            token: 'test-token-123',
            modifiedAt: '2024-01-01T00:00:00Z',
          },
        },
      };
      
      expect(validateInstallationState(populatedState)).toBe(true);
    });
    
    test('should validate CarrierInfo interface', () => {
      const validCarrier: CarrierInfo = {
        name: 'Verizon',
        country: 'US',
        mobileCountryCode: '310',
      };
      
      expect(validCarrier.name).toBe('Verizon');
      expect(validCarrier.country).toBe('US');
      expect(validCarrier.mobileCountryCode).toBe('310');
      
      // Test with optional changed property
      const changedCarrier: CarrierInfo = {
        ...validCarrier,
        changed: true,
      };
      
      expect(changedCarrier.changed).toBe(true);
    });
    
    test('should validate DeviceInfo interface', () => {
      const validDevice: DeviceInfo = {
        uuid: 'device-123',
        name: 'Test iPhone',
        brand: 'Apple',
        manufacturer: 'Apple Inc.',
        model: 'iPhone 15 Pro',
        simulator: false,
      };
      
      expect(validDevice.uuid).toBe('device-123');
      expect(validDevice.simulator).toBe(false);
      
      // Test with optional IP
      const deviceWithIP: DeviceInfo = {
        ...validDevice,
        ip: '*************',
      };
      
      expect(deviceWithIP.ip).toBe('*************');
    });
    
    test('should validate action payload types', () => {
      const setTokenPayload: SetTokenPayload = {
        provider: NotificationProviders.FCM,
        token: 'test-token-123',
      };
      
      expect(setTokenPayload.provider).toBe(NotificationProviders.FCM);
      expect(setTokenPayload.token).toBe('test-token-123');
      
      const updateGeoPayload: UpdateGeoPayload = {
        latitude: 37.7749,
        longitude: -122.4194,
      };
      
      expect(updateGeoPayload.latitude).toBe(37.7749);
      expect(updateGeoPayload.longitude).toBe(-122.4194);
    });
    
    test('should validate error types', () => {
      const installationError: InstallationError = {
        code: InstallationErrorCodes.NETWORK_ERROR,
        message: 'Network connection failed',
        context: { provider: 'FCM' },
      };
      
      expect(installationError.code).toBe(InstallationErrorCodes.NETWORK_ERROR);
      expect(installationError.message).toBe('Network connection failed');
      expect(installationError.context).toEqual({ provider: 'FCM' });
    });
  });
  
  // =============================================================================
  // PERMISSIONS TYPES TESTS
  // =============================================================================
  
  describe('Permissions Types', () => {
    test('should validate PermissionsState interface', () => {
      const validState: PermissionsState = {
        permissions: [],
        requestQueue: [],
        activeRequest: null,
        isInitialized: false,
        lastSync: null,
        lastRefresh: null,
        error: null,
        isLoading: false,
        isInBackground: false,
      };
      
      expect(validatePermissionsState(validState)).toBe(true);
    });
    
    test('should validate Permission interface', () => {
      const validPermission: Permission = {
        feature: PermissionFeature.CAMERA,
        status: PermissionStatus.ALLOWED,
        options: ['fullAccuracy'],
        grantedAt: '2024-01-01T00:00:00Z',
        revokedAt: null,
        lastChecked: '2024-01-01T00:00:00Z',
      };
      
      expect(validPermission.feature).toBe(PermissionFeature.CAMERA);
      expect(validPermission.status).toBe(PermissionStatus.ALLOWED);
      expect(validPermission.options).toEqual(['fullAccuracy']);
      expect(validPermission.grantedAt).toBe('2024-01-01T00:00:00Z');
      expect(validPermission.revokedAt).toBeNull();
    });
    
    test('should validate permission enums', () => {
      // Test PermissionFeature enum
      expect(Object.values(PermissionFeature)).toContain(PermissionFeature.CAMERA);
      expect(Object.values(PermissionFeature)).toContain(PermissionFeature.PHOTO);
      expect(Object.values(PermissionFeature)).toContain(PermissionFeature.LOCATION);
      expect(Object.values(PermissionFeature)).toContain(PermissionFeature.NOTIFICATIONS);
      
      // Test PermissionStatus enum
      expect(Object.values(PermissionStatus)).toContain(PermissionStatus.UNKNOWN);
      expect(Object.values(PermissionStatus)).toContain(PermissionStatus.ALLOWED);
      expect(Object.values(PermissionStatus)).toContain(PermissionStatus.BLOCKED);
      expect(Object.values(PermissionStatus)).toContain(PermissionStatus.LIMITED);
    });
    
    test('should validate permission mapping types', () => {
      // Test PLATFORM_PERMISSION_MAP structure
      expect(PLATFORM_PERMISSION_MAP[PermissionFeature.CAMERA]).toBeDefined();
      expect(PLATFORM_PERMISSION_MAP[PermissionFeature.CAMERA].ios).toBeDefined();
      expect(PLATFORM_PERMISSION_MAP[PermissionFeature.CAMERA].android).toBeDefined();
      
      // Test NATIVE_STATUS_MAP structure
      expect(NATIVE_STATUS_MAP['granted']).toBe(PermissionStatus.ALLOWED);
      expect(NATIVE_STATUS_MAP['blocked']).toBe(PermissionStatus.BLOCKED);
    });
    
    test('should validate permission action payloads', () => {
      const requestPayload: RequestPermissionPayload = {
        feature: PermissionFeature.CAMERA,
        options: {
          showRationale: true,
          type: 'whenInUse',
        },
      };
      
      expect(requestPayload.feature).toBe(PermissionFeature.CAMERA);
      expect(requestPayload.options?.showRationale).toBe(true);
      expect(requestPayload.options?.type).toBe('whenInUse');
      
      const updatePayload: UpdatePermissionPayload = {
        feature: PermissionFeature.LOCATION,
        status: PermissionStatus.LIMITED,
        options: ['whenInUse'],
        grantedAt: '2024-01-01T00:00:00Z',
      };
      
      expect(updatePayload.feature).toBe(PermissionFeature.LOCATION);
      expect(updatePayload.status).toBe(PermissionStatus.LIMITED);
      expect(updatePayload.options).toEqual(['whenInUse']);
    });
  });
  
  // =============================================================================
  // NOTIFICATIONS TYPES TESTS
  // =============================================================================
  
  describe('Notifications Types', () => {
    test('should validate NotificationsState interface', () => {
      const validState: NotificationsState = {
        activeProvider: null,
        providers: {},
        tokens: [],
        currentToken: null,
        handledNotifications: [],
        receivedNotifications: [],
        processingQueue: [],
        badges: {
          cardBadges: {},
          appBadge: 0,
          lastUpdated: '2024-01-01T00:00:00Z',
        },
        history: [],
        channels: [],
        isActive: false,
        isInitialized: false,
        lastSync: null,
        error: null,
        isLoading: false,
        bannerEnabled: true,
        soundEnabled: true,
      };
      
      expect(validateNotificationsState(validState)).toBe(true);
    });
    
    test('should validate NotificationData interface', () => {
      const validNotification: NotificationData = {
        id: 'notif-123',
        title: 'Test Notification',
        subtitle: 'Test Subtitle',
        body: 'Test notification body',
        image: 'https://example.com/image.png',
        sound: 'default',
        data: { key: 'value' },
        options: {
          sync: 'cache',
          banner: true,
          nav: { engage: { object: 'Offer', param: { id: 'offer-1' } } },
        },
      };
      
      expect(validNotification.id).toBe('notif-123');
      expect(validNotification.title).toBe('Test Notification');
      expect(validNotification.options?.sync).toBe('cache');
      expect(validNotification.options?.banner).toBe(true);
    });
    
    test('should validate ProcessedNotification interface', () => {
      const processedNotification: ProcessedNotification = {
        id: 'notif-123',
        title: 'Test Notification',
        subtitle: 'Test Subtitle',
        body: 'Test notification body',
        data: { key: 'value' },
        receivedAt: '2024-01-01T00:00:00Z',
        handledAt: '2024-01-01T00:00:00Z',
        tapped: false,
        inApp: true,
        shouldShowBanner: true,
        processed: true,
      };
      
      expect(processedNotification.tapped).toBe(false);
      expect(processedNotification.inApp).toBe(true);
      expect(processedNotification.shouldShowBanner).toBe(true);
      expect(processedNotification.processed).toBe(true);
    });
    
    test('should validate BadgeState interface', () => {
      const badgeState: BadgeState = {
        cardBadges: {
          'card-1': 5,
          'card-2': 3,
          'card-3': 0,
        },
        appBadge: 8,
        lastUpdated: '2024-01-01T00:00:00Z',
      };
      
      expect(badgeState.cardBadges['card-1']).toBe(5);
      expect(badgeState.appBadge).toBe(8);
      expect(badgeState.lastUpdated).toBe('2024-01-01T00:00:00Z');
    });
    
    test('should validate notification action payloads', () => {
      const initPayload: InitializeNotificationsPayload = {
        platform: 'ios',
        region: 'US',
      };
      
      expect(initPayload.platform).toBe('ios');
      expect(initPayload.region).toBe('US');
      
      const processPayload: ProcessNotificationPayload = {
        notification: {
          id: 'notif-123',
          title: 'Test',
          body: 'Test body',
          data: {},
        },
        tapped: false,
        inApp: true,
      };
      
      expect(processPayload.tapped).toBe(false);
      expect(processPayload.inApp).toBe(true);
      
      const badgePayload: UpdateBadgePayload = {
        cardId: 'card-123',
        count: 5,
      };
      
      expect(badgePayload.cardId).toBe('card-123');
      expect(badgePayload.count).toBe(5);
    });
  });
  
  // =============================================================================
  // RUNTIME TYPE VALIDATION TESTS
  // =============================================================================
  
  describe('Runtime Type Validation', () => {
    test('should detect invalid InstallationState at runtime', () => {
      const invalidState = {
        id: 123, // Should be string
        device: 'invalid', // Should be object or null
        isInitialized: 'false', // Should be boolean
      };
      
      expect(validateInstallationState(invalidState)).toBe(false);
    });
    
    test('should detect invalid PermissionsState at runtime', () => {
      const invalidState = {
        permissions: 'not-array', // Should be array
        isInitialized: 'false', // Should be boolean
        isInBackground: null, // Should be boolean
      };
      
      expect(validatePermissionsState(invalidState)).toBe(false);
    });
    
    test('should detect invalid NotificationsState at runtime', () => {
      const invalidState = {
        tokens: 'not-array', // Should be array
        badges: 'not-object', // Should be object
        isActive: 'true', // Should be boolean
      };
      
      expect(validateNotificationsState(invalidState)).toBe(false);
    });
    
    test('should validate error type structures', () => {
      const validInstallationError: InstallationError = {
        code: InstallationErrorCodes.NETWORK_ERROR,
        message: 'Network failed',
      };
      
      const validPermissionError: PermissionError = {
        code: PermissionErrorCodes.REQUEST_FAILED,
        message: 'Permission request failed',
        feature: PermissionFeature.CAMERA,
      };
      
      const validNotificationError: NotificationError = {
        code: NotificationErrorCodes.PROVIDER_REGISTRATION_FAILED,
        message: 'Provider registration failed',
        provider: NotificationProvider.FCM,
      };
      
      expect(validInstallationError.code).toBe(InstallationErrorCodes.NETWORK_ERROR);
      expect(validPermissionError.feature).toBe(PermissionFeature.CAMERA);
      expect(validNotificationError.provider).toBe(NotificationProvider.FCM);
    });
  });
});
