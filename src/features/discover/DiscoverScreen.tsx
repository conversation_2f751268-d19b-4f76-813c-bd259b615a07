/**
 * Discover Screen
 * Connected to Redux using new offer slices architecture
 */

import React, { useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, SafeAreaView } from 'react-native';
import { useAppSelector, useAppDispatch } from '../../core/store/hooks';
import { Button, ListItem } from '../../shared/ui';

// Import new offer slice actions and selectors
import {
  selectOfferMetrics,
  selectFilteredOffers,
  selectOfferDataState,
  selectActiveOffers,
  setFilters,
  selectOffer,
  setViewMode,
} from '../../core/store/slices/offer';

export const DiscoverScreen: React.FC = () => {
  const dispatch = useAppDispatch();

  // Select data from new offer slices
  const offers = useAppSelector(selectFilteredOffers);
  const activeOffers = useAppSelector(selectActiveOffers);
  const metrics = useAppSelector(selectOfferMetrics);
  const { loading, error } = useAppSelector(selectOfferDataState);

  // Load offers on mount
  useEffect(() => {
    console.log('DiscoverScreen mounted, offers:', offers.length);
  }, [offers.length]);

  const handleOfferPress = (offerId: string) => {
    console.log('Offer pressed:', offerId);
    dispatch(selectOffer(offerId));
  };

  const handleFilterPress = () => {
    console.log('Filter pressed');
    // Example: Toggle view mode
    dispatch(setViewMode('grid'));
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Discover</Text>
          <Text style={styles.subtitle}>
            {metrics.totalOffers} offers • {metrics.activeOffers} active
          </Text>
          <Button
            title="Filter"
            onPress={handleFilterPress}
            variant="secondary"
            size="small"
          />
        </View>

        {/* Featured Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Featured Offers</Text>

          {loading && (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Loading offers...</Text>
            </View>
          )}

          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>Error: {error}</Text>
            </View>
          )}

          {!loading && !error && activeOffers.length === 0 && (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No offers available</Text>
              <Text style={styles.emptySubtext}>Check back later for new deals</Text>
            </View>
          )}

          {activeOffers.slice(0, 3).map((offer) => (
            <ListItem
              key={offer.id}
              title={offer.title || offer.name || 'Special Offer'}
              subtitle={offer.description || 'Limited time offer'}
              value={
                offer.endTime
                  ? `Expires ${new Date(offer.endTime).toLocaleDateString()}`
                  : 'Limited time'
              }
              onPress={() => handleOfferPress(offer.id)}
              showChevron
            />
          ))}
        </View>

        {/* Nearby Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Nearby</Text>
          
          <ListItem
            title="Local Gym Membership"
            subtitle="50% off first month"
            value="0.2 miles away"
            onPress={() => handleOfferPress('gym-membership')}
            showChevron
          />
          
          <ListItem
            title="Restaurant Week"
            subtitle="Special 3-course menu"
            value="0.5 miles away"
            onPress={() => handleOfferPress('restaurant-week')}
            showChevron
          />
        </View>

        {/* Categories */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Categories</Text>
          
          <ListItem
            title="Food & Dining"
            onPress={() => handleOfferPress('category-food')}
            showChevron
          />
          
          <ListItem
            title="Shopping"
            onPress={() => handleOfferPress('category-shopping')}
            showChevron
          />
          
          <ListItem
            title="Entertainment"
            onPress={() => handleOfferPress('category-entertainment')}
            showChevron
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#C6C6C8',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
  },
  subtitle: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 4,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F2F2F7',
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#8E8E93',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
  },
});
