/**
 * V7 Installation Redux Slice Tests
 * 
 * Comprehensive tests for Redux Toolkit slice including action creators, reducers,
 * async thunks, state transitions, and error handling
 */

import { configureStore } from '@reduxjs/toolkit';
import installationReducer, {
  updateDevice,
  updateCarrier,
  updateCapabilities,
  updateCapability,
  updateGeo,
  updatePayments,
  setError,
  clearError,
  setLoading,
  initializeInstallation,
  setNotificationToken,
} from '../store/installationSlice';
import {
  InstallationState,
  CarrierInfo,
  DeviceInfo,
  DeviceCapability,
  SetTokenPayload,
  UpdateGeoPayload,
  InstallationErrorCodes,
  CapabilityTypes,
  NotificationProviders,
} from '../types';

// Mock service layer
jest.mock('../services/InstallationService', () => ({
  getDeviceInfo: jest.fn(),
  getAppInfo: jest.fn(),
  getCarrierInfo: jest.fn(),
  getLocaleInfo: jest.fn(),
  detectCapabilities: jest.fn(),
  persistInstallationState: jest.fn(),
  getPersistedInstallationState: jest.fn(),
  isNetworkError: jest.fn(),
  detectCarrierChange: jest.fn(),
  shouldClearNotificationsForAndroid13: jest.fn(),
}));

// =============================================================================
// TEST STORE SETUP
// =============================================================================

function createTestStore(initialState?: Partial<InstallationState>) {
  return configureStore({
    reducer: {
      installation: installationReducer,
    },
    preloadedState: {
      installation: {
        id: '',
        device: null,
        os: null,
        app: null,
        carrier: null,
        locale: null,
        geo: null,
        capabilities: [],
        tokens: {},
        permissions: [],
        payments: {},
        appList: [],
        isInitialized: false,
        lastUpdated: null,
        error: null,
        isLoading: false,
        ...initialState,
      },
    },
  });
}

// =============================================================================
// MOCK DATA
// =============================================================================

const mockDeviceInfo: DeviceInfo = {
  uuid: 'test-device-123',
  name: 'Test iPhone',
  brand: 'Apple',
  manufacturer: 'Apple Inc.',
  model: 'iPhone 15 Pro',
  simulator: false,
  ip: '*************',
};

const mockCarrierInfo: CarrierInfo = {
  name: 'Verizon',
  country: 'US',
  mobileCountryCode: '310',
};

const mockCapabilities: DeviceCapability[] = [
  { name: CapabilityTypes.NFC, support: true },
  { name: CapabilityTypes.BIOMETRICS, support: ['FaceID'] },
  { name: CapabilityTypes.PAYMENT, support: ['ApplePay'] },
];

// =============================================================================
// REDUX SLICE TESTS
// =============================================================================

describe('Installation Redux Slice Tests', () => {
  
  // =============================================================================
  // INITIAL STATE TESTS
  // =============================================================================
  
  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const store = createTestStore();
      const state = store.getState().installation;
      
      expect(state.id).toBe('');
      expect(state.device).toBeNull();
      expect(state.os).toBeNull();
      expect(state.app).toBeNull();
      expect(state.carrier).toBeNull();
      expect(state.locale).toBeNull();
      expect(state.geo).toBeNull();
      expect(state.capabilities).toEqual([]);
      expect(state.tokens).toEqual({});
      expect(state.permissions).toEqual([]);
      expect(state.payments).toEqual({});
      expect(state.appList).toEqual([]);
      expect(state.isInitialized).toBe(false);
      expect(state.lastUpdated).toBeNull();
      expect(state.error).toBeNull();
      expect(state.isLoading).toBe(false);
    });
  });
  
  // =============================================================================
  // SYNCHRONOUS ACTION TESTS
  // =============================================================================
  
  describe('Synchronous Actions', () => {
    test('should update device info', () => {
      const store = createTestStore();
      
      store.dispatch(updateDevice(mockDeviceInfo));
      
      const state = store.getState().installation;
      expect(state.device).toEqual(mockDeviceInfo);
      expect(state.lastUpdated).toBeTruthy();
    });
    
    test('should update carrier with change detection', () => {
      const store = createTestStore({
        carrier: { name: 'AT&T', country: 'US', mobileCountryCode: '310' },
      });
      
      store.dispatch(updateCarrier(mockCarrierInfo));
      
      const state = store.getState().installation;
      expect(state.carrier?.name).toBe('Verizon');
      expect(state.carrier?.changed).toBe(true);
      expect(state.carrier?.triggerBusinessCascade).toBe(true);
      expect(state.lastUpdated).toBeTruthy();
    });
    
    test('should not detect carrier change when same carrier', () => {
      const store = createTestStore({
        carrier: mockCarrierInfo,
      });
      
      store.dispatch(updateCarrier(mockCarrierInfo));
      
      const state = store.getState().installation;
      expect(state.carrier?.changed).toBe(false);
      expect(state.carrier?.triggerBusinessCascade).toBeUndefined();
    });
    
    test('should update all capabilities', () => {
      const store = createTestStore();
      
      store.dispatch(updateCapabilities(mockCapabilities));
      
      const state = store.getState().installation;
      expect(state.capabilities).toEqual(mockCapabilities);
      expect(state.lastUpdated).toBeTruthy();
    });
    
    test('should update single capability', () => {
      const store = createTestStore({
        capabilities: [{ name: CapabilityTypes.NFC, support: false }],
      });
      
      store.dispatch(updateCapability({ name: CapabilityTypes.NFC, support: true }));
      
      const state = store.getState().installation;
      expect(state.capabilities).toHaveLength(1);
      expect(state.capabilities[0].support).toBe(true);
    });
    
    test('should add new capability', () => {
      const store = createTestStore({
        capabilities: [{ name: CapabilityTypes.NFC, support: true }],
      });
      
      store.dispatch(updateCapability({ name: CapabilityTypes.BIOMETRICS, support: ['FaceID'] }));
      
      const state = store.getState().installation;
      expect(state.capabilities).toHaveLength(2);
      expect(state.capabilities[1]).toEqual({
        name: CapabilityTypes.BIOMETRICS,
        support: ['FaceID'],
      });
    });
    
    test('should update geo location', () => {
      const store = createTestStore();
      const geoPayload: UpdateGeoPayload = { latitude: 37.7749, longitude: -122.4194 };
      
      store.dispatch(updateGeo(geoPayload));
      
      const state = store.getState().installation;
      expect(state.geo).toEqual({
        type: 'Point',
        coordinates: [-122.4194, 37.7749], // [longitude, latitude]
      });
    });
    
    test('should update payments', () => {
      const store = createTestStore();
      const payments = { applePay: { methods: ['credit', 'debit'] } };
      
      store.dispatch(updatePayments(payments));
      
      const state = store.getState().installation;
      expect(state.payments).toEqual(payments);
    });
    
    test('should set and clear errors', () => {
      const store = createTestStore();
      const error = {
        code: InstallationErrorCodes.NETWORK_ERROR,
        message: 'Network connection failed',
      };
      
      store.dispatch(setError(error));
      let state = store.getState().installation;
      expect(state.error).toBe(error.message);
      expect(state.isLoading).toBe(false);
      
      store.dispatch(clearError());
      state = store.getState().installation;
      expect(state.error).toBeNull();
    });
    
    test('should set loading state', () => {
      const store = createTestStore();
      
      store.dispatch(setLoading(true));
      let state = store.getState().installation;
      expect(state.isLoading).toBe(true);
      
      store.dispatch(setLoading(false));
      state = store.getState().installation;
      expect(state.isLoading).toBe(false);
    });
  });
  
  // =============================================================================
  // ASYNC THUNK TESTS
  // =============================================================================
  
  describe('Async Thunks', () => {
    // Mock the helper functions
    beforeEach(() => {
      // Mock implementation functions
      global.restoreCachedState = jest.fn().mockResolvedValue({});
      global.initializeDevice = jest.fn().mockResolvedValue(mockDeviceInfo);
      global.initializeApp = jest.fn().mockResolvedValue({});
      global.initializeCarrier = jest.fn().mockResolvedValue(mockCarrierInfo);
      global.initializeLocale = jest.fn().mockResolvedValue({});
      global.initializeCapabilities = jest.fn().mockResolvedValue(mockCapabilities);
      global.initializeTokens = jest.fn().mockResolvedValue({});
      global.initializePermissions = jest.fn().mockResolvedValue([]);
      global.initializePayments = jest.fn().mockResolvedValue({});
      global.persistInstallationState = jest.fn().mockResolvedValue(undefined);
      global.syncTokenWithBackend = jest.fn().mockResolvedValue(undefined);
      global.isNetworkError = jest.fn().mockReturnValue(false);
    });
    
    test('should handle initializeInstallation success', async () => {
      const store = createTestStore();
      
      const action = await store.dispatch(initializeInstallation());
      
      expect(action.type).toBe('installation/initialize/fulfilled');
      
      const state = store.getState().installation;
      expect(state.isInitialized).toBe(true);
      expect(state.device).toEqual(mockDeviceInfo);
      expect(state.carrier).toEqual(mockCarrierInfo);
      expect(state.capabilities).toEqual(mockCapabilities);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });
    
    test('should handle initializeInstallation failure', async () => {
      global.initializeDevice = jest.fn().mockRejectedValue(new Error('Device initialization failed'));
      
      const store = createTestStore();
      
      const action = await store.dispatch(initializeInstallation());
      
      expect(action.type).toBe('installation/initialize/rejected');
      
      const state = store.getState().installation;
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeTruthy();
    });
    
    test('should handle setNotificationToken with deduplication', async () => {
      const store = createTestStore({
        tokens: {
          [NotificationProviders.FCM]: {
            token: 'existing-token',
            modifiedAt: '2024-01-01T00:00:00Z',
          },
        },
      });
      
      const tokenPayload: SetTokenPayload = {
        provider: NotificationProviders.FCM,
        token: 'existing-token', // Same token
      };
      
      const action = await store.dispatch(setNotificationToken(tokenPayload));
      
      expect(action.type).toBe('installation/setToken/fulfilled');
      expect(action.payload).toBeNull(); // No change needed
    });
    
    test('should handle setNotificationToken with new token', async () => {
      const store = createTestStore();
      
      const tokenPayload: SetTokenPayload = {
        provider: NotificationProviders.FCM,
        token: 'new-token-123',
      };
      
      const action = await store.dispatch(setNotificationToken(tokenPayload));
      
      expect(action.type).toBe('installation/setToken/fulfilled');
      
      const state = store.getState().installation;
      expect(state.tokens[NotificationProviders.FCM]).toBeDefined();
      expect(state.tokens[NotificationProviders.FCM].token).toBe('new-token-123');
      expect(state.notifyToken).toBe('new-token-123');
    });
    
    test('should handle setNotificationToken network error', async () => {
      global.syncTokenWithBackend = jest.fn().mockRejectedValue(new Error('Network Error'));
      global.isNetworkError = jest.fn().mockReturnValue(true);
      
      const store = createTestStore();
      
      const tokenPayload: SetTokenPayload = {
        provider: NotificationProviders.FCM,
        token: 'new-token-123',
      };
      
      const action = await store.dispatch(setNotificationToken(tokenPayload));
      
      expect(action.type).toBe('installation/setToken/rejected');
      
      const state = store.getState().installation;
      expect(state.error).toBeTruthy();
    });
  });
  
  // =============================================================================
  // STATE TRANSITION TESTS
  // =============================================================================
  
  describe('State Transitions', () => {
    test('should handle loading states correctly', async () => {
      const store = createTestStore();
      
      // Start async operation
      const promise = store.dispatch(initializeInstallation());
      
      // Check pending state
      let state = store.getState().installation;
      expect(state.isLoading).toBe(true);
      expect(state.error).toBeNull();
      
      // Wait for completion
      await promise;
      
      // Check fulfilled state
      state = store.getState().installation;
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });
    
    test('should maintain state immutability', () => {
      const store = createTestStore();
      const initialState = store.getState().installation;
      
      store.dispatch(updateDevice(mockDeviceInfo));
      
      const newState = store.getState().installation;
      expect(newState).not.toBe(initialState);
      expect(newState.device).toEqual(mockDeviceInfo);
    });
  });
});
