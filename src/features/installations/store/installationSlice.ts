// V7 Installation Redux Slice - Pure Redux Toolkit Implementation
// Business Logic Extracted from V6 src/lib/common/installation.js

import { Platform } from 'react-native';
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import {
  InstallationState,
  CarrierInfo,
  DeviceInfo,
  DeviceCapability,
  SetTokenPayload,
  UpdateGeoPayload,
  InstallationError,
  InstallationErrorCodes,
  NotificationToken,
} from '../types/index';
import * as InstallationService from '../services/InstallationService';

// Initial State
const initialState: InstallationState = {
  id: '',
  device: null,
  os: null,
  app: null,
  carrier: null,
  locale: null,
  geo: null,
  capabilities: [],
  tokens: {},
  permissions: [],
  payments: {},
  appList: [],
  isInitialized: false,
  lastUpdated: null,
  error: null,
  isLoading: false,
};

// Async Thunks for Complex Operations

export const initializeInstallation = createAsyncThunk(
  'installation/initialize',
  async (_, { rejectWithValue }) => {
    try {
      // V6 Business Logic: Sequential restoration, parallel initialization
      const cachedState = await restoreCachedState();
      
      // Initialize all components in parallel (V6 pattern)
      const [device, app, carrier, locale, capabilities, tokens, permissions, payments] = 
        await Promise.all([
          initializeDevice(),
          initializeApp(),
          initializeCarrier(),
          initializeLocale(),
          initializeCapabilities(),
          initializeTokens(),
          initializePermissions(),
          initializePayments(),
        ]);
      
      const finalState = {
        ...cachedState,
        device,
        app,
        carrier,
        locale,
        capabilities,
        tokens,
        permissions,
        payments,
        isInitialized: true,
        lastUpdated: new Date().toISOString(),
      };
      
      // Persist final state (V6 pattern)
      await persistInstallationState(finalState);
      
      return finalState;
    } catch (error) {
      return rejectWithValue({
        code: InstallationErrorCodes.INITIALIZATION_FAILED,
        message: error instanceof Error ? error.message : 'Unknown error',
        context: error,
      });
    }
  }
);

export const setNotificationToken = createAsyncThunk(
  'installation/setToken',
  async ({ provider, token }: SetTokenPayload, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { installation: InstallationState };
      const currentTokens = state.installation.tokens;
      
      // V6 Business Logic: Deduplication check
      if (currentTokens[provider]?.token === token) {
        return null; // No change needed
      }
      
      const tokenData: NotificationToken = {
        token,
        modifiedAt: new Date().toISOString(),
      };
      
      // V6 Business Logic: Sync with backend
      await syncTokenWithBackend(provider, tokenData);
      
      return { provider, tokenData };
    } catch (error) {
      // V6 Business Logic: Defer sync on network errors
      if (isNetworkError(error)) {
        // TODO: Implement deferred sync mechanism
        console.log('[Installation] Deferring token sync due to network error');
      }
      
      return rejectWithValue({
        code: InstallationErrorCodes.TOKEN_SYNC_FAILED,
        message: error instanceof Error ? error.message : 'Token sync failed',
        context: { provider, token },
      });
    }
  }
);

// Main Slice
const installationSlice = createSlice({
  name: 'installation',
  initialState,
  reducers: {
    // V6 Business Logic: Device initialization with Android 13+ handling
    updateDevice: (state, action: PayloadAction<DeviceInfo>) => {
      const newDevice = action.payload;
      
      // V6 Business Logic: Android 13+ notification cleanup
      if (state.os && state.os.version !== Platform.Version.toString()) {
        if (parseFloat(state.os.version) < 13 && Platform.OS === 'android' && Platform.Version >= 33) {
          // Trigger Android 13+ cleanup (handled by middleware)
          state.device = { ...newDevice, requiresAndroid13Cleanup: true };
        } else {
          state.device = newDevice;
        }
      } else {
        state.device = newDevice;
      }
      
      state.lastUpdated = new Date().toISOString();
    },

    // V6 Business Logic: Carrier change detection with multi-factor validation
    updateCarrier: (state, action: PayloadAction<CarrierInfo>) => {
      const oldCarrier = state.carrier;
      const newCarrier = action.payload;
      
      // V6 Business Logic: Carrier change detection
      const carrierChanged = detectCarrierChange(oldCarrier, newCarrier);
      
      state.carrier = {
        ...newCarrier,
        changed: carrierChanged,
      };
      
      // If carrier changed, trigger business cascade (handled by middleware)
      if (carrierChanged) {
        state.carrier.triggerBusinessCascade = true;
      }
      
      state.lastUpdated = new Date().toISOString();
    },

    // V6 Business Logic: Progressive capability discovery
    updateCapabilities: (state, action: PayloadAction<DeviceCapability[]>) => {
      state.capabilities = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    updateCapability: (state, action: PayloadAction<{ name: string; support: any }>) => {
      const { name, support } = action.payload;
      const existingIndex = state.capabilities.findIndex(cap => cap.name === name);
      
      if (existingIndex !== -1) {
        state.capabilities[existingIndex].support = support;
      } else {
        state.capabilities.push({ name, support });
      }
      
      state.lastUpdated = new Date().toISOString();
    },

    // V6 Business Logic: Geo location update
    updateGeo: (state, action: PayloadAction<UpdateGeoPayload>) => {
      const { latitude, longitude } = action.payload;
      state.geo = {
        type: 'Point',
        coordinates: [longitude, latitude],
      };
      state.lastUpdated = new Date().toISOString();
    },

    // V6 Business Logic: Payment methods update
    updatePayments: (state, action: PayloadAction<any>) => {
      state.payments = action.payload;
      state.lastUpdated = new Date().toISOString();
    },

    // Error handling
    setError: (state, action: PayloadAction<InstallationError>) => {
      state.error = action.payload.message;
      state.isLoading = false;
    },

    clearError: (state) => {
      state.error = null;
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Initialize Installation
      .addCase(initializeInstallation.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(initializeInstallation.fulfilled, (state, action) => {
        Object.assign(state, action.payload);
        state.isLoading = false;
        state.error = null;
      })
      .addCase(initializeInstallation.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as any)?.message || 'Initialization failed';
      })
      
      // Set Notification Token
      .addCase(setNotificationToken.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(setNotificationToken.fulfilled, (state, action) => {
        if (action.payload) {
          const { provider, tokenData } = action.payload;
          state.tokens[provider] = tokenData;
          state.notifyToken = tokenData.token; // For deduplication
        }
        state.isLoading = false;
        state.error = null;
      })
      .addCase(setNotificationToken.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as any)?.message || 'Token sync failed';
      });
  },
});

// V6 Business Logic Functions (Pure Functions)

// V6 Carrier Change Detection Logic
function detectCarrierChange(oldCarrier: CarrierInfo | null, newCarrier: CarrierInfo): boolean {
  // V6 logic: Check for meaningful carrier changes
  if (!oldCarrier || newCarrier.name === '--') return false;

  // V6 treats empty string as "no carrier" but still detects change to valid carrier
  if (oldCarrier.name === '' && newCarrier.name !== '' && newCarrier.name !== '--') {
    return true;
  }

  // Normal case: different non-empty carriers
  return oldCarrier.name !== '' && oldCarrier.name !== newCarrier.name;
}

// Helper Functions (to be implemented)
async function restoreCachedState(): Promise<Partial<InstallationState>> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).restoreCachedState) {
    return (global as any).restoreCachedState();
  }
  return InstallationService.getPersistedInstallationState() || {};
}

async function initializeDevice(): Promise<DeviceInfo> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).initializeDevice) {
    return (global as any).initializeDevice();
  }
  return InstallationService.getDeviceInfo();
}

async function initializeApp(): Promise<any> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).initializeApp) {
    return (global as any).initializeApp();
  }
  return InstallationService.getAppInfo();
}

async function initializeCarrier(): Promise<CarrierInfo> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).initializeCarrier) {
    return (global as any).initializeCarrier();
  }
  return InstallationService.getCarrierInfo();
}

async function initializeLocale(): Promise<any> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).initializeLocale) {
    return (global as any).initializeLocale();
  }
  return InstallationService.getLocaleInfo();
}

async function initializeCapabilities(): Promise<DeviceCapability[]> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).initializeCapabilities) {
    return (global as any).initializeCapabilities();
  }
  return InstallationService.detectCapabilities();
}

async function initializeTokens(): Promise<any> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).initializeTokens) {
    return (global as any).initializeTokens();
  }

  try {
    // V6 Business Logic: Initialize notification tokens during installation
    const { initializeNotificationForInstallation } = await import('../../notifications/services/NotificationIntegration');

    const { provider, token } = await initializeNotificationForInstallation();

    if (token) {
      return {
        [provider]: {
          token: token.token,
          modifiedAt: token.modifiedAt,
        }
      };
    }

    return {};
  } catch (error) {
    console.warn('Failed to initialize notification tokens:', error);
    return {};
  }
}

async function initializePermissions(): Promise<any[]> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).initializePermissions) {
    return (global as any).initializePermissions();
  }
  // TODO: Implement permission initialization
  return [];
}

async function initializePayments(): Promise<any> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).initializePayments) {
    return (global as any).initializePayments();
  }
  // TODO: Implement payment initialization
  return {};
}

async function persistInstallationState(state: any): Promise<void> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).persistInstallationState) {
    return (global as any).persistInstallationState(state);
  }
  InstallationService.persistInstallationState(state);
}

async function syncTokenWithBackend(provider: string, tokenData: NotificationToken): Promise<void> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).syncTokenWithBackend) {
    return (global as any).syncTokenWithBackend(provider, tokenData);
  }
  // TODO: Implement backend sync - will be handled by network service
}

function isNetworkError(error: any): boolean {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && (global as any).isNetworkError) {
    return (global as any).isNetworkError(error);
  }
  return InstallationService.isNetworkError(error);
}

export const {
  updateDevice,
  updateCarrier,
  updateCapabilities,
  updateCapability,
  updateGeo,
  updatePayments,
  setError,
  clearError,
  setLoading,
} = installationSlice.actions;

export default installationSlice.reducer;
