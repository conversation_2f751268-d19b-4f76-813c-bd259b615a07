// V7 Installation System Types - Extracted from V6 Business Rules

export interface DeviceInfo {
  uuid: string;
  name: string;
  brand: string;
  manufacturer: string;
  model: string;
  simulator: boolean;
  ip?: string;
  requiresAndroid13Cleanup?: boolean;
}

export interface OSInfo {
  name: string;
  version: string;
}

export interface AppInfo {
  id: string;
  version: string;
  build: string;
  environment: string;
  bundleId: string;
}

export interface CarrierInfo {
  name: string;
  country: string;
  mobileCountryCode: string;
  changed?: boolean;
  triggerBusinessCascade?: boolean;
}

export interface LocaleInfo {
  languages: string[];
  country: string;
  currency: string;
  timezone: string;
}

export interface DeviceCapability {
  name: string;
  support: any;
}

export interface DeviceCapabilities {
  payment: any[];
  biometrics: string[];
  connectivity: string[];
}

export interface NotificationToken {
  token: string;
  modifiedAt: string;
}

export interface TokenRegistry {
  [provider: string]: NotificationToken;
}

export interface GeoLocation {
  type: 'Point';
  coordinates: [number, number]; // [longitude, latitude]
}

export interface PaymentMethods {
  [type: string]: {
    methods?: any[];
  };
}

export interface InstallationState {
  // Core device information
  id: string;
  device: DeviceInfo | null;
  os: OSInfo | null;
  app: AppInfo | null;
  
  // Network and carrier information
  carrier: CarrierInfo | null;
  locale: LocaleInfo | null;
  geo: GeoLocation | null;
  
  // Device capabilities
  capabilities: DeviceCapability[];
  
  // Notification tokens
  tokens: TokenRegistry;
  notifyToken?: string; // Current active token for deduplication
  
  // Permissions (managed by permissions slice)
  permissions: any[]; // Will be managed by permissions slice
  
  // Payment capabilities
  payments: PaymentMethods;
  
  // App list for capability detection
  appList: any[];
  
  // State management
  isInitialized: boolean;
  lastUpdated: string | null;
  
  // Error handling
  error: string | null;
  isLoading: boolean;
}

// Business Logic Interfaces

export interface CarrierChangeDetection {
  detectChange(oldCarrier: CarrierInfo | null, newCarrier: CarrierInfo): boolean;
  triggerBusinessCascade(carrierInfo: CarrierInfo): void;
}

export interface Android13Compliance {
  detectOSUpgrade(cachedVersion: string, currentVersion: string): boolean;
  clearNotificationsOnUpgrade(): Promise<void>;
}

export interface CapabilityDetection {
  detectAllCapabilities(): Promise<DeviceCapabilities>;
  updateCapability(name: string, support: any): void;
  getCapability(name: string): DeviceCapability | null;
}

export interface TokenManagement {
  setToken(provider: string, token: string): Promise<void>;
  hasTokenChanged(provider: string, token: string): boolean;
  deferSyncOnError(tokens: TokenRegistry): void;
  getToken(provider: string): NotificationToken | null;
}

export interface InstallationInitialization {
  initializeInstallation(): Promise<InstallationState>;
  restoreCachedState(): Promise<Partial<InstallationState>>;
  initializeAllComponents(): Promise<ComponentStates>;
}

export interface ComponentStates {
  device: DeviceInfo;
  app: AppInfo;
  carrier: CarrierInfo;
  locale: LocaleInfo;
  capabilities: DeviceCapability[];
  tokens: TokenRegistry;
  permissions: any[];
  payments: PaymentMethods;
}

// Action Payloads

export interface UpdateDevicePayload {
  device: Partial<DeviceInfo>;
}

export interface UpdateCarrierPayload {
  carrier: CarrierInfo;
}

export interface UpdateCapabilitiesPayload {
  capabilities: DeviceCapability[];
}

export interface SetTokenPayload {
  provider: string;
  token: string;
}

export interface UpdateGeoPayload {
  latitude: number;
  longitude: number;
}

export interface InitializeInstallationPayload {
  cachedState?: Partial<InstallationState>;
}

// Error Types

export interface InstallationError {
  code: string;
  message: string;
  context?: any;
}

export const InstallationErrorCodes = {
  NETWORK_ERROR: 'Network Error',
  TIMEOUT: 'Timeout',
  REQUEST_FAILED: 'Request failed',
  INITIALIZATION_FAILED: 'Initialization failed',
  CARRIER_DETECTION_FAILED: 'Carrier detection failed',
  CAPABILITY_DETECTION_FAILED: 'Capability detection failed',
  TOKEN_SYNC_FAILED: 'Token sync failed',
} as const;

// Constants

export const CapabilityTypes = {
  PAYMENT: 'payment',
  BIOMETRICS: 'biometrics',
  CONNECTIVITY: 'connectivity',
  NFC: 'nfc',
  FACEID: 'faceid',
  TOUCHID: 'touchid',
} as const;

export const NotificationProviders = {
  APNS: 'apns',
  FCM: 'fcm',
  JPUSH: 'jpush',
} as const;

// Utility Types

export type CapabilityType = typeof CapabilityTypes[keyof typeof CapabilityTypes];
export type NotificationProvider = typeof NotificationProviders[keyof typeof NotificationProviders];
export type InstallationErrorCode = typeof InstallationErrorCodes[keyof typeof InstallationErrorCodes];
