/**
 * Notification Demo Screen
 * Modern, elegant interface for testing notification features
 */

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  SafeAreaView,
  Alert,
  Switch,
  TextInput,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { useAppSelector, useAppDispatch } from '../../core/store/hooks';
import { Button, ListItem } from '../../shared/ui';

// Import notification actions and selectors
import {
  initializeNotifications,
  refreshNotificationToken,
  processNotification,
  updateCardBadge,
  clearHistory,
  setSettings,
} from './store/notificationsSlice';

// Import notification services
import * as NotificationService from './services/NotificationService';
import { NotificationProvider, NotificationPayload } from './types';
import notifee, { AuthorizationStatus } from '@notifee/react-native';

export const NotificationDemoScreen: React.FC = () => {
  const dispatch = useAppDispatch();
  const notificationState = useAppSelector(state => state.notifications);
  const installationState = useAppSelector(state => state.installation);
  
  const [customTitle, setCustomTitle] = useState('Test Notification');
  const [customBody, setCustomBody] = useState('This is a test notification from the demo screen');
  const [testCounter, setTestCounter] = useState(1);
  const [enableSound, setEnableSound] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<string>('unknown');
  const [permissionSettings, setPermissionSettings] = useState<any>(null);

  useEffect(() => {
    console.log('🔔 NotificationDemoScreen mounted');
    console.log('📊 Notification state:', {
      isInitialized: notificationState.isInitialized,
      activeProvider: notificationState.activeProvider,
      hasToken: !!notificationState.currentToken,
      historyCount: notificationState.history.length,
      error: notificationState.error,
    });
    console.log('⚙️ Installation state:', {
      isInitialized: installationState.isInitialized,
      hasTokens: Object.keys(installationState.tokens).length > 0,
    });

    // Check initial permission status
    checkPermissionStatus();
  }, [notificationState, installationState]);

  // Permission Functions
  const checkPermissionStatus = async () => {
    try {
      const settings = await notifee.getNotificationSettings();
      setPermissionStatus(settings.authorizationStatus.toString());
      setPermissionSettings(settings);
      console.log('📋 Permission settings:', settings);
    } catch (error) {
      console.error('Failed to get permission settings:', error);
      setPermissionStatus('error');
    }
  };

  const handleRequestPermissions = async () => {
    try {
      const settings = await notifee.requestPermission();
      setPermissionStatus(settings.authorizationStatus.toString());
      setPermissionSettings(settings);

      const statusText = getPermissionStatusText(settings.authorizationStatus);
      Alert.alert(
        'Permission Request Result',
        `Status: ${statusText}\n\nYou can now test notifications!`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Permission Request Failed',
        error instanceof Error ? error.message : 'Unknown error',
        [{ text: 'OK' }]
      );
    }
  };

  const handleOpenSettings = async () => {
    try {
      await notifee.openNotificationSettings();
    } catch (error) {
      Alert.alert(
        'Cannot Open Settings',
        'Unable to open notification settings. Please open Settings app manually.',
        [{ text: 'OK' }]
      );
    }
  };

  const getPermissionStatusText = (status: AuthorizationStatus): string => {
    switch (status) {
      case AuthorizationStatus.AUTHORIZED:
        return 'Authorized';
      case AuthorizationStatus.DENIED:
        return 'Denied';
      case AuthorizationStatus.NOT_DETERMINED:
        return 'Not Determined';
      case AuthorizationStatus.PROVISIONAL:
        return 'Provisional';
      default:
        return 'Unknown';
    }
  };

  const getPermissionStatusColor = (status: string): string => {
    switch (status) {
      case '1': // AUTHORIZED
        return '#34C759';
      case '2': // DENIED
        return '#FF3B30';
      case '0': // NOT_DETERMINED
        return '#FF9500';
      case '3': // PROVISIONAL
        return '#007AFF';
      default:
        return '#8E8E93';
    }
  };

  // Test Functions
  const handleInitializeNotifications = async () => {
    try {
      const result = await dispatch(initializeNotifications({
        platform: 'mobile',
        region: 'US'
      })).unwrap();
      
      Alert.alert(
        'Initialization Success',
        `Provider: ${result.provider}\nInitialized: ${result.initialized}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Initialization Failed',
        error instanceof Error ? error.message : 'Unknown error',
        [{ text: 'OK' }]
      );
    }
  };

  const handleRefreshToken = async () => {
    if (!notificationState.activeProvider) {
      Alert.alert('Error', 'No active provider. Initialize notifications first.');
      return;
    }

    try {
      const result = await dispatch(refreshNotificationToken(notificationState.activeProvider)).unwrap();
      Alert.alert(
        'Token Refreshed',
        `New token: ${result.token?.token?.substring(0, 20) || 'Unknown'}...`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Token Refresh Failed',
        error instanceof Error ? error.message : 'Unknown error',
        [{ text: 'OK' }]
      );
    }
  };

  const handleDisplayLocalNotification = async () => {
    try {
      // Check permissions first
      const settings = await notifee.getNotificationSettings();
      if (settings.authorizationStatus !== AuthorizationStatus.AUTHORIZED) {
        Alert.alert(
          'Permission Required',
          'Notifications are not authorized. Please grant permission first.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Request Permission', onPress: handleRequestPermissions }
          ]
        );
        return;
      }

      const notification: NotificationPayload = {
        id: `demo_${Date.now()}`,
        title: customTitle,
        body: customBody,
        data: {
          source: 'demo',
          counter: testCounter,
          timestamp: new Date().toISOString(),
        },
        channelId: 'Perkd',
        priority: 'high',
        showInForeground: true,
        // Only include sound if enabled and use a valid string
        ...(enableSound && { sound: 'default' }),
      };

      await NotificationService.displayNotification(notification);
      setTestCounter(prev => prev + 1);

      Alert.alert(
        'Local Notification Sent',
        `Check your notification panel${enableSound ? ' (with sound)' : ' (silent)'}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Display Failed',
        error instanceof Error ? error.message : 'Unknown error',
        [{ text: 'OK' }]
      );
    }
  };

  const handleProcessTestNotification = async () => {
    try {
      const testNotification = {
        id: `processed_${Date.now()}`,
        title: 'Processed Test Notification',
        subtitle: 'Demo Processing',
        body: 'This notification was processed through Redux',
        data: {
          sync: 'cache',
          action: 'test',
          nav: { screen: 'Wallet' },
          options: { banner: true },
        },
      };

      await dispatch(processNotification({
        notification: testNotification,
        tapped: false,
        inApp: true,
      })).unwrap();

      Alert.alert(
        'Notification Processed',
        'Check the notification history below',
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'Processing Failed',
        error instanceof Error ? error.message : 'Unknown error',
        [{ text: 'OK' }]
      );
    }
  };

  const handleUpdateBadge = () => {
    const newCount = Math.floor(Math.random() * 10) + 1;
    dispatch(updateCardBadge({
      cardId: 'demo-card',
      count: newCount,
    }));

    Alert.alert(
      'Badge Updated',
      `Demo card badge set to ${newCount}`,
      [{ text: 'OK' }]
    );
  };

  const handleClearHistory = () => {
    dispatch(clearHistory());
    Alert.alert('History Cleared', 'Notification history has been cleared', [{ text: 'OK' }]);
  };

  const handleGetCurrentToken = async () => {
    if (!notificationState.activeProvider) {
      Alert.alert('Error', 'No active provider. Initialize notifications first.');
      return;
    }

    try {
      const token = await NotificationService.getNotificationToken(notificationState.activeProvider);
      if (token) {
        Alert.alert(
          'Current Token',
          `${token.substring(0, 50)}...`,
          [{ text: 'Copy', onPress: () => console.log('Full token:', token) }, { text: 'OK' }]
        );
      } else {
        Alert.alert('No Token', 'No notification token available');
      }
    } catch (error) {
      Alert.alert(
        'Token Retrieval Failed',
        error instanceof Error ? error.message : 'Unknown error',
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Modern Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Notifications</Text>
          <Text style={styles.subtitle}>Test & manage notification features</Text>

          {/* Permission Status Card */}
          <View style={styles.statusCard}>
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Permission Status</Text>
              <View style={[
                styles.statusBadge,
                { backgroundColor: getPermissionStatusColor(permissionStatus) }
              ]}>
                <Text style={styles.statusBadgeText}>
                  {getPermissionStatusText(parseInt(permissionStatus) || 0)}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Quick Actions Card */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Quick Actions</Text>

          <View style={styles.actionGrid}>
            <TouchableOpacity
              style={[styles.actionButton, styles.primaryAction]}
              onPress={handleRequestPermissions}
            >
              <Text style={styles.actionIcon}>🔔</Text>
              <Text style={[styles.actionText, { color: '#FFFFFF' }]}>Request Permissions</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryAction]}
              onPress={checkPermissionStatus}
            >
              <Text style={styles.actionIcon}>🔍</Text>
              <Text style={styles.actionText}>Check Status</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryAction]}
              onPress={handleOpenSettings}
            >
              <Text style={styles.actionIcon}>⚙️</Text>
              <Text style={styles.actionText}>Open Settings</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryAction]}
              onPress={async () => {
                try {
                  const settings = await notifee.getNotificationSettings();
                  const isAuthorized = settings.authorizationStatus === AuthorizationStatus.AUTHORIZED;

                  Alert.alert(
                    'Permission Test',
                    isAuthorized
                      ? '✅ Notifications authorized!'
                      : '❌ Please grant permissions',
                    [{ text: 'OK' }]
                  );
                } catch (error) {
                  Alert.alert('Test Failed', 'Could not check permissions', [{ text: 'OK' }]);
                }
              }}
            >
              <Text style={styles.actionIcon}>✅</Text>
              <Text style={styles.actionText}>Quick Test</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* System Status Card */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>System Status</Text>

          <View style={styles.statusGrid}>
            <View style={styles.statusItem}>
              <Text style={styles.statusValue}>
                {notificationState.isInitialized ? '✅' : '❌'}
              </Text>
              <Text style={styles.statusLabel}>Notifications</Text>
            </View>

            <View style={styles.statusItem}>
              <Text style={styles.statusValue}>
                {notificationState.activeProvider ? '✅' : '❌'}
              </Text>
              <Text style={styles.statusLabel}>Provider</Text>
            </View>

            <View style={styles.statusItem}>
              <Text style={styles.statusValue}>
                {notificationState.currentToken ? '✅' : '❌'}
              </Text>
              <Text style={styles.statusLabel}>Token</Text>
            </View>

            <View style={styles.statusItem}>
              <Text style={styles.statusValue}>
                {installationState.isInitialized ? '✅' : '❌'}
              </Text>
              <Text style={styles.statusLabel}>Installation</Text>
            </View>
          </View>

          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.modernButton, styles.primaryButton]}
              onPress={handleInitializeNotifications}
              disabled={notificationState.isLoading}
            >
              <Text style={styles.primaryButtonText}>Initialize</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.modernButton, styles.secondaryButton]}
              onPress={handleRefreshToken}
              disabled={!notificationState.activeProvider || notificationState.isLoading}
            >
              <Text style={styles.secondaryButtonText}>Refresh Token</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Test Notification Card */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Test Notification</Text>

          <View style={styles.inputGroup}>
            <TextInput
              style={styles.modernInput}
              value={customTitle}
              onChangeText={setCustomTitle}
              placeholder="Notification title"
              placeholderTextColor="#8E8E93"
            />

            <TextInput
              style={[styles.modernInput, styles.multilineInput]}
              value={customBody}
              onChangeText={setCustomBody}
              placeholder="Notification body"
              placeholderTextColor="#8E8E93"
              multiline
              numberOfLines={3}
            />

            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Enable Sound</Text>
              <Switch
                value={enableSound}
                onValueChange={setEnableSound}
                trackColor={{ false: '#E5E5EA', true: '#34C759' }}
                thumbColor="#FFFFFF"
                ios_backgroundColor="#E5E5EA"
              />
            </View>
          </View>

          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.modernButton, styles.primaryButton]}
              onPress={handleDisplayLocalNotification}
            >
              <Text style={styles.primaryButtonText}>Send Notification</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.modernButton, styles.secondaryButton]}
              onPress={handleProcessTestNotification}
            >
              <Text style={styles.secondaryButtonText}>Process Test</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Settings & History Card */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Settings & History</Text>

          <View style={styles.settingsGroup}>
            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Banner Notifications</Text>
              <Switch
                value={notificationState.bannerEnabled}
                onValueChange={(value) => { dispatch(setSettings({ bannerEnabled: value })); }}
                trackColor={{ false: '#E5E5EA', true: '#34C759' }}
                thumbColor="#FFFFFF"
                ios_backgroundColor="#E5E5EA"
              />
            </View>

            <View style={styles.switchRow}>
              <Text style={styles.switchLabel}>Sound Enabled</Text>
              <Switch
                value={notificationState.soundEnabled}
                onValueChange={(value) => { dispatch(setSettings({ soundEnabled: value })); }}
                trackColor={{ false: '#E5E5EA', true: '#34C759' }}
                thumbColor="#FFFFFF"
                ios_backgroundColor="#E5E5EA"
              />
            </View>
          </View>

          <View style={styles.historySection}>
            <View style={styles.historyHeader}>
              <Text style={styles.historyTitle}>
                Recent History ({notificationState.history.length})
              </Text>
              <TouchableOpacity onPress={handleClearHistory}>
                <Text style={styles.clearButton}>Clear</Text>
              </TouchableOpacity>
            </View>

            {notificationState.history.length === 0 ? (
              <Text style={styles.emptyText}>No notifications yet</Text>
            ) : (
              notificationState.history.slice(0, 3).map((item, index) => (
                <View key={item.id} style={styles.historyItem}>
                  <Text style={styles.historyItemTitle} numberOfLines={1}>
                    {item.notification.title}
                  </Text>
                  <Text style={styles.historyItemTime}>
                    {new Date(item.timestamp).toLocaleTimeString()}
                  </Text>
                </View>
              ))
            )}
          </View>
        </View>

        {/* Bottom Spacing */}
        <View style={{ height: 32 }} />
      </ScrollView>
    </SafeAreaView>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    paddingVertical: 32,
    paddingHorizontal: 4,
  },
  title: {
    fontSize: 34,
    fontWeight: '700',
    color: '#1D1D1F',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 17,
    color: '#6E6E73',
    marginBottom: 24,
  },
  statusCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusLabel: {
    fontSize: 17,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
  },
  statusBadgeText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1D1D1F',
    marginBottom: 16,
  },
  actionGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: (width - 64) / 2,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
  },
  primaryAction: {
    backgroundColor: '#007AFF',
  },
  secondaryAction: {
    backgroundColor: '#F2F2F7',
  },
  actionIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1D1D1F',
    textAlign: 'center',
  },
  statusGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statusItem: {
    alignItems: 'center',
    flex: 1,
  },
  statusValue: {
    fontSize: 24,
    marginBottom: 4,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  modernButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButton: {
    backgroundColor: '#007AFF',
  },
  secondaryButton: {
    backgroundColor: '#F2F2F7',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
  },
  inputGroup: {
    marginBottom: 20,
  },
  modernInput: {
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: '#1D1D1F',
    marginBottom: 12,
    borderWidth: 0,
  },
  multilineInput: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1D1D1F',
  },
  settingsGroup: {
    marginBottom: 20,
  },
  historySection: {
    marginTop: 8,
  },
  historyHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1D1D1F',
  },
  clearButton: {
    fontSize: 14,
    fontWeight: '500',
    color: '#007AFF',
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    marginBottom: 6,
  },
  historyItemTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1D1D1F',
    flex: 1,
  },
  historyItemTime: {
    fontSize: 12,
    color: '#6E6E73',
  },
  emptyText: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 20,
  },
});
