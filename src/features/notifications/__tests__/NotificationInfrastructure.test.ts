// V7 Notification Infrastructure Integration Test
// Tests the complete notification infrastructure including providers, permissions, and local notifications

import { Platform } from 'react-native';
import {
  initializeProvider,
  getCurrentProvider,
  selectNotificationProvider,
  getNotificationToken,
  requestNotificationPermissions,
  getNotificationPermissions,
  scheduleLocalNotification,
  cancelLocalNotification,
  setBadgeCount,
  getBadgeCount,
} from '../services/NotificationService';
import {
  createNotificationProvider,
  selectBestProvider,
  getAvailableProviders,
} from '../providers';
import { NotificationProvider } from '../types';

// Mock platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios', // Default to iOS for testing
  },
}));

// Mock notifee
jest.mock('@notifee/react-native', () => ({
  default: {
    displayNotification: jest.fn().mockResolvedValue(undefined),
    createTriggerNotification: jest.fn().mockResolvedValue(undefined),
    cancelNotification: jest.fn().mockResolvedValue(undefined),
    cancelAllNotifications: jest.fn().mockResolvedValue(undefined),
    setBadgeCount: jest.fn().mockResolvedValue(undefined),
    getBadgeCount: jest.fn().mockResolvedValue(0),
    getTriggerNotifications: jest.fn().mockResolvedValue([]),
    getDisplayedNotifications: jest.fn().mockResolvedValue([]),
    createChannel: jest.fn().mockResolvedValue(undefined),
  },
  TriggerType: {
    TIMESTAMP: 0,
  },
  RepeatFrequency: {
    DAILY: 'daily',
  },
  AndroidImportance: {
    HIGH: 4,
  },
}));

// Mock Firebase messaging
jest.mock('@react-native-firebase/messaging', () => ({
  default: () => ({
    requestPermission: jest.fn().mockResolvedValue(1), // AUTHORIZED
    hasPermission: jest.fn().mockResolvedValue(1),
    getToken: jest.fn().mockResolvedValue('mock-fcm-token'),
    onTokenRefresh: jest.fn().mockReturnValue(() => {}),
    onMessage: jest.fn().mockReturnValue(() => {}),
    onNotificationOpenedApp: jest.fn().mockReturnValue(() => {}),
    getInitialNotification: jest.fn().mockResolvedValue(null),
    setBackgroundMessageHandler: jest.fn(),
  }),
  AuthorizationStatus: {
    AUTHORIZED: 1,
    PROVISIONAL: 2,
    DENIED: 0,
  },
}));

// Mock iOS push notifications
jest.mock('@react-native-community/push-notification-ios', () => ({
  requestPermissions: jest.fn().mockResolvedValue({
    alert: true,
    badge: true,
    sound: true,
    critical: false,
  }),
  checkPermissions: jest.fn().mockImplementation((callback) => {
    callback({
      alert: true,
      badge: true,
      sound: true,
      critical: false,
    });
  }),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  getInitialNotification: jest.fn().mockResolvedValue(null),
  setApplicationIconBadgeNumber: jest.fn(),
  getApplicationIconBadgeNumber: jest.fn().mockImplementation((callback) => {
    callback(0);
  }),
}));

describe('Notification Infrastructure', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Provider Architecture', () => {
    test('should select best provider for iOS', () => {
      (Platform as any).OS = 'ios';
      const provider = selectBestProvider();
      expect(provider).toBe(NotificationProvider.APNS);
    });

    test('should select best provider for Android', () => {
      (Platform as any).OS = 'android';
      const provider = selectBestProvider();
      expect(provider).toBe(NotificationProvider.FCM);
    });

    test('should get available providers for current platform', () => {
      (Platform as any).OS = 'ios';
      const providers = getAvailableProviders();
      expect(providers).toContain(NotificationProvider.APNS);
      expect(providers).not.toContain(NotificationProvider.FCM);
    });

    test('should create provider instance', () => {
      (Platform as any).OS = 'ios';
      const provider = createNotificationProvider(NotificationProvider.APNS);
      expect(provider).toBeDefined();
      expect(provider.provider).toBe(NotificationProvider.APNS);
    });
  });

  describe('Provider Initialization', () => {
    test('should initialize iOS provider successfully', async () => {
      (Platform as any).OS = 'ios';
      
      const provider = await initializeProvider();
      expect(provider).toBeDefined();
      expect(provider.provider).toBe(NotificationProvider.APNS);
      expect(provider.isInitialized).toBe(true);
    });

    test('should initialize Android provider successfully', async () => {
      (Platform as any).OS = 'android';
      
      const provider = await initializeProvider();
      expect(provider).toBeDefined();
      expect(provider.provider).toBe(NotificationProvider.FCM);
      expect(provider.isInitialized).toBe(true);
    });

    test('should get current provider after initialization', async () => {
      (Platform as any).OS = 'ios';
      
      await initializeProvider();
      const currentProvider = getCurrentProvider();
      expect(currentProvider).toBeDefined();
      expect(currentProvider?.provider).toBe(NotificationProvider.APNS);
    });
  });

  describe('Permission Management', () => {
    test('should request notification permissions', async () => {
      (Platform as any).OS = 'ios';
      
      const settings = await requestNotificationPermissions();
      expect(settings).toBeDefined();
      expect(settings?.authorizationStatus).toBe('authorized');
      expect(settings?.alert).toBe(1);
      expect(settings?.badge).toBe(1);
      expect(settings?.sound).toBe(1);
    });

    test('should get current permission status', async () => {
      (Platform as any).OS = 'ios';
      
      const settings = await getNotificationPermissions();
      expect(settings).toBeDefined();
      expect(settings?.authorizationStatus).toBe('authorized');
    });

    test('should handle permission request failure gracefully', async () => {
      // Mock permission failure
      const mockRequestPermissions = require('@react-native-community/push-notification-ios').requestPermissions;
      mockRequestPermissions.mockRejectedValueOnce(new Error('Permission denied'));
      
      (Platform as any).OS = 'ios';
      
      const settings = await requestNotificationPermissions();
      expect(settings).toBeNull();
    });
  });

  describe('Token Management', () => {
    test('should get notification token for iOS', async () => {
      (Platform as any).OS = 'ios';
      
      const token = await getNotificationToken();
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
    });

    test('should get notification token for Android', async () => {
      (Platform as any).OS = 'android';
      
      const token = await getNotificationToken();
      expect(token).toBe('mock-fcm-token');
    });

    test('should handle token retrieval failure', async () => {
      // Mock token failure
      const messaging = require('@react-native-firebase/messaging').default;
      messaging().getToken.mockRejectedValueOnce(new Error('Token failed'));
      
      (Platform as any).OS = 'android';
      
      const token = await getNotificationToken();
      expect(token).toBeNull();
    });
  });

  describe('Local Notifications', () => {
    test('should schedule immediate local notification', async () => {
      const notifee = require('@notifee/react-native').default;
      
      const notificationId = await scheduleLocalNotification({
        title: 'Test Notification',
        body: 'This is a test notification',
        data: { test: true },
      });

      expect(notificationId).toBeDefined();
      expect(notifee.displayNotification).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'Test Notification',
          body: 'This is a test notification',
        })
      );
    });

    test('should schedule delayed local notification', async () => {
      const notifee = require('@notifee/react-native').default;
      const futureTime = Date.now() + 60000; // 1 minute from now
      
      const notificationId = await scheduleLocalNotification({
        title: 'Delayed Notification',
        body: 'This notification is scheduled',
        trigger: {
          type: 'timestamp',
          timestamp: futureTime,
        },
      });

      expect(notificationId).toBeDefined();
      expect(notifee.createTriggerNotification).toHaveBeenCalled();
    });

    test('should cancel local notification', async () => {
      const notifee = require('@notifee/react-native').default;
      
      await cancelLocalNotification('test-id');
      
      expect(notifee.cancelNotification).toHaveBeenCalledWith('test-id');
    });
  });

  describe('Badge Management', () => {
    test('should set badge count', async () => {
      await setBadgeCount(5);
      
      // Should use provider if available, otherwise fall back to notifee
      const notifee = require('@notifee/react-native').default;
      expect(notifee.setBadgeCount).toHaveBeenCalledWith(5);
    });

    test('should get badge count', async () => {
      const count = await getBadgeCount();
      expect(count).toBe(0);
    });

    test('should handle badge operations gracefully on failure', async () => {
      const notifee = require('@notifee/react-native').default;
      notifee.setBadgeCount.mockRejectedValueOnce(new Error('Badge failed'));
      
      // Should not throw
      await expect(setBadgeCount(3)).resolves.toBeUndefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle provider initialization failure', async () => {
      // Mock platform as unsupported
      (Platform as any).OS = 'web';
      
      await expect(initializeProvider()).rejects.toThrow();
    });

    test('should handle invalid notification data gracefully', async () => {
      await expect(scheduleLocalNotification({
        title: '',
        body: '',
      })).rejects.toThrow();
    });
  });
});
