// V7 Notification System Integration Test
// Tests the complete notification flow from token retrieval to action processing

import { configureStore } from '@reduxjs/toolkit';
import notificationsReducer, {
  initializeNotifications,
  refreshNotificationToken,
  startNotificationEventHandling,
  processNotification,
} from '../store/notificationsSlice';
import * as NotificationService from '../services/NotificationService';
import * as NotificationIntegration from '../services/NotificationIntegration';
import { NotificationProvider, NotificationErrorCodes } from '../types';

// Mock Firebase messaging
jest.mock('@react-native-firebase/messaging', () => {
  const mockMessaging = {
    requestPermission: jest.fn(() => Promise.resolve(1)), // AUTHORIZED
    getToken: jest.fn(() => Promise.resolve('mock-fcm-token')),
    onTokenRefresh: jest.fn(() => jest.fn()), // Returns unsubscribe function
    onMessage: jest.fn(() => jest.fn()),
    onNotificationOpenedApp: jest.fn(() => jest.fn()),
    setBackgroundMessageHandler: jest.fn(),
    getInitialNotification: jest.fn(() => Promise.resolve(null)),
  };

  return {
    __esModule: true,
    default: () => mockMessaging,
    // Export AuthorizationStatus constants
    AuthorizationStatus: {
      AUTHORIZED: 1,
      DENIED: 0,
      NOT_DETERMINED: -1,
      PROVISIONAL: 2,
    },
  };
});

// Mock notifee
jest.mock('@notifee/react-native', () => ({
  createChannel: jest.fn(() => Promise.resolve()),
  onForegroundEvent: jest.fn(() => jest.fn()),
  onBackgroundEvent: jest.fn(),
  getInitialNotification: jest.fn(() => Promise.resolve(null)),
  getNotificationSettings: jest.fn(() => Promise.resolve({
    authorizationStatus: 2, // AUTHORIZED (notifee uses 2 for authorized)
    ios: {
      sound: 1,
      badge: 1,
      alert: 1,
      criticalAlert: 0,
      provisional: 0,
    },
  })),
  EventType: {
    PRESS: 1,
  },
  AndroidImportance: {
    DEFAULT: 3,
    HIGH: 4,
    LOW: 2,
    MIN: 1,
    NONE: 0,
  },
  AndroidVisibility: {
    PRIVATE: 0,
    PUBLIC: 1,
    SECRET: -1,
  },
}));

// Mock MMKV
jest.mock('react-native-mmkv', () => ({
  MMKV: jest.fn().mockImplementation(() => ({
    getString: jest.fn(),
    setString: jest.fn(),
    set: jest.fn(), // Add the set method
    get: jest.fn(),
    delete: jest.fn(),
  })),
}));

// Mock Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'android', // Default to android for FCM tests
  },
  AppState: {
    currentState: 'active',
  },
}));

// Mock the store import for NotificationEventHandler
jest.mock('../../../core/store', () => ({
  store: {
    dispatch: jest.fn(() => Promise.resolve()),
  },
}));

// Mock missing dependencies for notification action processing
// These modules don't exist yet, so we'll mock them at the global level
global.handleNotificationSync = jest.fn(() => Promise.resolve());
global.handleNotificationEngagement = jest.fn(() => Promise.resolve());

// Mock the NotificationActionService to avoid import issues
jest.mock('../services/NotificationActionService', () => ({
  parseNotificationPayload: jest.fn((payload) => ({
    id: payload.messageId || 'test-id',
    title: payload.notification?.title || 'Test Title',
    subtitle: payload.notification?.subtitle || '',
    body: payload.notification?.body || 'Test Body',
    data: payload.data || {},
  })),
  processNotificationActions: jest.fn(() => Promise.resolve()),
  isAppInForeground: jest.fn(() => true),
}));

describe('Notification System Integration', () => {
  let store: any;

  beforeEach(() => {
    store = configureStore({
      reducer: {
        notifications: notificationsReducer,
      },
    });
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('Notification Initialization', () => {
    it('should initialize notifications with FCM provider', async () => {
      const result = await store.dispatch(
        initializeNotifications({
          platform: 'android',
          region: 'US',
        })
      );

      expect(result.type).toBe('notifications/initialize/fulfilled');
      expect(result.payload.provider).toBe(NotificationProvider.FCM);
      expect(result.payload.initialized).toBe(true);

      const state = store.getState().notifications;
      expect(state.activeProvider).toBe(NotificationProvider.FCM);
      expect(state.isInitialized).toBe(true);
    });

    it('should select APNS provider for iOS', async () => {
      // Temporarily mock Platform.OS to be iOS for this test
      const originalOS = require('react-native').Platform.OS;
      require('react-native').Platform.OS = 'ios';

      const result = await store.dispatch(
        initializeNotifications({
          platform: 'ios',
          region: 'US',
        })
      );

      expect(result.payload.provider).toBe(NotificationProvider.APNS);

      // Restore original Platform.OS
      require('react-native').Platform.OS = originalOS;
    });
  });

  describe('Token Management', () => {
    it('should retrieve and persist notification token', async () => {
      const token = await NotificationService.getNotificationToken(NotificationProvider.FCM);
      
      expect(token).toBe('mock-fcm-token');
    });

    it('should detect token changes', () => {
      const hasChanged = NotificationService.hasTokenChanged(NotificationProvider.FCM, 'new-token');
      
      // Should return true since no token is persisted initially
      expect(hasChanged).toBe(true);
    });

    it('should refresh token when changed', async () => {
      const result = await store.dispatch(
        refreshNotificationToken(NotificationProvider.FCM)
      );

      expect(result.type).toBe('notifications/refreshToken/fulfilled');
      expect(result.payload.changed).toBe(true);
      expect(result.payload.token.token).toBe('mock-fcm-token');
    });
  });

  describe('Event Handling', () => {
    it('should start notification event handling', async () => {
      const result = await store.dispatch(startNotificationEventHandling());

      expect(result.type).toBe('notifications/startEventHandling/fulfilled');
      expect(result.payload.eventHandlingActive).toBe(true);

      const state = store.getState().notifications;
      expect(state.isInitialized).toBe(true);
    });
  });

  describe('Notification Processing', () => {
    it('should process notification with sync action', async () => {
      // Mock sync function
      global.handleNotificationSync = jest.fn(() => Promise.resolve());

      const mockNotification = {
        id: 'test-notification-1',
        title: 'Test Notification',
        body: 'Test notification body',
        data: {
          sync: 'cache',
          options: {
            banner: true,
          },
        },
        options: {
          sync: 'cache',
          banner: true,
        },
      };

      const result = await store.dispatch(
        processNotification({
          notification: mockNotification,
          tapped: false,
          inApp: true,
        })
      );

      expect(result.type).toBe('notifications/process/fulfilled');
      expect(result.payload.processed).toBe(true);
      expect(result.payload.shouldShowBanner).toBe(true);

      const state = store.getState().notifications;
      expect(state.handledNotifications).toContain('test-notification-1');
      expect(state.history).toHaveLength(1);
    });

    it('should not process duplicate notifications', async () => {
      const mockNotification = {
        id: 'duplicate-notification',
        title: 'Duplicate Test',
        body: 'This should only be processed once',
        data: {},
        options: {},
      };

      // Process first time
      await store.dispatch(
        processNotification({
          notification: mockNotification,
          tapped: false,
          inApp: true,
        })
      );

      // Process second time (should be ignored)
      const result = await store.dispatch(
        processNotification({
          notification: mockNotification,
          tapped: false,
          inApp: true,
        })
      );

      expect(result.payload).toBeNull(); // Should return null for duplicates
    });
  });

  describe('Installation Integration', () => {
    it('should initialize notifications for installation', async () => {
      const result = await NotificationIntegration.initializeNotificationForInstallation();

      expect(result.provider).toBe(NotificationProvider.FCM);
      expect(result.token).toBeTruthy();
      expect(result.refreshListener).toBeTruthy();
    });

    it('should check notification permission status', async () => {
      const status = await NotificationIntegration.checkNotificationPermissionStatus();

      expect(status.granted).toBe(true);
      expect(status.status).toBe('authorized');
      expect(status.canRequest).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle initialization errors gracefully', async () => {
      // Mock notifee to throw error during channel creation
      const mockNotifee = require('@notifee/react-native');
      mockNotifee.createChannel.mockRejectedValueOnce(new Error('Channel creation failed'));

      const result = await store.dispatch(
        initializeNotifications({
          platform: 'android',
          region: 'US',
        })
      );

      expect(result.type).toBe('notifications/initialize/rejected');
      expect(result.payload.code).toBe(NotificationErrorCodes.PROVIDER_SELECTION_FAILED);

      // Restore the mock
      mockNotifee.createChannel.mockResolvedValue(undefined);
    });

    it('should handle token refresh errors', async () => {
      // Mock Firebase to throw error
      const mockMessaging = require('@react-native-firebase/messaging').default;
      mockMessaging().getToken.mockRejectedValueOnce(new Error('Network error'));

      const result = await store.dispatch(
        refreshNotificationToken(NotificationProvider.FCM)
      );

      expect(result.type).toBe('notifications/refreshToken/rejected');
      expect(result.payload.code).toBe(NotificationErrorCodes.TOKEN_SYNC_FAILED);
    });
  });

  describe('Lifecycle Management', () => {
    it('should validate notification setup', async () => {
      const validation = await NotificationIntegration.validateNotificationSetup();

      expect(validation.isValid).toBe(true);
      expect(validation.issues).toHaveLength(0);
      expect(validation.recommendations).toContain('Ensure notification channels are properly configured');
    });

    it('should create and manage lifecycle', () => {
      const manager = NotificationIntegration.createNotificationLifecycleManager();

      expect(manager.start).toBeDefined();
      expect(manager.stop).toBeDefined();
      expect(manager.refresh).toBeDefined();
    });
  });
});

describe('Notification Service Functions', () => {
  describe('Provider Selection', () => {
    it('should select correct provider for platform', () => {
      const fcmProvider = NotificationService.selectNotificationProvider();
      expect([NotificationProvider.FCM, NotificationProvider.APNS]).toContain(fcmProvider);
    });
  });

  describe('Channel Management', () => {
    it('should create notification channels on Android', async () => {
      const channels = [
        {
          id: 'test-channel',
          name: 'Test Channel',
          description: 'Test channel description',
          importance: 4,
        },
      ];

      await expect(
        NotificationService.createNotificationChannels(channels)
      ).resolves.not.toThrow();
    });
  });
});
