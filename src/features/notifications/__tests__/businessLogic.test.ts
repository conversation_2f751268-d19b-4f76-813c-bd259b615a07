/**
 * V7 Notifications Business Logic Tests
 * 
 * Comprehensive tests for all V6 business rules extracted and implemented in V7 patterns
 * Tests multi-provider architecture, deduplication logic, badge management, notification processing
 */

import { Platform } from 'react-native';
import {
  NotificationProvider,
  NotificationData,
  ProcessedNotification,
  NotificationToken,
  BadgeState,
  NotificationErrorCodes,
} from '../types';

// =============================================================================
// BUSINESS LOGIC FUNCTIONS (Extracted from slice for testing)
// =============================================================================

// V6 Business Logic: Provider Selection
function selectNotificationProvider(platform: string, region: string): NotificationProvider {
  if (platform === 'ios') return NotificationProvider.APNS;
  
  // V6 Business Logic: Market-based selection for Android
  return isChineseMarket(region) ? NotificationProvider.JPUSH : NotificationProvider.FCM;
}

function isChineseMarket(region: string): boolean {
  return region === 'CN' || region === 'China';
}

// V6 Business Logic: Notification Deduplication
function isNotificationHandled(handledNotifications: string[], notificationId: string): boolean {
  return handledNotifications.includes(notificationId);
}

function addToHandledNotifications(handledNotifications: string[], notificationId: string): string[] {
  if (handledNotifications.includes(notificationId)) {
    return handledNotifications;
  }
  
  const updated = [...handledNotifications, notificationId];
  
  // V6 Business Logic: Cleanup old entries to prevent memory leaks
  if (updated.length > 1000) {
    return updated.slice(-500);
  }
  
  return updated;
}

// V6 Business Logic: Badge Management
function calculateAppBadge(cardBadges: Record<string, number>): number {
  return Object.values(cardBadges).reduce((total, count) => total + count, 0);
}

function updateCardBadge(badgeState: BadgeState, cardId: string, count: number): BadgeState {
  const updatedCardBadges = {
    ...badgeState.cardBadges,
    [cardId]: count,
  };
  
  return {
    cardBadges: updatedCardBadges,
    appBadge: calculateAppBadge(updatedCardBadges),
    lastUpdated: new Date().toISOString(),
  };
}

// V6 Business Logic: Banner Display Logic
function shouldShowBanner(notification: NotificationData, inApp: boolean, tapped: boolean): boolean {
  return notification.options?.banner !== false && inApp && !tapped;
}

// V6 Business Logic: Token Deduplication
function hasNotificationTokenChanged(tokens: NotificationToken[], provider: NotificationProvider, token: string): boolean {
  const existingToken = tokens.find(t => t.provider === provider);
  return !existingToken || existingToken.token !== token;
}

// V6 Business Logic: Notification Processing
function processNotificationData(
  notification: NotificationData,
  tapped: boolean,
  inApp: boolean
): ProcessedNotification {
  return {
    id: notification.id,
    title: notification.title,
    subtitle: notification.subtitle,
    body: notification.body,
    data: notification.data,
    receivedAt: new Date().toISOString(),
    handledAt: new Date().toISOString(),
    tapped,
    inApp,
    shouldShowBanner: shouldShowBanner(notification, inApp, tapped),
    processed: true,
  };
}

// V6 Business Logic: Sync Detection
function shouldTriggerSync(notification: NotificationData): boolean {
  return notification.options?.sync !== undefined && 
         ['cache', 'all'].includes(notification.options.sync);
}

// V6 Business Logic: Engagement Detection
function shouldTriggerEngagement(notification: NotificationData): boolean {
  return notification.options?.nav?.engage !== undefined;
}

// =============================================================================
// BUSINESS LOGIC TESTS
// =============================================================================

describe('Notifications Business Logic Tests', () => {
  
  // =============================================================================
  // PROVIDER SELECTION TESTS
  // =============================================================================
  
  describe('Provider Selection', () => {
    test('should select APNS for iOS platform', () => {
      const provider = selectNotificationProvider('ios', 'US');
      expect(provider).toBe(NotificationProvider.APNS);
    });
    
    test('should select FCM for Android in global markets', () => {
      const usProvider = selectNotificationProvider('android', 'US');
      expect(usProvider).toBe(NotificationProvider.FCM);
      
      const ukProvider = selectNotificationProvider('android', 'UK');
      expect(ukProvider).toBe(NotificationProvider.FCM);
      
      const deProvider = selectNotificationProvider('android', 'DE');
      expect(deProvider).toBe(NotificationProvider.FCM);
    });
    
    test('should select JPush for Android in Chinese market', () => {
      const cnProvider = selectNotificationProvider('android', 'CN');
      expect(cnProvider).toBe(NotificationProvider.JPUSH);
      
      const chinaProvider = selectNotificationProvider('android', 'China');
      expect(chinaProvider).toBe(NotificationProvider.JPUSH);
    });
    
    test('should detect Chinese market correctly', () => {
      expect(isChineseMarket('CN')).toBe(true);
      expect(isChineseMarket('China')).toBe(true);
      expect(isChineseMarket('US')).toBe(false);
      expect(isChineseMarket('UK')).toBe(false);
    });
  });
  
  // =============================================================================
  // DEDUPLICATION TESTS
  // =============================================================================
  
  describe('Notification Deduplication', () => {
    test('should detect handled notifications', () => {
      const handledNotifications = ['notif-1', 'notif-2', 'notif-3'];
      
      expect(isNotificationHandled(handledNotifications, 'notif-2')).toBe(true);
      expect(isNotificationHandled(handledNotifications, 'notif-4')).toBe(false);
    });
    
    test('should add new notifications to handled list', () => {
      const handledNotifications = ['notif-1', 'notif-2'];
      
      const updated = addToHandledNotifications(handledNotifications, 'notif-3');
      
      expect(updated).toHaveLength(3);
      expect(updated).toContain('notif-3');
      expect(updated).not.toBe(handledNotifications); // Immutability
    });
    
    test('should not duplicate existing notifications', () => {
      const handledNotifications = ['notif-1', 'notif-2'];
      
      const updated = addToHandledNotifications(handledNotifications, 'notif-2');
      
      expect(updated).toHaveLength(2);
      expect(updated).toBe(handledNotifications); // No change
    });
    
    test('should cleanup old notifications when limit exceeded', () => {
      // Create array with 1000 notifications
      const handledNotifications = Array.from({ length: 1000 }, (_, i) => `notif-${i}`);
      
      const updated = addToHandledNotifications(handledNotifications, 'notif-new');
      
      expect(updated).toHaveLength(500); // Cleaned up to 500
      expect(updated).toContain('notif-new');
      expect(updated).toContain('notif-999'); // Recent ones preserved
      expect(updated).not.toContain('notif-0'); // Old ones removed
    });
    
    test('should handle empty handled notifications list', () => {
      const updated = addToHandledNotifications([], 'notif-1');
      
      expect(updated).toHaveLength(1);
      expect(updated[0]).toBe('notif-1');
    });
  });
  
  // =============================================================================
  // BADGE MANAGEMENT TESTS
  // =============================================================================
  
  describe('Badge Management', () => {
    test('should calculate app badge as sum of card badges', () => {
      const cardBadges = {
        'card-1': 5,
        'card-2': 3,
        'card-3': 0,
        'card-4': 2,
      };
      
      const appBadge = calculateAppBadge(cardBadges);
      expect(appBadge).toBe(10); // 5 + 3 + 0 + 2
    });
    
    test('should handle empty card badges', () => {
      const appBadge = calculateAppBadge({});
      expect(appBadge).toBe(0);
    });
    
    test('should update card badge and recalculate app badge', () => {
      const initialBadgeState: BadgeState = {
        cardBadges: {
          'card-1': 5,
          'card-2': 3,
        },
        appBadge: 8,
        lastUpdated: '2024-01-01T00:00:00Z',
      };
      
      const updated = updateCardBadge(initialBadgeState, 'card-1', 7);
      
      expect(updated.cardBadges['card-1']).toBe(7);
      expect(updated.cardBadges['card-2']).toBe(3);
      expect(updated.appBadge).toBe(10); // 7 + 3
      expect(updated.lastUpdated).not.toBe(initialBadgeState.lastUpdated);
      expect(updated).not.toBe(initialBadgeState); // Immutability
    });
    
    test('should add new card badge', () => {
      const initialBadgeState: BadgeState = {
        cardBadges: {
          'card-1': 5,
        },
        appBadge: 5,
        lastUpdated: '2024-01-01T00:00:00Z',
      };
      
      const updated = updateCardBadge(initialBadgeState, 'card-2', 3);
      
      expect(updated.cardBadges['card-1']).toBe(5);
      expect(updated.cardBadges['card-2']).toBe(3);
      expect(updated.appBadge).toBe(8); // 5 + 3
    });
    
    test('should handle zero badge counts', () => {
      const initialBadgeState: BadgeState = {
        cardBadges: {
          'card-1': 5,
          'card-2': 3,
        },
        appBadge: 8,
        lastUpdated: '2024-01-01T00:00:00Z',
      };
      
      const updated = updateCardBadge(initialBadgeState, 'card-1', 0);
      
      expect(updated.cardBadges['card-1']).toBe(0);
      expect(updated.appBadge).toBe(3); // 0 + 3
    });
  });
  
  // =============================================================================
  // BANNER DISPLAY LOGIC TESTS
  // =============================================================================
  
  describe('Banner Display Logic', () => {
    const mockNotification: NotificationData = {
      id: 'test-notif-1',
      title: 'Test Notification',
      body: 'Test body',
      data: {},
      options: { banner: true },
    };
    
    test('should show banner when in app and not tapped', () => {
      const result = shouldShowBanner(mockNotification, true, false);
      expect(result).toBe(true);
    });
    
    test('should not show banner when tapped', () => {
      const result = shouldShowBanner(mockNotification, true, true);
      expect(result).toBe(false);
    });
    
    test('should not show banner when not in app', () => {
      const result = shouldShowBanner(mockNotification, false, false);
      expect(result).toBe(false);
    });
    
    test('should respect banner option disabled', () => {
      const noBannerNotification: NotificationData = {
        ...mockNotification,
        options: { banner: false },
      };
      
      const result = shouldShowBanner(noBannerNotification, true, false);
      expect(result).toBe(false);
    });
    
    test('should default to showing banner when option not specified', () => {
      const defaultNotification: NotificationData = {
        ...mockNotification,
        options: {},
      };
      
      const result = shouldShowBanner(defaultNotification, true, false);
      expect(result).toBe(true);
    });
  });
  
  // =============================================================================
  // TOKEN MANAGEMENT TESTS
  // =============================================================================
  
  describe('Token Management', () => {
    test('should detect token changes', () => {
      const tokens: NotificationToken[] = [
        {
          provider: NotificationProvider.FCM,
          token: 'old-token-123',
          modifiedAt: '2024-01-01T00:00:00Z',
          isActive: true,
        },
      ];
      
      const hasChanged = hasNotificationTokenChanged(tokens, NotificationProvider.FCM, 'new-token-456');
      expect(hasChanged).toBe(true);
    });
    
    test('should not detect change when token is same', () => {
      const tokens: NotificationToken[] = [
        {
          provider: NotificationProvider.FCM,
          token: 'same-token-123',
          modifiedAt: '2024-01-01T00:00:00Z',
          isActive: true,
        },
      ];
      
      const hasChanged = hasNotificationTokenChanged(tokens, NotificationProvider.FCM, 'same-token-123');
      expect(hasChanged).toBe(false);
    });
    
    test('should detect change when no existing token', () => {
      const tokens: NotificationToken[] = [];
      
      const hasChanged = hasNotificationTokenChanged(tokens, NotificationProvider.FCM, 'new-token-123');
      expect(hasChanged).toBe(true);
    });
    
    test('should handle multiple providers', () => {
      const tokens: NotificationToken[] = [
        {
          provider: NotificationProvider.FCM,
          token: 'fcm-token-123',
          modifiedAt: '2024-01-01T00:00:00Z',
          isActive: true,
        },
        {
          provider: NotificationProvider.APNS,
          token: 'apns-token-456',
          modifiedAt: '2024-01-01T00:00:00Z',
          isActive: true,
        },
      ];
      
      const fcmChanged = hasNotificationTokenChanged(tokens, NotificationProvider.FCM, 'fcm-token-123');
      const apnsChanged = hasNotificationTokenChanged(tokens, NotificationProvider.APNS, 'new-apns-token');
      const jpushChanged = hasNotificationTokenChanged(tokens, NotificationProvider.JPUSH, 'jpush-token');
      
      expect(fcmChanged).toBe(false);
      expect(apnsChanged).toBe(true);
      expect(jpushChanged).toBe(true);
    });
  });
  
  // =============================================================================
  // NOTIFICATION PROCESSING TESTS
  // =============================================================================
  
  describe('Notification Processing', () => {
    const mockNotificationData: NotificationData = {
      id: 'test-notif-1',
      title: 'Test Notification',
      subtitle: 'Test Subtitle',
      body: 'Test notification body',
      data: { key: 'value' },
      options: {
        sync: 'cache',
        banner: true,
        nav: { engage: { object: 'Offer', param: { id: 'offer-1' } } },
      },
    };
    
    test('should process notification data correctly', () => {
      const processed = processNotificationData(mockNotificationData, false, true);
      
      expect(processed.id).toBe('test-notif-1');
      expect(processed.title).toBe('Test Notification');
      expect(processed.subtitle).toBe('Test Subtitle');
      expect(processed.body).toBe('Test notification body');
      expect(processed.data).toEqual({ key: 'value' });
      expect(processed.tapped).toBe(false);
      expect(processed.inApp).toBe(true);
      expect(processed.shouldShowBanner).toBe(true);
      expect(processed.processed).toBe(true);
      expect(processed.receivedAt).toBeTruthy();
      expect(processed.handledAt).toBeTruthy();
    });
    
    test('should detect sync requirements', () => {
      const syncNotification: NotificationData = {
        ...mockNotificationData,
        options: { sync: 'cache' },
      };
      
      const allSyncNotification: NotificationData = {
        ...mockNotificationData,
        options: { sync: 'all' },
      };
      
      const noSyncNotification: NotificationData = {
        ...mockNotificationData,
        options: {},
      };
      
      expect(shouldTriggerSync(syncNotification)).toBe(true);
      expect(shouldTriggerSync(allSyncNotification)).toBe(true);
      expect(shouldTriggerSync(noSyncNotification)).toBe(false);
    });
    
    test('should detect engagement requirements', () => {
      const engagementNotification: NotificationData = {
        ...mockNotificationData,
        options: {
          nav: { engage: { object: 'Offer', param: { id: 'offer-1' } } },
        },
      };
      
      const noEngagementNotification: NotificationData = {
        ...mockNotificationData,
        options: {},
      };
      
      expect(shouldTriggerEngagement(engagementNotification)).toBe(true);
      expect(shouldTriggerEngagement(noEngagementNotification)).toBe(false);
    });
    
    test('should handle notifications without options', () => {
      const minimalNotification: NotificationData = {
        id: 'minimal-notif',
        title: 'Minimal',
        body: 'Body',
        data: {},
      };

      const processed = processNotificationData(minimalNotification, true, false);

      expect(processed.shouldShowBanner).toBe(false); // Not in app
      expect(processed.tapped).toBe(true);
      expect(processed.inApp).toBe(false);
    });
  });
});
