/**
 * V7 Notifications Redux Slice Tests
 * 
 * Comprehensive tests for Redux Toolkit slice including action creators, reducers,
 * async thunks, state transitions, and error handling
 */

import { configureStore } from '@reduxjs/toolkit';
import notificationsReducer, {
  setActiveProvider,
  updateProviderStatus,
  setNotificationToken,
  markNotificationHandled,
  markNotificationReceived,
  updateCardBadge,
  refreshAllBadges,
  addToHistory,
  removeFromHistory,
  clearHistory,
  setActive,
  setSettings,
  setError,
  clearError,
  setLoading,
  initializeNotifications,
  processNotification,
} from '../store/notificationsSlice';
import {
  NotificationsState,
  NotificationProvider,
  NotificationData,
  ProcessedNotification,
  NotificationToken,
  InitializeNotificationsPayload,
  ProcessNotificationPayload,
  UpdateBadgePayload,
  SetTokenPayload,
  NotificationError,
  NotificationErrorCodes,
  NotificationChannels,
  NotificationImportance,
} from '../types';

// =============================================================================
// TEST STORE SETUP
// =============================================================================

function createTestStore(initialState?: Partial<NotificationsState>) {
  return configureStore({
    reducer: {
      notifications: notificationsReducer,
    },
    preloadedState: {
      notifications: {
        activeProvider: null,
        providers: {},
        tokens: [],
        currentToken: null,
        handledNotifications: [],
        receivedNotifications: [],
        processingQueue: [],
        badges: {
          cardBadges: {},
          appBadge: 0,
          lastUpdated: new Date().toISOString(),
        },
        history: [],
        channels: [
          {
            id: NotificationChannels.PERKD,
            name: NotificationChannels.PERKD,
            description: NotificationChannels.PERKD,
            importance: NotificationImportance.HIGH,
            sound: 'chord',
          },
          {
            id: NotificationChannels.ALERTS,
            name: NotificationChannels.ALERTS,
            description: NotificationChannels.ALERTS,
            importance: NotificationImportance.HIGH,
          },
        ],
        isActive: false,
        isInitialized: false,
        lastSync: null,
        error: null,
        isLoading: false,
        bannerEnabled: true,
        soundEnabled: true,
        ...initialState,
      },
    },
  });
}

// =============================================================================
// MOCK DATA
// =============================================================================

const mockNotificationData: NotificationData = {
  id: 'test-notif-1',
  title: 'Test Notification',
  subtitle: 'Test Subtitle',
  body: 'Test notification body',
  data: { key: 'value' },
  options: {
    sync: 'cache',
    banner: true,
  },
};

const mockProcessedNotification: ProcessedNotification = {
  id: 'test-notif-1',
  title: 'Test Notification',
  subtitle: 'Test Subtitle',
  body: 'Test notification body',
  data: { key: 'value' },
  receivedAt: '2024-01-01T00:00:00Z',
  handledAt: '2024-01-01T00:00:00Z',
  tapped: false,
  inApp: true,
  shouldShowBanner: true,
  processed: true,
};

const mockNotificationToken: NotificationToken = {
  provider: NotificationProvider.FCM,
  token: 'test-token-123',
  modifiedAt: '2024-01-01T00:00:00Z',
  isActive: true,
};

// =============================================================================
// MOCK FUNCTIONS
// =============================================================================

// Mock the helper functions
global.selectNotificationProvider = jest.fn();
global.loadNotificationProvider = jest.fn();
global.createNotificationChannels = jest.fn();
global.handleNotificationSync = jest.fn();
global.handleNotificationEngagement = jest.fn();
global.isChineseMarket = jest.fn();

// =============================================================================
// REDUX SLICE TESTS
// =============================================================================

describe('Notifications Redux Slice Tests', () => {
  
  // =============================================================================
  // INITIAL STATE TESTS
  // =============================================================================
  
  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const store = createTestStore();
      const state = store.getState().notifications;
      
      expect(state.activeProvider).toBeNull();
      expect(state.providers).toEqual({});
      expect(state.tokens).toEqual([]);
      expect(state.currentToken).toBeNull();
      expect(state.handledNotifications).toEqual([]);
      expect(state.receivedNotifications).toEqual([]);
      expect(state.processingQueue).toEqual([]);
      expect(state.badges.cardBadges).toEqual({});
      expect(state.badges.appBadge).toBe(0);
      expect(state.history).toEqual([]);
      expect(state.channels).toHaveLength(2);
      expect(state.isActive).toBe(false);
      expect(state.isInitialized).toBe(false);
      expect(state.lastSync).toBeNull();
      expect(state.error).toBeNull();
      expect(state.isLoading).toBe(false);
      expect(state.bannerEnabled).toBe(true);
      expect(state.soundEnabled).toBe(true);
    });
  });
  
  // =============================================================================
  // SYNCHRONOUS ACTION TESTS
  // =============================================================================
  
  describe('Synchronous Actions', () => {
    test('should set active provider', () => {
      const store = createTestStore();
      
      store.dispatch(setActiveProvider(NotificationProvider.FCM));
      
      const state = store.getState().notifications;
      expect(state.activeProvider).toBe(NotificationProvider.FCM);
    });
    
    test('should update provider status', () => {
      const store = createTestStore();
      
      store.dispatch(updateProviderStatus({
        provider: NotificationProvider.FCM,
        registered: true,
        available: true,
      }));
      
      const state = store.getState().notifications;
      expect(state.providers[NotificationProvider.FCM]).toEqual({
        registered: true,
        available: true,
      });
    });
    
    test('should set notification token with deduplication', () => {
      const store = createTestStore({
        tokens: [mockNotificationToken],
      });
      
      const tokenPayload: SetTokenPayload = {
        provider: NotificationProvider.FCM,
        token: 'new-token-456',
      };
      
      store.dispatch(setNotificationToken(tokenPayload));
      
      const state = store.getState().notifications;
      expect(state.tokens).toHaveLength(1);
      expect(state.tokens[0].token).toBe('new-token-456');
      expect(state.currentToken).toBe('new-token-456');
    });
    
    test('should not update token when same token provided', () => {
      const store = createTestStore({
        tokens: [mockNotificationToken],
      });
      
      const tokenPayload: SetTokenPayload = {
        provider: NotificationProvider.FCM,
        token: 'test-token-123', // Same token
      };
      
      store.dispatch(setNotificationToken(tokenPayload));
      
      const state = store.getState().notifications;
      expect(state.tokens[0].modifiedAt).toBe('2024-01-01T00:00:00Z'); // Unchanged
    });
    
    test('should add new provider token', () => {
      const store = createTestStore({
        tokens: [mockNotificationToken],
      });
      
      const tokenPayload: SetTokenPayload = {
        provider: NotificationProvider.APNS,
        token: 'apns-token-123',
      };
      
      store.dispatch(setNotificationToken(tokenPayload));
      
      const state = store.getState().notifications;
      expect(state.tokens).toHaveLength(2);
      expect(state.tokens[1].provider).toBe(NotificationProvider.APNS);
      expect(state.tokens[1].token).toBe('apns-token-123');
    });
    
    test('should mark notifications as handled with cleanup', () => {
      const store = createTestStore();
      
      // Add notifications
      store.dispatch(markNotificationHandled('notif-1'));
      store.dispatch(markNotificationHandled('notif-2'));
      store.dispatch(markNotificationHandled('notif-1')); // Duplicate
      
      const state = store.getState().notifications;
      expect(state.handledNotifications).toHaveLength(2);
      expect(state.handledNotifications).toContain('notif-1');
      expect(state.handledNotifications).toContain('notif-2');
    });
    
    test('should cleanup handled notifications when limit exceeded', () => {
      // Create store with 1000 handled notifications
      const handledNotifications = Array.from({ length: 1000 }, (_, i) => `notif-${i}`);
      const store = createTestStore({ handledNotifications });
      
      store.dispatch(markNotificationHandled('notif-new'));
      
      const state = store.getState().notifications;
      expect(state.handledNotifications).toHaveLength(500);
      expect(state.handledNotifications).toContain('notif-new');
    });
    
    test('should mark notifications as received', () => {
      const store = createTestStore();
      
      store.dispatch(markNotificationReceived('notif-1'));
      store.dispatch(markNotificationReceived('notif-2'));
      
      const state = store.getState().notifications;
      expect(state.receivedNotifications).toHaveLength(2);
      expect(state.receivedNotifications).toContain('notif-1');
      expect(state.receivedNotifications).toContain('notif-2');
    });
    
    test('should update card badge and recalculate app badge', () => {
      const store = createTestStore({
        badges: {
          cardBadges: { 'card-1': 5 },
          appBadge: 5,
          lastUpdated: '2024-01-01T00:00:00Z',
        },
      });
      
      const badgePayload: UpdateBadgePayload = {
        cardId: 'card-2',
        count: 3,
      };
      
      store.dispatch(updateCardBadge(badgePayload));
      
      const state = store.getState().notifications;
      expect(state.badges.cardBadges['card-1']).toBe(5);
      expect(state.badges.cardBadges['card-2']).toBe(3);
      expect(state.badges.appBadge).toBe(8); // 5 + 3
      expect(state.badges.lastUpdated).not.toBe('2024-01-01T00:00:00Z');
    });
    
    test('should refresh all badges', () => {
      const store = createTestStore();
      
      const newBadges = {
        'card-1': 10,
        'card-2': 5,
        'card-3': 0,
      };
      
      store.dispatch(refreshAllBadges(newBadges));
      
      const state = store.getState().notifications;
      expect(state.badges.cardBadges).toEqual(newBadges);
      expect(state.badges.appBadge).toBe(15); // 10 + 5 + 0
    });
    
    test('should manage notification history', () => {
      const store = createTestStore();
      
      // Add to history
      store.dispatch(addToHistory(mockProcessedNotification));
      
      let state = store.getState().notifications;
      expect(state.history).toHaveLength(1);
      expect(state.history[0].notification).toEqual(mockProcessedNotification);
      
      // Remove from history
      store.dispatch(removeFromHistory('test-notif-1'));
      
      state = store.getState().notifications;
      expect(state.history).toHaveLength(0);
    });
    
    test('should clear all history', () => {
      const store = createTestStore({
        history: [
          { id: 'notif-1', notification: mockProcessedNotification, timestamp: '2024-01-01T00:00:00Z' },
          { id: 'notif-2', notification: mockProcessedNotification, timestamp: '2024-01-01T00:00:00Z' },
        ],
      });
      
      store.dispatch(clearHistory());
      
      const state = store.getState().notifications;
      expect(state.history).toHaveLength(0);
    });
    
    test('should limit history to 100 notifications', () => {
      // Create store with 100 history items
      const history = Array.from({ length: 100 }, (_, i) => ({
        id: `notif-${i}`,
        notification: { ...mockProcessedNotification, id: `notif-${i}` },
        timestamp: '2024-01-01T00:00:00Z',
      }));
      
      const store = createTestStore({ history });
      
      // Add new notification
      store.dispatch(addToHistory({ ...mockProcessedNotification, id: 'notif-new' }));
      
      const state = store.getState().notifications;
      expect(state.history).toHaveLength(100);
      expect(state.history[0].id).toBe('notif-new'); // Most recent first
    });
    
    test('should set active state and settings', () => {
      const store = createTestStore();
      
      store.dispatch(setActive(true));
      store.dispatch(setSettings({ bannerEnabled: false, soundEnabled: false }));
      
      const state = store.getState().notifications;
      expect(state.isActive).toBe(true);
      expect(state.bannerEnabled).toBe(false);
      expect(state.soundEnabled).toBe(false);
    });
    
    test('should handle error states', () => {
      const store = createTestStore();
      const error: NotificationError = {
        code: NotificationErrorCodes.PROVIDER_REGISTRATION_FAILED,
        message: 'Provider registration failed',
        provider: NotificationProvider.FCM,
      };
      
      store.dispatch(setError(error));
      let state = store.getState().notifications;
      expect(state.error).toBe(error.message);
      expect(state.isLoading).toBe(false);
      
      store.dispatch(clearError());
      state = store.getState().notifications;
      expect(state.error).toBeNull();
    });
  });

  // =============================================================================
  // ASYNC THUNK TESTS
  // =============================================================================

  describe('Async Thunks', () => {
    beforeEach(() => {
      // Reset mocks
      jest.clearAllMocks();
    });

    test('should handle initializeNotifications success', async () => {
      global.selectNotificationProvider = jest.fn().mockReturnValue(NotificationProvider.FCM);
      global.loadNotificationProvider = jest.fn().mockResolvedValue({
        init: jest.fn().mockResolvedValue(undefined),
      });
      global.createNotificationChannels = jest.fn().mockResolvedValue(undefined);

      const store = createTestStore();

      const initPayload: InitializeNotificationsPayload = {
        platform: 'android',
        region: 'US',
      };

      const action = await store.dispatch(initializeNotifications(initPayload));

      expect(action.type).toBe('notifications/initialize/fulfilled');
      expect(global.selectNotificationProvider).toHaveBeenCalledWith('android', 'US');

      const state = store.getState().notifications;
      expect(state.activeProvider).toBe(NotificationProvider.FCM);
      expect(state.isInitialized).toBe(true);
      expect(state.lastSync).toBeTruthy();
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });

    test('should handle initializeNotifications failure', async () => {
      global.selectNotificationProvider = jest.fn().mockImplementation(() => {
        throw new Error('Provider selection failed');
      });

      const store = createTestStore();

      const initPayload: InitializeNotificationsPayload = {
        platform: 'android',
        region: 'US',
      };

      const action = await store.dispatch(initializeNotifications(initPayload));

      expect(action.type).toBe('notifications/initialize/rejected');

      const state = store.getState().notifications;
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeTruthy();
    });

    // Note: registerProvider tests removed as the function doesn't exist in the current implementation

    test('should handle processNotification with deduplication', async () => {
      const store = createTestStore({
        handledNotifications: ['test-notif-1'], // Already handled
      });

      const processPayload: ProcessNotificationPayload = {
        notification: mockNotificationData,
        tapped: false,
        inApp: true,
      };

      const action = await store.dispatch(processNotification(processPayload));

      expect(action.type).toBe('notifications/process/fulfilled');
      expect(action.payload).toBeNull(); // Already handled
    });

    test('should handle processNotification success', async () => {
      global.handleNotificationSync = jest.fn().mockResolvedValue(undefined);
      global.handleNotificationEngagement = jest.fn().mockResolvedValue(undefined);

      const store = createTestStore();

      const processPayload: ProcessNotificationPayload = {
        notification: mockNotificationData,
        tapped: false,
        inApp: true,
      };

      const action = await store.dispatch(processNotification(processPayload));

      expect(action.type).toBe('notifications/process/fulfilled');
      expect(action.payload).toBeTruthy();

      const state = store.getState().notifications;
      expect(state.handledNotifications).toContain('test-notif-1');
      expect(state.history).toHaveLength(1);
      expect(state.processingQueue).toHaveLength(1); // Should show banner
    });

    test('should handle processNotification with sync', async () => {
      global.handleNotificationSync = jest.fn().mockResolvedValue(undefined);

      const store = createTestStore();

      const notificationWithSync: NotificationData = {
        ...mockNotificationData,
        options: { sync: 'cache' },
      };

      const processPayload: ProcessNotificationPayload = {
        notification: notificationWithSync,
        tapped: false,
        inApp: true,
      };

      await store.dispatch(processNotification(processPayload));

      expect(global.handleNotificationSync).toHaveBeenCalledWith('cache');
    });

    test('should handle processNotification with engagement', async () => {
      global.handleNotificationEngagement = jest.fn().mockResolvedValue(undefined);

      const store = createTestStore();

      const notificationWithEngagement: NotificationData = {
        ...mockNotificationData,
        options: {
          nav: { engage: { object: 'Offer', param: { id: 'offer-1' } } },
        },
      };

      const processPayload: ProcessNotificationPayload = {
        notification: notificationWithEngagement,
        tapped: false,
        inApp: true,
      };

      await store.dispatch(processNotification(processPayload));

      expect(global.handleNotificationEngagement).toHaveBeenCalledWith({
        object: 'Offer',
        param: { id: 'offer-1' },
      });
    });
  });

  // =============================================================================
  // STATE TRANSITION TESTS
  // =============================================================================

  describe('State Transitions', () => {
    test('should handle loading states correctly during async operations', async () => {
      global.selectNotificationProvider = jest.fn().mockReturnValue(NotificationProvider.FCM);
      global.loadNotificationProvider = jest.fn().mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve({ init: jest.fn() }), 100))
      );
      global.createNotificationChannels = jest.fn().mockResolvedValue(undefined);

      const store = createTestStore();

      // Start async operation
      const promise = store.dispatch(initializeNotifications({ platform: 'android', region: 'US' }));

      // Check pending state
      let state = store.getState().notifications;
      expect(state.isLoading).toBe(true);
      expect(state.error).toBeNull();

      // Wait for completion
      await promise;

      // Check fulfilled state
      state = store.getState().notifications;
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });

    test('should maintain state immutability', () => {
      const store = createTestStore();
      const initialState = store.getState().notifications;

      store.dispatch(setActiveProvider(NotificationProvider.FCM));

      const newState = store.getState().notifications;
      expect(newState).not.toBe(initialState);
      expect(newState.activeProvider).toBe(NotificationProvider.FCM);
    });
  });
});
