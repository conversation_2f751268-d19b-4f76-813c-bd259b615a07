// V7 Notification Infrastructure Demo
// Example usage of the notification system for testing and development

import {
  initializeProvider,
  getCurrentProvider,
  getNotificationToken,
  requestNotificationPermissions,
  getNotificationPermissions,
  scheduleLocalNotification,
  cancelLocalNotification,
  cancelAllLocalNotifications,
  setBadgeCount,
  getBadgeCount,
  getScheduledNotifications,
  getDisplayedNotifications,
} from '../services/NotificationService';
import { initializeNotificationEventHandlers } from '../services/NotificationEventHandler';
import { NotificationProvider } from '../types';

/**
 * Demo class for testing notification infrastructure
 */
export class NotificationDemo {
  private eventHandlers: any = null;

  /**
   * Initialize the notification system
   */
  async initialize(): Promise<void> {
    try {
      console.log('🚀 Initializing notification demo...');

      // Initialize provider
      const provider = await initializeProvider();
      console.log(`✅ Provider initialized: ${provider.provider}`);

      // Initialize event handlers
      this.eventHandlers = await initializeNotificationEventHandlers();
      console.log('✅ Event handlers initialized');

      console.log('✅ Notification demo initialization complete');
    } catch (error) {
      console.error('❌ Failed to initialize notification demo:', error);
      throw error;
    }
  }

  /**
   * Test permission management
   */
  async testPermissions(): Promise<void> {
    console.log('\n📋 Testing Permission Management...');

    try {
      // Check current permissions
      const currentPermissions = await getNotificationPermissions();
      console.log('Current permissions:', currentPermissions);

      // Request permissions
      const requestedPermissions = await requestNotificationPermissions();
      console.log('Requested permissions:', requestedPermissions);

      if (requestedPermissions?.authorizationStatus === 'authorized') {
        console.log('✅ Notifications are authorized');
      } else {
        console.log('⚠️ Notifications are not authorized');
      }
    } catch (error) {
      console.error('❌ Permission test failed:', error);
    }
  }

  /**
   * Test token management
   */
  async testTokenManagement(): Promise<void> {
    console.log('\n🔑 Testing Token Management...');

    try {
      const token = await getNotificationToken();
      if (token) {
        console.log(`✅ Token retrieved: ${token.substring(0, 20)}...`);
      } else {
        console.log('⚠️ No token available');
      }
    } catch (error) {
      console.error('❌ Token test failed:', error);
    }
  }

  /**
   * Test local notifications
   */
  async testLocalNotifications(): Promise<void> {
    console.log('\n📱 Testing Local Notifications...');

    try {
      // Schedule immediate notification
      const immediateId = await scheduleLocalNotification({
        title: 'Immediate Test',
        body: 'This notification should appear immediately',
        data: { type: 'test', immediate: true },
      });
      console.log(`✅ Immediate notification scheduled: ${immediateId}`);

      // Schedule delayed notification (5 seconds)
      const delayedId = await scheduleLocalNotification({
        title: 'Delayed Test',
        body: 'This notification was scheduled for 5 seconds',
        data: { type: 'test', delayed: true },
        trigger: {
          type: 'timestamp',
          timestamp: Date.now() + 5000, // 5 seconds from now
        },
      });
      console.log(`✅ Delayed notification scheduled: ${delayedId}`);

      // Schedule repeating notification (10 seconds, daily)
      const repeatingId = await scheduleLocalNotification({
        title: 'Repeating Test',
        body: 'This notification repeats daily',
        data: { type: 'test', repeating: true },
        trigger: {
          type: 'timestamp',
          timestamp: Date.now() + 10000, // 10 seconds from now
          repeats: true,
        },
      });
      console.log(`✅ Repeating notification scheduled: ${repeatingId}`);

      // Get scheduled notifications
      const scheduled = await getScheduledNotifications();
      console.log(`📋 Scheduled notifications: ${scheduled.length}`);

      // Test cancellation after 3 seconds
      setTimeout(async () => {
        try {
          await cancelLocalNotification(delayedId);
          console.log(`✅ Cancelled notification: ${delayedId}`);
        } catch (error) {
          console.warn('Failed to cancel notification:', error);
        }
      }, 3000);

    } catch (error) {
      console.error('❌ Local notification test failed:', error);
    }
  }

  /**
   * Test badge management
   */
  async testBadgeManagement(): Promise<void> {
    console.log('\n🔴 Testing Badge Management...');

    try {
      // Get current badge count
      const currentCount = await getBadgeCount();
      console.log(`Current badge count: ${currentCount}`);

      // Set badge count to 5
      await setBadgeCount(5);
      console.log('✅ Badge count set to 5');

      // Verify badge count
      const newCount = await getBadgeCount();
      console.log(`New badge count: ${newCount}`);

      // Clear badge count after 5 seconds
      setTimeout(async () => {
        try {
          await setBadgeCount(0);
          console.log('✅ Badge count cleared');
        } catch (error) {
          console.warn('Failed to clear badge count:', error);
        }
      }, 5000);

    } catch (error) {
      console.error('❌ Badge management test failed:', error);
    }
  }

  /**
   * Test provider capabilities
   */
  async testProviderCapabilities(): Promise<void> {
    console.log('\n⚙️ Testing Provider Capabilities...');

    try {
      const provider = getCurrentProvider();
      if (provider) {
        console.log(`Provider: ${provider.provider}`);
        console.log('Capabilities:', provider.capabilities);
        console.log(`Initialized: ${provider.isInitialized}`);
        console.log(`Available: ${provider.isAvailable}`);
      } else {
        console.log('⚠️ No provider available');
      }
    } catch (error) {
      console.error('❌ Provider capabilities test failed:', error);
    }
  }

  /**
   * Run all tests
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 Running all notification tests...\n');

    await this.testPermissions();
    await this.testTokenManagement();
    await this.testProviderCapabilities();
    await this.testLocalNotifications();
    await this.testBadgeManagement();

    console.log('\n✅ All notification tests completed');
  }

  /**
   * Cleanup demo resources
   */
  cleanup(): void {
    console.log('🧹 Cleaning up notification demo...');

    if (this.eventHandlers) {
      this.eventHandlers.cleanup();
      this.eventHandlers = null;
    }

    // Cancel all local notifications
    cancelAllLocalNotifications().catch(error => {
      console.warn('Failed to cancel all notifications during cleanup:', error);
    });

    console.log('✅ Notification demo cleanup complete');
  }

  /**
   * Get system status
   */
  async getStatus(): Promise<any> {
    try {
      const provider = getCurrentProvider();
      const permissions = await getNotificationPermissions();
      const token = await getNotificationToken();
      const badgeCount = await getBadgeCount();
      const scheduled = await getScheduledNotifications();
      const displayed = await getDisplayedNotifications();

      return {
        provider: provider ? {
          type: provider.provider,
          initialized: provider.isInitialized,
          available: provider.isAvailable,
          capabilities: provider.capabilities,
        } : null,
        permissions,
        token: token ? `${token.substring(0, 20)}...` : null,
        badgeCount,
        scheduledCount: scheduled.length,
        displayedCount: displayed.length,
        eventHandlersInitialized: this.eventHandlers?.isInitialized || false,
      };
    } catch (error) {
      console.error('Failed to get notification status:', error);
      return { error: error.message };
    }
  }
}

/**
 * Quick test function for development
 */
export async function quickNotificationTest(): Promise<void> {
  const demo = new NotificationDemo();
  
  try {
    await demo.initialize();
    await demo.runAllTests();
    
    // Show status
    const status = await demo.getStatus();
    console.log('\n📊 Final Status:', JSON.stringify(status, null, 2));
    
  } catch (error) {
    console.error('Quick test failed:', error);
  } finally {
    demo.cleanup();
  }
}

/**
 * Simple notification test for immediate feedback
 */
export async function simpleNotificationTest(): Promise<boolean> {
  try {
    console.log('🔔 Running simple notification test...');

    // Request permissions
    const permissions = await requestNotificationPermissions();
    if (permissions?.authorizationStatus !== 'authorized') {
      console.log('⚠️ Notifications not authorized');
      return false;
    }

    // Schedule a test notification
    const notificationId = await scheduleLocalNotification({
      title: 'Test Notification',
      body: 'If you see this, notifications are working!',
      data: { test: true },
    });

    console.log(`✅ Test notification scheduled: ${notificationId}`);
    return true;
  } catch (error) {
    console.error('❌ Simple notification test failed:', error);
    return false;
  }
}
