// V7 APNs Notification Provider
// iOS-specific notification provider using @react-native-community/push-notification-ios

import { Platform } from 'react-native';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { BaseNotificationProvider, ProviderCapabilities } from './base';
import { NotificationProvider, NotificationData, NotificationSettings, NotificationError } from '../types';

/**
 * APNs (Apple Push Notification service) Provider
 * Handles iOS-specific notification functionality
 */
export class APNsProvider extends BaseNotificationProvider {
  readonly provider = NotificationProvider.APNS;
  readonly capabilities: ProviderCapabilities = {
    richNotifications: true,
    badgeSupport: true,
    soundSupport: true,
    imageSupport: true,
    actionSupport: true,
    scheduledNotifications: false, // Handled by notifee
  };

  async initialize(): Promise<void> {
    if (Platform.OS !== 'ios') {
      throw this.createError(
        'PLATFORM_UNSUPPORTED',
        'APNs provider can only be used on iOS',
        { platform: Platform.OS }
      );
    }

    try {
      this.log('Initializing APNs provider...');

      // Request permissions during initialization
      const settings = await this.requestPermissions();
      this.log('APNs permissions requested', 'info', settings);

      // Set up event listeners
      this.setupEventListeners();

      this.setInitialized(true);
      this.log('APNs provider initialized successfully');
    } catch (error) {
      this.log('Failed to initialize APNs provider', 'error', error);
      throw this.createError(
        'INITIALIZATION_FAILED',
        'Failed to initialize APNs provider',
        { error }
      );
    }
  }

  async requestPermissions(): Promise<NotificationSettings> {
    try {
      this.log('Requesting APNs permissions...');

      return new Promise((resolve, reject) => {
        PushNotificationIOS.requestPermissions({
          alert: true,
          badge: true,
          sound: true,
          critical: false,
        }).then((permissions) => {
          const settings: NotificationSettings = {
            authorizationStatus: permissions.alert ? 'authorized' : 'denied',
            sound: permissions.sound ? 1 : 0,
            badge: permissions.badge ? 1 : 0,
            alert: permissions.alert ? 1 : 0,
            criticalAlert: permissions.critical ? 1 : 0,
            provisional: 0, // Not supported in this API
            lockScreen: permissions.alert ? 1 : 0,
            notificationCenter: permissions.alert ? 1 : 0,
          };

          this.log('APNs permissions granted', 'info', settings);
          resolve(settings);
        }).catch((error) => {
          this.log('APNs permission request failed', 'error', error);
          reject(this.createError(
            'PERMISSION_REQUEST_FAILED',
            'Failed to request APNs permissions',
            { error }
          ));
        });
      });
    } catch (error) {
      this.log('APNs permission request error', 'error', error);
      throw this.createError(
        'PERMISSION_REQUEST_ERROR',
        'Error requesting APNs permissions',
        { error }
      );
    }
  }

  async getPermissionStatus(): Promise<NotificationSettings> {
    try {
      return new Promise((resolve) => {
        PushNotificationIOS.checkPermissions((permissions) => {
          const settings: NotificationSettings = {
            authorizationStatus: permissions.alert ? 'authorized' : 'denied',
            sound: permissions.sound ? 1 : 0,
            badge: permissions.badge ? 1 : 0,
            alert: permissions.alert ? 1 : 0,
            criticalAlert: permissions.critical ? 1 : 0,
            provisional: 0,
            lockScreen: permissions.alert ? 1 : 0,
            notificationCenter: permissions.alert ? 1 : 0,
          };

          resolve(settings);
        });
      });
    } catch (error) {
      this.log('Failed to get APNs permission status', 'error', error);
      throw this.createError(
        'PERMISSION_STATUS_ERROR',
        'Failed to get APNs permission status',
        { error }
      );
    }
  }

  async getToken(): Promise<string | null> {
    try {
      this.log('Getting APNs device token...');

      return new Promise((resolve, reject) => {
        // Set up token listener
        const tokenListener = (token: string) => {
          this.log('APNs token received', 'info', { tokenLength: token?.length });
          this.setToken(token);
          resolve(token);
        };

        const failureListener = (error: any) => {
          this.log('APNs token registration failed', 'error', error);
          reject(this.createError(
            'TOKEN_REGISTRATION_FAILED',
            'Failed to register for APNs token',
            { error }
          ));
        };

        // Add listeners
        PushNotificationIOS.addEventListener('register', tokenListener);
        PushNotificationIOS.addEventListener('registrationError', failureListener);

        // Request token
        PushNotificationIOS.requestPermissions();

        // Cleanup listeners after timeout
        setTimeout(() => {
          PushNotificationIOS.removeEventListener('register', tokenListener);
          PushNotificationIOS.removeEventListener('registrationError', failureListener);
        }, 10000);
      });
    } catch (error) {
      this.log('APNs token request error', 'error', error);
      throw this.createError(
        'TOKEN_REQUEST_ERROR',
        'Error requesting APNs token',
        { error }
      );
    }
  }

  onTokenRefresh(callback: (token: string) => void): () => void {
    this.log('Setting up APNs token refresh listener...');

    const tokenListener = (token: string) => {
      this.log('APNs token refreshed', 'info', { tokenLength: token?.length });
      this.setToken(token);
      callback(token);
    };

    PushNotificationIOS.addEventListener('register', tokenListener);

    const unsubscribe = () => {
      PushNotificationIOS.removeEventListener('register', tokenListener);
    };

    this.addUnsubscriber(unsubscribe);
    return unsubscribe;
  }

  onNotificationReceived(callback: (notification: NotificationData) => void): () => void {
    this.log('Setting up APNs notification received listener...');

    const notificationListener = (notification: any) => {
      try {
        const parsedNotification = this.parseAPNsNotification(notification);
        if (parsedNotification) {
          this.log('APNs notification received', 'info', { id: parsedNotification.id });
          callback(parsedNotification);
        }
      } catch (error) {
        this.log('Error parsing APNs notification', 'error', { error, notification });
      }
    };

    PushNotificationIOS.addEventListener('notification', notificationListener);

    const unsubscribe = () => {
      PushNotificationIOS.removeEventListener('notification', notificationListener);
    };

    this.addUnsubscriber(unsubscribe);
    return unsubscribe;
  }

  onNotificationOpened(callback: (notification: NotificationData) => void): () => void {
    this.log('Setting up APNs notification opened listener...');

    const notificationListener = (notification: any) => {
      try {
        // Check if notification was tapped (userInteraction is true)
        if (notification.userInteraction) {
          const parsedNotification = this.parseAPNsNotification(notification);
          if (parsedNotification) {
            this.log('APNs notification opened', 'info', { id: parsedNotification.id });
            callback(parsedNotification);
          }
        }
      } catch (error) {
        this.log('Error parsing opened APNs notification', 'error', { error, notification });
      }
    };

    PushNotificationIOS.addEventListener('notification', notificationListener);

    const unsubscribe = () => {
      PushNotificationIOS.removeEventListener('notification', notificationListener);
    };

    this.addUnsubscriber(unsubscribe);
    return unsubscribe;
  }

  async getInitialNotification(): Promise<NotificationData | null> {
    try {
      this.log('Getting initial APNs notification...');

      return new Promise((resolve) => {
        PushNotificationIOS.getInitialNotification().then((notification) => {
          if (notification) {
            const parsedNotification = this.parseAPNsNotification(notification);
            this.log('Initial APNs notification found', 'info', { id: parsedNotification?.id });
            resolve(parsedNotification);
          } else {
            this.log('No initial APNs notification found');
            resolve(null);
          }
        }).catch((error) => {
          this.log('Error getting initial APNs notification', 'error', error);
          resolve(null);
        });
      });
    } catch (error) {
      this.log('Error in getInitialNotification', 'error', error);
      return null;
    }
  }

  async displayNotification(notification: NotificationData): Promise<void> {
    try {
      if (!this.validateNotificationData(notification)) {
        throw this.createError(
          'INVALID_NOTIFICATION_DATA',
          'Invalid notification data provided',
          { notification }
        );
      }

      this.log('Displaying APNs notification', 'info', { id: notification.id });

      // Use notifee for local notification display
      const notifee = await import('@notifee/react-native');
      
      await notifee.default.displayNotification({
        id: notification.id,
        title: notification.title,
        subtitle: notification.subtitle,
        body: notification.body,
        data: notification.data,
        ios: {
          sound: notification.sound,
          badge: notification.data?.badge,
          attachments: notification.image ? [{ url: notification.image }] : undefined,
        },
      });

      this.log('APNs notification displayed successfully', 'info', { id: notification.id });
    } catch (error) {
      this.log('Failed to display APNs notification', 'error', { error, notification });
      throw this.createError(
        'DISPLAY_NOTIFICATION_FAILED',
        'Failed to display APNs notification',
        { error, notification }
      );
    }
  }

  async setBadgeCount(count: number): Promise<void> {
    try {
      this.log('Setting APNs badge count', 'info', { count });
      PushNotificationIOS.setApplicationIconBadgeNumber(count);
    } catch (error) {
      this.log('Failed to set APNs badge count', 'error', { error, count });
      throw this.createError(
        'BADGE_UPDATE_FAILED',
        'Failed to set APNs badge count',
        { error, count }
      );
    }
  }

  async getBadgeCount(): Promise<number> {
    try {
      return new Promise((resolve) => {
        PushNotificationIOS.getApplicationIconBadgeNumber((count) => {
          this.log('APNs badge count retrieved', 'info', { count });
          resolve(count);
        });
      });
    } catch (error) {
      this.log('Failed to get APNs badge count', 'error', error);
      return 0;
    }
  }

  private setupEventListeners(): void {
    this.log('Setting up APNs event listeners...');

    // Set up registration error listener
    const registrationErrorListener = (error: any) => {
      this.log('APNs registration error', 'error', error);
    };

    PushNotificationIOS.addEventListener('registrationError', registrationErrorListener);

    this.addUnsubscriber(() => {
      PushNotificationIOS.removeEventListener('registrationError', registrationErrorListener);
    });
  }

  private parseAPNsNotification(notification: any): NotificationData | null {
    try {
      if (!notification) return null;

      const data = notification.getData ? notification.getData() : notification;
      
      return {
        id: data.id || data.notificationId || `apns-${Date.now()}`,
        title: data.title || notification.getAlert?.() || '',
        subtitle: data.subtitle || '',
        body: data.body || data.message || notification.getMessage?.() || '',
        image: data.image,
        sound: data.sound || notification.getSound?.(),
        data: data,
        options: {
          sync: data.sync,
          banner: data.banner,
          nav: data.nav,
        },
      };
    } catch (error) {
      this.log('Error parsing APNs notification', 'error', { error, notification });
      return null;
    }
  }
}
