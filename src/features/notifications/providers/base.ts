// V7 Notification Provider Base Interface
// Defines the contract for all notification providers (APNs, FCM, JPush)

import { NotificationProvider, NotificationToken, NotificationData, NotificationSettings, NotificationError } from '../types';

/**
 * Base notification provider interface
 * All platform-specific providers must implement this interface
 */
export interface INotificationProvider {
  readonly provider: NotificationProvider;
  readonly isAvailable: boolean;
  readonly capabilities: ProviderCapabilities;

  // Lifecycle methods
  initialize(): Promise<void>;
  cleanup(): void;

  // Registration and permissions
  requestPermissions(): Promise<NotificationSettings>;
  getPermissionStatus(): Promise<NotificationSettings>;
  
  // Token management
  getToken(): Promise<string | null>;
  onTokenRefresh(callback: (token: string) => void): () => void;

  // Notification handling
  onNotificationReceived(callback: (notification: NotificationData) => void): () => void;
  onNotificationOpened(callback: (notification: NotificationData) => void): () => void;
  getInitialNotification(): Promise<NotificationData | null>;

  // Display notifications
  displayNotification(notification: NotificationData): Promise<void>;
  cancelNotification(id: string): Promise<void>;
  cancelAllNotifications(): Promise<void>;

  // Badge management
  setBadgeCount(count: number): Promise<void>;
  getBadgeCount(): Promise<number>;
}

/**
 * Provider capabilities interface
 */
export interface ProviderCapabilities {
  richNotifications: boolean;
  badgeSupport: boolean;
  soundSupport: boolean;
  imageSupport: boolean;
  actionSupport: boolean;
  scheduledNotifications: boolean;
}

/**
 * Provider registration result
 */
export interface ProviderRegistrationResult {
  success: boolean;
  token?: string;
  settings?: NotificationSettings;
  error?: NotificationError;
}

/**
 * Abstract base provider class
 * Provides common functionality for all providers
 */
export abstract class BaseNotificationProvider implements INotificationProvider {
  abstract readonly provider: NotificationProvider;
  abstract readonly capabilities: ProviderCapabilities;
  
  protected _isInitialized = false;
  protected _token: string | null = null;
  protected _unsubscribers: Array<() => void> = [];

  get isAvailable(): boolean {
    return this._isInitialized;
  }

  get isInitialized(): boolean {
    return this._isInitialized;
  }

  get currentToken(): string | null {
    return this._token;
  }

  // Abstract methods that must be implemented by subclasses
  abstract initialize(): Promise<void>;
  abstract requestPermissions(): Promise<NotificationSettings>;
  abstract getPermissionStatus(): Promise<NotificationSettings>;
  abstract getToken(): Promise<string | null>;
  abstract onTokenRefresh(callback: (token: string) => void): () => void;
  abstract onNotificationReceived(callback: (notification: NotificationData) => void): () => void;
  abstract onNotificationOpened(callback: (notification: NotificationData) => void): () => void;
  abstract getInitialNotification(): Promise<NotificationData | null>;
  abstract displayNotification(notification: NotificationData): Promise<void>;
  abstract setBadgeCount(count: number): Promise<void>;
  abstract getBadgeCount(): Promise<number>;

  // Common implementation for all providers
  async cancelNotification(id: string): Promise<void> {
    try {
      // Default implementation using notifee
      const notifee = await import('@notifee/react-native');
      await notifee.default.cancelNotification(id);
    } catch (error) {
      console.warn(`Failed to cancel notification ${id}:`, error);
    }
  }

  async cancelAllNotifications(): Promise<void> {
    try {
      // Default implementation using notifee
      const notifee = await import('@notifee/react-native');
      await notifee.default.cancelAllNotifications();
    } catch (error) {
      console.warn('Failed to cancel all notifications:', error);
    }
  }

  cleanup(): void {
    // Unsubscribe from all listeners
    this._unsubscribers.forEach(unsubscribe => {
      try {
        unsubscribe();
      } catch (error) {
        console.warn('Error during provider cleanup:', error);
      }
    });
    this._unsubscribers = [];
    this._isInitialized = false;
    this._token = null;
  }

  protected addUnsubscriber(unsubscriber: () => void): void {
    this._unsubscribers.push(unsubscriber);
  }

  protected setToken(token: string | null): void {
    this._token = token;
  }

  protected setInitialized(initialized: boolean): void {
    this._isInitialized = initialized;
  }

  // Helper method to create standardized errors
  protected createError(code: string, message: string, context?: any): NotificationError {
    return {
      code,
      message,
      provider: this.provider,
      context,
    };
  }

  // Helper method to validate notification data
  protected validateNotificationData(notification: NotificationData): boolean {
    return !!(notification.id && notification.title && notification.body);
  }

  // Helper method to log provider actions
  protected log(message: string, level: 'info' | 'warn' | 'error' = 'info', data?: any): void {
    const prefix = `[${this.provider.toUpperCase()}]`;
    
    switch (level) {
      case 'error':
        console.error(prefix, message, data);
        break;
      case 'warn':
        console.warn(prefix, message, data);
        break;
      default:
        console.log(prefix, message, data);
        break;
    }
  }
}

/**
 * Provider factory interface
 */
export interface IProviderFactory {
  createProvider(provider: NotificationProvider): INotificationProvider;
  getAvailableProviders(): NotificationProvider[];
  selectBestProvider(): NotificationProvider;
}
