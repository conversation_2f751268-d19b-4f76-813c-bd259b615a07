// V7 FCM Notification Provider
// Android-specific notification provider using @react-native-firebase/messaging

import { Platform } from 'react-native';
import messaging, { FirebaseMessagingTypes } from '@react-native-firebase/messaging';
import { BaseNotificationProvider, ProviderCapabilities } from './base';
import { NotificationProvider, NotificationData, NotificationSettings, NotificationError } from '../types';

/**
 * FCM (Firebase Cloud Messaging) Provider
 * Handles Android-specific notification functionality
 */
export class FCMProvider extends BaseNotificationProvider {
  readonly provider = NotificationProvider.FCM;
  readonly capabilities: ProviderCapabilities = {
    richNotifications: true,
    badgeSupport: false, // Android doesn't have native badge support
    soundSupport: true,
    imageSupport: true,
    actionSupport: true,
    scheduledNotifications: false, // Handled by notifee
  };

  async initialize(): Promise<void> {
    if (Platform.OS !== 'android') {
      throw this.createError(
        'PLATFORM_UNSUPPORTED',
        'FCM provider can only be used on Android',
        { platform: Platform.OS }
      );
    }

    try {
      this.log('Initializing FCM provider...');

      // Request permissions during initialization
      const settings = await this.requestPermissions();
      this.log('FCM permissions requested', 'info', settings);

      // Set up event listeners
      this.setupEventListeners();

      this.setInitialized(true);
      this.log('FCM provider initialized successfully');
    } catch (error) {
      this.log('Failed to initialize FCM provider', 'error', error);
      throw this.createError(
        'INITIALIZATION_FAILED',
        'Failed to initialize FCM provider',
        { error }
      );
    }
  }

  async requestPermissions(): Promise<NotificationSettings> {
    try {
      this.log('Requesting FCM permissions...');

      const authStatus = await messaging().requestPermission();
      const enabled = authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
                     authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      const settings: NotificationSettings = {
        authorizationStatus: enabled ? 'authorized' : 'denied',
        sound: enabled ? 1 : 0,
        badge: 0, // Android doesn't support badges natively
        alert: enabled ? 1 : 0,
        criticalAlert: 0, // Not supported on Android
        provisional: authStatus === messaging.AuthorizationStatus.PROVISIONAL ? 1 : 0,
        lockScreen: enabled ? 1 : 0,
        notificationCenter: enabled ? 1 : 0,
      };

      this.log('FCM permissions result', 'info', { authStatus, settings });
      return settings;
    } catch (error) {
      this.log('FCM permission request failed', 'error', error);
      throw this.createError(
        'PERMISSION_REQUEST_FAILED',
        'Failed to request FCM permissions',
        { error }
      );
    }
  }

  async getPermissionStatus(): Promise<NotificationSettings> {
    try {
      const authStatus = await messaging().hasPermission();
      const enabled = authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
                     authStatus === messaging.AuthorizationStatus.PROVISIONAL;

      const settings: NotificationSettings = {
        authorizationStatus: enabled ? 'authorized' : 'denied',
        sound: enabled ? 1 : 0,
        badge: 0,
        alert: enabled ? 1 : 0,
        criticalAlert: 0,
        provisional: authStatus === messaging.AuthorizationStatus.PROVISIONAL ? 1 : 0,
        lockScreen: enabled ? 1 : 0,
        notificationCenter: enabled ? 1 : 0,
      };

      return settings;
    } catch (error) {
      this.log('Failed to get FCM permission status', 'error', error);
      throw this.createError(
        'PERMISSION_STATUS_ERROR',
        'Failed to get FCM permission status',
        { error }
      );
    }
  }

  async getToken(): Promise<string | null> {
    try {
      this.log('Getting FCM device token...');

      const token = await messaging().getToken();
      
      if (token) {
        this.log('FCM token received', 'info', { tokenLength: token.length });
        this.setToken(token);
        return token;
      } else {
        this.log('No FCM token received', 'warn');
        return null;
      }
    } catch (error) {
      this.log('FCM token request failed', 'error', error);
      throw this.createError(
        'TOKEN_REQUEST_FAILED',
        'Failed to get FCM token',
        { error }
      );
    }
  }

  onTokenRefresh(callback: (token: string) => void): () => void {
    this.log('Setting up FCM token refresh listener...');

    const unsubscribe = messaging().onTokenRefresh((token) => {
      this.log('FCM token refreshed', 'info', { tokenLength: token.length });
      this.setToken(token);
      callback(token);
    });

    this.addUnsubscriber(unsubscribe);
    return unsubscribe;
  }

  onNotificationReceived(callback: (notification: NotificationData) => void): () => void {
    this.log('Setting up FCM notification received listener...');

    const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      try {
        const parsedNotification = this.parseFCMNotification(remoteMessage);
        if (parsedNotification) {
          this.log('FCM notification received', 'info', { id: parsedNotification.id });
          callback(parsedNotification);
        }
      } catch (error) {
        this.log('Error parsing FCM notification', 'error', { error, remoteMessage });
      }
    });

    this.addUnsubscriber(unsubscribe);
    return unsubscribe;
  }

  onNotificationOpened(callback: (notification: NotificationData) => void): () => void {
    this.log('Setting up FCM notification opened listener...');

    const unsubscribe = messaging().onNotificationOpenedApp((remoteMessage) => {
      try {
        const parsedNotification = this.parseFCMNotification(remoteMessage);
        if (parsedNotification) {
          this.log('FCM notification opened', 'info', { id: parsedNotification.id });
          callback(parsedNotification);
        }
      } catch (error) {
        this.log('Error parsing opened FCM notification', 'error', { error, remoteMessage });
      }
    });

    this.addUnsubscriber(unsubscribe);
    return unsubscribe;
  }

  async getInitialNotification(): Promise<NotificationData | null> {
    try {
      this.log('Getting initial FCM notification...');

      const remoteMessage = await messaging().getInitialNotification();
      
      if (remoteMessage) {
        const parsedNotification = this.parseFCMNotification(remoteMessage);
        this.log('Initial FCM notification found', 'info', { id: parsedNotification?.id });
        return parsedNotification;
      } else {
        this.log('No initial FCM notification found');
        return null;
      }
    } catch (error) {
      this.log('Error getting initial FCM notification', 'error', error);
      return null;
    }
  }

  async displayNotification(notification: NotificationData): Promise<void> {
    try {
      if (!this.validateNotificationData(notification)) {
        throw this.createError(
          'INVALID_NOTIFICATION_DATA',
          'Invalid notification data provided',
          { notification }
        );
      }

      this.log('Displaying FCM notification', 'info', { id: notification.id });

      // Use notifee for local notification display
      const notifee = await import('@notifee/react-native');
      
      await notifee.default.displayNotification({
        id: notification.id,
        title: notification.title,
        subtitle: notification.subtitle,
        body: notification.body,
        data: notification.data,
        android: {
          channelId: notification.data?.channelId || 'default',
          smallIcon: 'ic_notification',
          largeIcon: notification.image,
          sound: notification.sound,
          importance: notifee.AndroidImportance.HIGH,
          pressAction: {
            id: 'default',
            launchActivity: 'default',
          },
          style: notification.image ? {
            type: notifee.AndroidStyle.BIGPICTURE,
            picture: notification.image,
          } : undefined,
        },
      });

      this.log('FCM notification displayed successfully', 'info', { id: notification.id });
    } catch (error) {
      this.log('Failed to display FCM notification', 'error', { error, notification });
      throw this.createError(
        'DISPLAY_NOTIFICATION_FAILED',
        'Failed to display FCM notification',
        { error, notification }
      );
    }
  }

  async setBadgeCount(count: number): Promise<void> {
    try {
      this.log('Setting FCM badge count (using notifee)', 'info', { count });
      
      // Use notifee for badge management on Android
      const notifee = await import('@notifee/react-native');
      await notifee.default.setBadgeCount(count);
    } catch (error) {
      this.log('Failed to set FCM badge count', 'error', { error, count });
      throw this.createError(
        'BADGE_UPDATE_FAILED',
        'Failed to set FCM badge count',
        { error, count }
      );
    }
  }

  async getBadgeCount(): Promise<number> {
    try {
      // Use notifee for badge management on Android
      const notifee = await import('@notifee/react-native');
      const count = await notifee.default.getBadgeCount();
      
      this.log('FCM badge count retrieved', 'info', { count });
      return count;
    } catch (error) {
      this.log('Failed to get FCM badge count', 'error', error);
      return 0;
    }
  }

  private setupEventListeners(): void {
    this.log('Setting up FCM event listeners...');

    // Set up background message handler
    messaging().setBackgroundMessageHandler(async (remoteMessage) => {
      this.log('FCM background message received', 'info', { 
        messageId: remoteMessage.messageId 
      });
      
      // Background messages are handled by the system
      // We just log them for debugging
    });
  }

  private parseFCMNotification(remoteMessage: FirebaseMessagingTypes.RemoteMessage): NotificationData | null {
    try {
      if (!remoteMessage) return null;

      const { notification, data, messageId } = remoteMessage;
      
      return {
        id: data?.id || messageId || `fcm-${Date.now()}`,
        title: notification?.title || data?.title || '',
        subtitle: notification?.subtitle || data?.subtitle || '',
        body: notification?.body || data?.body || '',
        image: notification?.android?.imageUrl || data?.image,
        sound: notification?.android?.sound || data?.sound,
        data: data || {},
        options: {
          sync: data?.sync,
          banner: data?.banner === 'true' || data?.banner === true,
          nav: data?.nav ? JSON.parse(data.nav) : undefined,
        },
      };
    } catch (error) {
      this.log('Error parsing FCM notification', 'error', { error, remoteMessage });
      return null;
    }
  }
}
