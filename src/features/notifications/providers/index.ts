// V7 Notification Provider Factory
// Creates and manages notification providers based on platform and region

import { Platform } from 'react-native';
import { INotificationProvider, IProviderFactory } from './base';
import { APNsProvider } from './apns';
import { FCMProvider } from './fcm';
import { NotificationProvider } from '../types';

/**
 * Provider Factory Implementation
 * Creates appropriate providers based on platform and configuration
 */
export class NotificationProviderFactory implements IProviderFactory {
  private static instance: NotificationProviderFactory;
  private providers: Map<NotificationProvider, INotificationProvider> = new Map();

  private constructor() {}

  static getInstance(): NotificationProviderFactory {
    if (!NotificationProviderFactory.instance) {
      NotificationProviderFactory.instance = new NotificationProviderFactory();
    }
    return NotificationProviderFactory.instance;
  }

  createProvider(provider: NotificationProvider): INotificationProvider {
    // Return existing provider if already created
    if (this.providers.has(provider)) {
      return this.providers.get(provider)!;
    }

    let providerInstance: INotificationProvider;

    switch (provider) {
      case NotificationProvider.APNS:
        if (Platform.OS !== 'ios') {
          throw new Error('APNs provider can only be used on iOS');
        }
        providerInstance = new APNsProvider();
        break;

      case NotificationProvider.FCM:
        if (Platform.OS !== 'android') {
          throw new Error('FCM provider can only be used on Android');
        }
        providerInstance = new FCMProvider();
        break;

      case NotificationProvider.JPUSH:
        // TODO: Implement JPush provider for Chinese market
        throw new Error('JPush provider not yet implemented');

      default:
        throw new Error(`Unsupported notification provider: ${provider}`);
    }

    // Cache the provider instance
    this.providers.set(provider, providerInstance);
    return providerInstance;
  }

  getAvailableProviders(): NotificationProvider[] {
    const available: NotificationProvider[] = [];

    if (Platform.OS === 'ios') {
      available.push(NotificationProvider.APNS);
    }

    if (Platform.OS === 'android') {
      available.push(NotificationProvider.FCM);
      // TODO: Add JPush for Chinese market detection
      // if (isChineseMarket()) {
      //   available.push(NotificationProvider.JPUSH);
      // }
    }

    return available;
  }

  selectBestProvider(): NotificationProvider {
    const available = this.getAvailableProviders();

    if (available.length === 0) {
      throw new Error('No notification providers available for this platform');
    }

    // For now, return the first available provider
    // TODO: Add logic for Chinese market detection and JPush selection
    return available[0];
  }

  /**
   * Get provider instance if it exists
   */
  getProvider(provider: NotificationProvider): INotificationProvider | null {
    return this.providers.get(provider) || null;
  }

  /**
   * Cleanup all providers
   */
  cleanup(): void {
    this.providers.forEach((provider) => {
      try {
        provider.cleanup();
      } catch (error) {
        console.warn('Error cleaning up provider:', error);
      }
    });
    this.providers.clear();
  }

  /**
   * Get all created provider instances
   */
  getAllProviders(): INotificationProvider[] {
    return Array.from(this.providers.values());
  }
}

/**
 * Convenience functions for provider management
 */

// Global factory instance
const factory = NotificationProviderFactory.getInstance();

/**
 * Create a notification provider
 */
export function createNotificationProvider(provider: NotificationProvider): INotificationProvider {
  return factory.createProvider(provider);
}

/**
 * Get available providers for current platform
 */
export function getAvailableProviders(): NotificationProvider[] {
  return factory.getAvailableProviders();
}

/**
 * Select the best provider for current platform
 */
export function selectBestProvider(): NotificationProvider {
  return factory.selectBestProvider();
}

/**
 * Get existing provider instance
 */
export function getProvider(provider: NotificationProvider): INotificationProvider | null {
  return factory.getProvider(provider);
}

/**
 * Cleanup all providers
 */
export function cleanupProviders(): void {
  factory.cleanup();
}

/**
 * Get all created provider instances
 */
export function getAllProviders(): INotificationProvider[] {
  return factory.getAllProviders();
}

// Re-export types and classes
export * from './base';
export { APNsProvider } from './apns';
export { FCMProvider } from './fcm';

/**
 * Provider utility functions
 */
export const ProviderUtils = {
  /**
   * Check if a provider is supported on current platform
   */
  isProviderSupported(provider: NotificationProvider): boolean {
    const available = getAvailableProviders();
    return available.includes(provider);
  },

  /**
   * Get provider capabilities
   */
  getProviderCapabilities(provider: NotificationProvider) {
    const providerInstance = getProvider(provider);
    return providerInstance?.capabilities || null;
  },

  /**
   * Check if provider is initialized
   */
  isProviderInitialized(provider: NotificationProvider): boolean {
    const providerInstance = getProvider(provider);
    return providerInstance?.isInitialized || false;
  },

  /**
   * Get current token from provider
   */
  getProviderToken(provider: NotificationProvider): string | null {
    const providerInstance = getProvider(provider);
    return (providerInstance as any)?.currentToken || null;
  },
};
