// V7 Notification Action Service
// Handles notification processing, actions, and navigation
// Extracted from V6 src/lib/common/notify.js handle() function

import { AppState } from 'react-native';
import { NotificationData, NotificationPayload, ProcessedNotification } from '../types';

// =============================================================================
// NOTIFICATION PARSING
// =============================================================================

export function parseNotificationData(rawData: any): NotificationData {
  try {
    // V6 Business Logic: Parse notification data with fallbacks
    const data = typeof rawData === 'string' ? JSON.parse(rawData) : rawData;
    
    return {
      id: data.id || generateNotificationId(),
      title: data.title || '',
      subtitle: data.subtitle,
      body: data.body || '',
      image: data.image,
      sound: data.sound,
      data: data.data || {},
      options: {
        sync: data.sync,
        banner: data.banner !== false, // Default to true
        nav: data.nav,
        ...data.options,
      },
    };
  } catch (error) {
    console.error('Failed to parse notification data:', error);
    return {
      id: generateNotificationId(),
      title: 'Notification',
      body: 'New notification received',
      data: {},
      options: {},
    };
  }
}

export function parseNotificationPayload(notification: any): NotificationPayload {
  // V6 Business Logic: Extract notification payload from different sources
  if (notification.notification) {
    // FCM format
    const { id, title, body } = notification.notification;
    const data = notification.data || {};
    
    return {
      id: id || generateNotificationId(),
      title: title || '',
      body: body || '',
      data: parseNotificationData(data),
      timestamp: Date.now(),
    };
  }
  
  if (notification._data) {
    // iOS format
    const { alert, sound, badge } = notification._data;
    const data = notification._data.data || {};
    
    return {
      id: generateNotificationId(),
      title: alert?.title || '',
      body: alert?.body || '',
      sound,
      badge,
      data: parseNotificationData(data),
      timestamp: Date.now(),
    };
  }
  
  // Direct format
  return {
    id: notification.id || generateNotificationId(),
    title: notification.title || '',
    body: notification.body || '',
    data: parseNotificationData(notification.data || {}),
    timestamp: Date.now(),
  };
}

// =============================================================================
// NOTIFICATION PROCESSING
// =============================================================================

export async function processNotificationActions(
  notification: NotificationPayload,
  tapped: boolean,
  inApp: boolean
): Promise<ProcessedNotification> {
  const data = notification.data;
  const { sync, action, nav, options = {} } = data;
  
  // V6 Business Logic: Process in specific order
  
  // 1. Handle sync operations first (must complete before actions)
  if (sync && ['cache', 'all'].includes(sync)) {
    await handleSyncOperation(sync);
  }
  
  // 2. Handle fetch operations
  if (data.fetch) {
    await handleFetchOperation(data.fetch);
  }
  
  // 3. Handle engagement
  if (nav?.engage) {
    await handleEngagement(nav.engage);
  }
  
  // 4. Handle actions (when tapped OR app active)
  if (action && (tapped || inApp)) {
    await handleNotificationAction(action);
  }
  
  // 5. Handle navigation (when tapped and not locked)
  if (nav && tapped && !isAppLocked()) {
    await handleNavigation(nav);
  }
  
  // 6. Show banner if needed (when app active and not tapped)
  const shouldShowBanner = options.banner && inApp && !tapped;
  if (shouldShowBanner) {
    await displayNotificationBanner(notification);
  }
  
  return {
    id: notification.id,
    title: notification.title,
    subtitle: notification.subtitle,
    body: notification.body,
    data: notification.data,
    receivedAt: new Date().toISOString(),
    handledAt: new Date().toISOString(),
    tapped,
    inApp,
    shouldShowBanner,
    processed: true,
  };
}

// =============================================================================
// ACTION HANDLERS
// =============================================================================

async function handleSyncOperation(syncType: string): Promise<void> {
  try {
    console.log(`Handling sync operation: ${syncType}`);

    // V6 Business Logic: Use global mock function if available (for testing)
    if (typeof global !== 'undefined' && global.handleNotificationSync) {
      await global.handleNotificationSync(syncType);
      console.log(`Sync operation ${syncType} completed via global mock`);
      return;
    }

    // V7 Architecture: Sync system integration
    // TODO: Implement sync system integration with v7 architecture
    console.log('🔄 Notification sync requested:', syncType);

    // V7 Pattern: Use Redux actions instead of direct module imports
    // This should be handled by the sync feature slice when implemented
    if (syncType === 'cache') {
      console.log('📦 Cache sync requested - should dispatch to sync slice');
      // TODO: store.dispatch(syncSlice.actions.syncCache());
    } else if (syncType === 'all') {
      console.log('🔄 Full sync requested - should dispatch to sync slice');
      // TODO: store.dispatch(syncSlice.actions.syncAll());
    }

    console.log(`✅ Sync operation ${syncType} logged for v7 implementation`);
  } catch (error) {
    console.error(`Failed to handle sync operation ${syncType}:`, error);
  }
}

async function handleFetchOperation(fetchData: any): Promise<void> {
  try {
    console.log('📥 Handling fetch operation:', fetchData);

    // V7 Architecture: Fetch system integration
    // TODO: Implement fetch system integration with v7 architecture
    console.log('📥 Notification fetch requested:', fetchData);

    // V7 Pattern: Use Redux actions instead of direct module imports
    // This should be handled by the appropriate feature slice when implemented
    console.log('📦 Fetch operation should dispatch to appropriate feature slice');
    // TODO: store.dispatch(dataSlice.actions.fetchObjects(fetchData));

    console.log('✅ Fetch operation logged for v7 implementation');
  } catch (error) {
    console.error('❌ Failed to handle fetch operation:', error);
  }
}

async function handleEngagement(engageData: any): Promise<void> {
  try {
    console.log('Handling engagement:', engageData);

    // V6 Business Logic: Use global mock function if available (for testing)
    if (typeof global !== 'undefined' && global.handleNotificationEngagement) {
      await global.handleNotificationEngagement(engageData);
      console.log('Engagement handled via global mock');
      return;
    }

    // V7 Architecture: Engagement system integration
    // TODO: Implement engagement system integration with v7 architecture
    console.log('🎯 Notification engagement requested:', engageData);

    const { object, param } = engageData;

    if (object && param) {
      // V7 Pattern: Use Redux actions instead of direct model imports
      // This should be handled by the engagement feature slice when implemented
      console.log(`🎯 Engagement request: ${object} with param:`, param);

      // TODO: Dispatch engagement action to appropriate feature slice
      // Example: store.dispatch(engagementSlice.actions.handleEngagement({ object, param }));
    }

    console.log('✅ Engagement logged for v7 implementation');
  } catch (error) {
    console.error('Failed to handle engagement:', error);
  }
}

async function handleNotificationAction(actionData: any): Promise<void> {
  try {
    console.log('⚡ Handling notification action:', actionData);

    // V7 Architecture: Actions system integration
    // TODO: Implement actions system integration with v7 architecture
    console.log('⚡ Notification action requested:', actionData);

    // V7 Pattern: Use Redux actions instead of direct module imports
    // This should be handled by the actions feature slice when implemented
    console.log('⚡ Action should dispatch to actions feature slice');
    // TODO: store.dispatch(actionsSlice.actions.executeAction(actionData));

    console.log('✅ Action logged for v7 implementation');
  } catch (error) {
    console.error('❌ Failed to handle notification action:', error);
  }
}

async function handleNavigation(navData: any): Promise<void> {
  try {
    console.log('Handling navigation:', navData);

    // V7 Architecture: Navigation system integration
    // TODO: Implement navigation system integration with v7 architecture
    console.log('🧭 Notification navigation requested:', navData);

    const { route = [] } = navData;

    if (route.length > 0) {
      // V7 Pattern: Use React Navigation instead of custom navigation system
      // This should be handled by the navigation service when implemented
      console.log('🧭 Navigation route:', route);
      // TODO: navigationRef.navigate(route[0], route[1]);
    }

    console.log('✅ Navigation logged for v7 implementation');
  } catch (error) {
    console.error('Failed to handle navigation:', error);
  }
}

async function displayNotificationBanner(notification: NotificationPayload): Promise<void> {
  try {
    console.log('Displaying notification banner');
    
    // V6 Business Logic: Create banner notification
    const bannerData = {
      ...notification.data,
      options: { ...notification.data.options, banner: false }, // Prevent recursive banners
    };
    
    const { displayNotification } = await import('./NotificationService');
    
    await displayNotification({
      id: `banner_${notification.id}`,
      title: notification.title,
      body: notification.body,
      data: bannerData,
      sound: notification.sound,
      showInForeground: true,
    });
    
    console.log('Notification banner displayed');
  } catch (error) {
    console.error('Failed to display notification banner:', error);
  }
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

function generateNotificationId(): string {
  return `notification_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

function isAppLocked(): boolean {
  // V6 Business Logic: Check if app is in DND mode
  try {
    // This would integrate with the DND system
    return false; // Simplified for now
  } catch (error) {
    return false;
  }
}

export function isAppInForeground(): boolean {
  return AppState.currentState === 'active';
}

export function shouldShowBanner(notification: NotificationPayload, inApp: boolean, tapped: boolean): boolean {
  // V6 Business Logic: Banner display logic
  const { options = {} } = notification.data;
  
  return !!(
    options.banner !== false && // Banner not explicitly disabled
    inApp && // App is in foreground
    !tapped && // User didn't tap the notification
    (notification.priority === 'high' || 
     notification.priority === 'critical' ||
     options.banner === true)
  );
}

// =============================================================================
// NOTIFICATION EVENT HANDLING
// =============================================================================

export function setupNotificationEventHandlers() {
  // V6 Business Logic: Setup event handlers for different notification sources
  
  return {
    // Handle foreground notifications
    onForegroundNotification: async (notification: any) => {
      const payload = parseNotificationPayload(notification);
      return processNotificationActions(payload, false, true);
    },
    
    // Handle background notifications
    onBackgroundNotification: async (notification: any) => {
      const payload = parseNotificationPayload(notification);
      return processNotificationActions(payload, false, false);
    },
    
    // Handle notification taps
    onNotificationTap: async (notification: any) => {
      const payload = parseNotificationPayload(notification);
      return processNotificationActions(payload, true, isAppInForeground());
    },
    
    // Handle initial notification (app opened from notification)
    onInitialNotification: async (notification: any) => {
      if (!notification) return null;
      
      const payload = parseNotificationPayload(notification);
      return processNotificationActions(payload, true, true);
    },
  };
}
