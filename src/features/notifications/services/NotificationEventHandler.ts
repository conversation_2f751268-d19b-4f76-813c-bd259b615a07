// V7 Notification Event Handler
// Integrates provider architecture with Redux store and event handling
// Enhanced with provider-based notification handling

import { Platform } from 'react-native';
import messaging from '@react-native-firebase/messaging';
import notifee, { EventType } from '@notifee/react-native';
import { store } from '../../../core/store'; // Redux store from core
import { processNotification } from '../store/notificationsSlice';
import { parseNotificationPayload, isAppInForeground } from './NotificationActionService';
import {
  initializeProvider,
  getCurrentProvider,
  selectNotificationProvider,
  cleanupCurrentProvider
} from './NotificationService';

// =============================================================================
// EVENT HANDLER SETUP
// =============================================================================

export async function initializeNotificationEventHandlers(): Promise<{
  cleanup: () => void;
  isInitialized: boolean;
}> {
  const unsubscribers: Array<() => void> = [];

  try {
    console.log('🚀 Initializing enhanced notification event handlers...');

    // V7 Enhancement: Initialize provider first
    let provider;
    try {
      provider = await initializeProvider();
      console.log(`✅ Provider ${provider.provider} initialized`);
    } catch (error) {
      console.warn('⚠️ Provider initialization failed, falling back to legacy handlers:', error);
    }

    // V7 Enhancement: Setup provider event listeners if available
    if (provider) {
      setupProviderEventListeners(provider, unsubscribers);
    }

    // V6 Business Logic: Setup Firebase messaging handlers (legacy compatibility)
    if (Platform.OS === 'android' || Platform.OS === 'ios') {
      setupFirebaseHandlers(unsubscribers);
    }

    // V6 Business Logic: Setup notifee handlers for local notifications
    setupNotifeeHandlers(unsubscribers);

    // Handle initial notification if app was opened from notification
    await handleInitialNotification();

    console.log('✅ Enhanced notification event handlers initialized');

    return {
      cleanup: () => {
        unsubscribers.forEach(unsubscribe => {
          try {
            unsubscribe();
          } catch (error) {
            console.warn('Error during notification handler cleanup:', error);
          }
        });

        // Cleanup provider
        cleanupCurrentProvider();

        console.log('✅ Notification event handlers cleaned up');
      },
      isInitialized: true,
    };
  } catch (error) {
    console.error('❌ Failed to initialize notification event handlers:', error);
    return {
      cleanup: () => {},
      isInitialized: false,
    };
  }
}

/**
 * Setup provider-based event listeners
 */
function setupProviderEventListeners(provider: any, unsubscribers: Array<() => void>): void {
  console.log('🔗 Setting up provider event listeners...');

  try {
    // Token refresh listener
    const tokenUnsubscribe = provider.onTokenRefresh((token: string) => {
      console.log('🔄 Provider token refreshed');
      store.dispatch({
        type: 'notifications/setNotificationToken',
        payload: { provider: provider.provider, token }
      });
    });

    // Notification received listener
    const receivedUnsubscribe = provider.onNotificationReceived((notification: any) => {
      console.log('📨 Provider notification received');
      store.dispatch(processNotification({
        notification,
        tapped: false,
        inApp: isAppInForeground(),
      }));
    });

    // Notification opened listener
    const openedUnsubscribe = provider.onNotificationOpened((notification: any) => {
      console.log('👆 Provider notification opened');
      store.dispatch(processNotification({
        notification,
        tapped: true,
        inApp: isAppInForeground(),
      }));
    });

    unsubscribers.push(tokenUnsubscribe, receivedUnsubscribe, openedUnsubscribe);
    console.log('✅ Provider event listeners setup complete');
  } catch (error) {
    console.warn('⚠️ Failed to setup provider event listeners:', error);
  }
}

// =============================================================================
// FIREBASE MESSAGING HANDLERS
// =============================================================================

function setupFirebaseHandlers(unsubscribers: Array<() => void>): void {
  try {
  // V6 Business Logic: Handle foreground messages
  const foregroundUnsubscribe = messaging().onMessage(async (remoteMessage) => {
    console.log('Received foreground notification:', remoteMessage);

    try {
      const notification = parseNotificationPayload(remoteMessage);

      // Dispatch to Redux store for processing
      await store.dispatch(processNotification({
        notification: {
          id: notification.id,
          title: notification.title,
          subtitle: notification.subtitle,
          body: notification.body,
          data: notification.data,
          options: notification.data.options || {},
        },
        tapped: false,
        inApp: true,
      }));
    } catch (error) {
      console.warn('Failed to process foreground notification:', error);
    }
  });
  
  // V6 Business Logic: Handle notification opened app (background to foreground)
  const notificationOpenedUnsubscribe = messaging().onNotificationOpenedApp(async (remoteMessage) => {
    console.log('Notification opened app:', remoteMessage);
    
    try {
      const notification = parseNotificationPayload(remoteMessage);
      
      // Dispatch to Redux store for processing
      await store.dispatch(processNotification({
        notification: {
          id: notification.id,
          title: notification.title,
          subtitle: notification.subtitle,
          body: notification.body,
          data: notification.data,
          options: notification.data.options || {},
        },
        tapped: true,
        inApp: isAppInForeground(),
      }));
    } catch (error) {
      console.error('Error processing notification that opened app:', error);
    }
  });
  
  // V6 Business Logic: Handle background messages
  messaging().setBackgroundMessageHandler(async (remoteMessage) => {
    console.log('Received background notification:', remoteMessage);
    
    try {
      const notification = parseNotificationPayload(remoteMessage);
      
      // For background messages, we typically just log or perform silent operations
      // The actual processing will happen when the notification is tapped
      console.log('Background notification processed silently');
    } catch (error) {
      console.error('Error processing background notification:', error);
    }
  });
  
  unsubscribers.push(foregroundUnsubscribe);
  unsubscribers.push(notificationOpenedUnsubscribe);
  } catch (error) {
    console.warn('Failed to setup Firebase handlers:', error);
  }
}

// =============================================================================
// NOTIFEE HANDLERS
// =============================================================================

function setupNotifeeHandlers(unsubscribers: Array<() => void>): void {
  try {
  // V6 Business Logic: Handle foreground events (local notifications)
  const foregroundUnsubscribe = notifee.onForegroundEvent(async ({ type, detail }) => {
    console.log('Notifee foreground event:', type, detail);
    
    if (type === EventType.PRESS && detail.notification) {
      try {
        const notification = parseNotifeeNotification(detail.notification);
        
        // Dispatch to Redux store for processing
        await store.dispatch(processNotification({
          notification: {
            id: notification.id,
            title: notification.title,
            subtitle: notification.subtitle,
            body: notification.body,
            data: notification.data,
            options: notification.data.options || {},
          },
          tapped: true,
          inApp: true,
        }));
      } catch (error) {
        console.error('Error processing notifee foreground event:', error);
      }
    }
  });
  
  // V6 Business Logic: Handle background events (local notifications)
  notifee.onBackgroundEvent(async ({ type, detail }) => {
    console.log('Notifee background event:', type, detail);
    
    if (type === EventType.PRESS && detail.notification) {
      try {
        const notification = parseNotifeeNotification(detail.notification);
        
        // For background events, we'll process when app comes to foreground
        console.log('Background notifee event logged for later processing');
      } catch (error) {
        console.error('Error processing notifee background event:', error);
      }
    }
  });
  
  unsubscribers.push(foregroundUnsubscribe);
  // Note: Background handler doesn't return unsubscribe function
  } catch (error) {
    console.warn('Failed to setup Notifee handlers:', error);
  }
}

// =============================================================================
// INITIAL NOTIFICATION HANDLING
// =============================================================================

export async function handleInitialNotification(): Promise<void> {
  try {
    // V6 Business Logic: Check for initial notification from Firebase
    const initialNotification = await messaging().getInitialNotification();

    if (initialNotification) {
      console.log('App opened from notification:', initialNotification);

      try {
        const notification = parseNotificationPayload(initialNotification);

        // Dispatch to Redux store for processing
        await store.dispatch(processNotification({
          notification: {
            id: notification.id,
            title: notification.title,
            subtitle: notification.subtitle,
            body: notification.body,
            data: notification.data,
            options: notification.data.options || {},
          },
          tapped: true,
          inApp: true,
        }));
      } catch (processError) {
        console.warn('Failed to process initial Firebase notification:', processError);
      }
    }

    // V6 Business Logic: Check for initial notification from notifee
    const initialNotifeeNotification = await notifee.getInitialNotification();

    if (initialNotifeeNotification && initialNotifeeNotification.notification) {
      console.log('App opened from local notification:', initialNotifeeNotification);

      try {
        const notification = parseNotifeeNotification(initialNotifeeNotification.notification);

        // Dispatch to Redux store for processing
        await store.dispatch(processNotification({
          notification: {
            id: notification.id,
            title: notification.title,
            subtitle: notification.subtitle,
            body: notification.body,
            data: notification.data,
            options: notification.data.options || {},
          },
          tapped: true,
          inApp: true,
        }));
      } catch (processError) {
        console.warn('Failed to process initial notifee notification:', processError);
      }
    }
  } catch (error) {
    console.error('Error handling initial notification:', error);
  }
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

function parseNotifeeNotification(notification: any): any {
  try {
    const data = notification.data?.data ? JSON.parse(notification.data.data) : {};
    
    return {
      id: notification.id || `local_${Date.now()}`,
      title: notification.title || '',
      subtitle: notification.subtitle,
      body: notification.body || '',
      data: data,
    };
  } catch (error) {
    console.error('Error parsing notifee notification:', error);
    return {
      id: `local_${Date.now()}`,
      title: notification.title || 'Local Notification',
      body: notification.body || '',
      data: {},
    };
  }
}

// =============================================================================
// LIFECYCLE MANAGEMENT
// =============================================================================

let eventHandlers: { cleanup: () => void; isInitialized: boolean } | null = null;

export function startNotificationEventHandling(): boolean {
  try {
    if (eventHandlers?.isInitialized) {
      console.log('Notification event handlers already initialized');
      return true;
    }

    eventHandlers = initializeNotificationEventHandlers();

    // Handle initial notification (async but don't block initialization)
    handleInitialNotification().catch(error => {
      console.warn('Failed to handle initial notification:', error);
    });

    return eventHandlers.isInitialized;
  } catch (error) {
    console.error('Failed to start notification event handling:', error);
    return false;
  }
}

export function stopNotificationEventHandling(): void {
  if (eventHandlers) {
    eventHandlers.cleanup();
    eventHandlers = null;
    console.log('Notification event handling stopped');
  }
}

export function isNotificationEventHandlingActive(): boolean {
  return eventHandlers?.isInitialized || false;
}
