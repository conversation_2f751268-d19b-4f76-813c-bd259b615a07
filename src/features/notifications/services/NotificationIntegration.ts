// V7 Notification Integration Service
// Connects notification system with installation and permissions systems

import { Platform } from 'react-native';
import * as NotificationService from './NotificationService';
import { NotificationProvider, NotificationToken } from '../types';

// =============================================================================
// INSTALLATION INTEGRATION
// =============================================================================

export async function initializeNotificationForInstallation(): Promise<{
  provider: NotificationProvider;
  token: NotificationToken | null;
  refreshListener: (() => void) | null;
}> {
  try {
    // V6 Business Logic: Initialize notifications during installation
    const provider = NotificationService.selectNotificationProvider();
    
    // Create Android channels first if needed
    if (Platform.OS === 'android') {
      await NotificationService.createNotificationChannels([
        {
          id: 'Perkd',
          name: 'Perkd',
          description: 'Perkd notifications',
          importance: 4, // HIGH
          sound: 'chord',
        },
        {
          id: 'Alerts',
          name: 'Alerts', 
          description: 'Alert notifications',
          importance: 4, // HIGH
        },
      ]);
    }
    
    // Initialize token management
    const token = await NotificationService.initializeTokenManagement(provider);
    
    // Setup token refresh listener for installation updates
    const refreshListener = NotificationService.setupTokenRefreshListener(
      provider,
      (newToken) => {
        // This callback will be called when token refreshes
        // The installation system can listen to this for updates
        console.log('Notification token refreshed, installation system should be notified');
      }
    );
    
    return {
      provider,
      token,
      refreshListener,
    };
  } catch (error) {
    console.error('Failed to initialize notifications for installation:', error);
    return {
      provider: NotificationService.selectNotificationProvider(),
      token: null,
      refreshListener: null,
    };
  }
}

// =============================================================================
// PERMISSION INTEGRATION
// =============================================================================

export async function checkNotificationPermissionStatus(): Promise<{
  granted: boolean;
  status: string;
  canRequest: boolean;
}> {
  try {
    const settings = await NotificationService.getNotificationSettings();
    
    const granted = settings.authorizationStatus === 'authorized' || 
                   settings.authorizationStatus === 'provisional';
    
    const canRequest = settings.authorizationStatus === 'notDetermined';
    
    return {
      granted,
      status: settings.authorizationStatus,
      canRequest,
    };
  } catch (error) {
    console.error('Failed to check notification permission status:', error);
    return {
      granted: false,
      status: 'denied',
      canRequest: false,
    };
  }
}

export async function requestNotificationPermissionWithFallback(): Promise<boolean> {
  try {
    // V6 Business Logic: Request permission with graceful fallback
    const provider = NotificationService.selectNotificationProvider();
    const token = await NotificationService.getNotificationToken(provider);
    
    return !!token; // If we got a token, permission was granted
  } catch (error) {
    console.warn('Failed to request notification permission:', error);
    return false;
  }
}

// =============================================================================
// LIFECYCLE INTEGRATION
// =============================================================================

export function createNotificationLifecycleManager() {
  let tokenRefreshUnsubscribe: (() => void) | null = null;
  
  return {
    start: async (onTokenChange?: (token: string) => void) => {
      try {
        const provider = NotificationService.selectNotificationProvider();
        
        // Setup token refresh listener
        tokenRefreshUnsubscribe = NotificationService.setupTokenRefreshListener(
          provider,
          onTokenChange
        );
        
        console.log('Notification lifecycle manager started');
        return true;
      } catch (error) {
        console.error('Failed to start notification lifecycle manager:', error);
        return false;
      }
    },
    
    stop: () => {
      if (tokenRefreshUnsubscribe) {
        tokenRefreshUnsubscribe();
        tokenRefreshUnsubscribe = null;
        console.log('Notification lifecycle manager stopped');
      }
    },
    
    refresh: async () => {
      try {
        const provider = NotificationService.selectNotificationProvider();
        const token = await NotificationService.getNotificationToken(provider);
        
        if (token && NotificationService.hasTokenChanged(provider, token)) {
          const notificationToken = {
            provider,
            token,
            modifiedAt: new Date().toISOString(),
            isActive: true,
          };
          
          NotificationService.persistNotificationToken(provider, notificationToken);
          return notificationToken;
        }
        
        return NotificationService.getPersistedToken(provider);
      } catch (error) {
        console.error('Failed to refresh notification token:', error);
        return null;
      }
    },
  };
}

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

export function getNotificationProviderForPlatform(): NotificationProvider {
  return NotificationService.selectNotificationProvider();
}

export async function validateNotificationSetup(): Promise<{
  isValid: boolean;
  issues: string[];
  recommendations: string[];
}> {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  try {
    // Check provider selection
    const provider = NotificationService.selectNotificationProvider();
    
    // Check token availability
    const token = await NotificationService.getNotificationToken(provider);
    if (!token) {
      issues.push('No notification token available');
      recommendations.push('Request notification permissions');
    }
    
    // Check permission status
    const permissionStatus = await checkNotificationPermissionStatus();
    if (!permissionStatus.granted) {
      issues.push('Notification permissions not granted');
      if (permissionStatus.canRequest) {
        recommendations.push('Request notification permissions from user');
      } else {
        recommendations.push('Guide user to app settings to enable notifications');
      }
    }
    
    // Check Android channels (if applicable)
    if (Platform.OS === 'android') {
      // Note: We could add channel validation here if needed
      recommendations.push('Ensure notification channels are properly configured');
    }
    
    return {
      isValid: issues.length === 0,
      issues,
      recommendations,
    };
  } catch (error) {
    return {
      isValid: false,
      issues: ['Failed to validate notification setup'],
      recommendations: ['Check notification service configuration'],
    };
  }
}
