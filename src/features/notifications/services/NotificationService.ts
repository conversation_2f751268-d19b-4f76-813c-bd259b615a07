// V7 Notification Service - Platform Integration Layer
// Enhanced implementation with provider architecture and improved token management

import { Platform } from 'react-native';
import notifee, { AndroidImportance, AndroidVisibility, TriggerType, RepeatFrequency, AndroidStyle } from '@notifee/react-native';
import { MMKV } from 'react-native-mmkv';
import {
  NotificationProvider,
  NotificationToken,
  NotificationPayload,
  NotificationBadge,
  NotificationSettings,
  NotificationChannel,
  NotificationError,
  NotificationErrorCodes,
  NotificationData,
} from '../types';
import {
  createNotificationProvider,
  selectBestProvider,
  getAvailableProviders,
  getProvider,
  cleanupProviders,
  INotificationProvider,
} from '../providers';

// Simple storage instance
const storage = new MMKV({ id: 'notifications' });

// =============================================================================
// PROVIDER MANAGEMENT
// =============================================================================

// Global provider instance
let currentProvider: INotificationProvider | null = null;

export function selectNotificationProvider(): NotificationProvider {
  // V7 Enhanced: Use provider factory for selection
  return selectBestProvider();
}

export async function initializeProvider(provider?: NotificationProvider): Promise<INotificationProvider> {
  try {
    const selectedProvider = provider || selectNotificationProvider();

    // Create provider instance
    currentProvider = createNotificationProvider(selectedProvider);

    // Initialize the provider
    await currentProvider.initialize();

    console.log(`✅ Notification provider ${selectedProvider} initialized successfully`);
    return currentProvider;
  } catch (error) {
    console.error('❌ Failed to initialize notification provider:', error);
    throw error;
  }
}

export function getCurrentProvider(): INotificationProvider | null {
  return currentProvider;
}

export function cleanupCurrentProvider(): void {
  if (currentProvider) {
    currentProvider.cleanup();
    currentProvider = null;
  }
}

// =============================================================================
// TOKEN MANAGEMENT
// =============================================================================

export async function getNotificationToken(provider?: NotificationProvider): Promise<string | null> {
  try {
    // Use current provider if none specified
    const targetProvider = provider || selectNotificationProvider();

    // Get or create provider instance
    let providerInstance = getProvider(targetProvider);
    if (!providerInstance) {
      providerInstance = await initializeProvider(targetProvider);
    }

    // Get token from provider
    const token = await providerInstance.getToken();
    console.log(`Successfully retrieved ${targetProvider} token:`, token ? 'present' : 'null');
    return token;
  } catch (error) {
    console.error('Failed to get notification token:', error);
    return null;
  }
}

export async function getNotificationPermissions(provider?: NotificationProvider): Promise<NotificationSettings | null> {
  try {
    // Use current provider if none specified
    const targetProvider = provider || selectNotificationProvider();

    // Get or create provider instance
    let providerInstance = getProvider(targetProvider);
    if (!providerInstance) {
      providerInstance = await initializeProvider(targetProvider);
    }

    // Get permissions from provider
    const settings = await providerInstance.getPermissionStatus();
    console.log(`Retrieved ${targetProvider} permissions:`, settings);
    return settings;
  } catch (error) {
    console.error('Failed to get notification permissions:', error);
    return null;
  }
}

export async function requestNotificationPermissions(provider?: NotificationProvider): Promise<NotificationSettings | null> {
  try {
    // Use current provider if none specified
    const targetProvider = provider || selectNotificationProvider();

    // Get or create provider instance
    let providerInstance = getProvider(targetProvider);
    if (!providerInstance) {
      providerInstance = await initializeProvider(targetProvider);
    }

    // Request permissions from provider
    const settings = await providerInstance.requestPermissions();
    console.log(`Requested ${targetProvider} permissions:`, settings);
    return settings;
  } catch (error) {
    console.error('Failed to request notification permissions:', error);
    return null;
  }
}

export function persistNotificationToken(provider: NotificationProvider, token: NotificationToken): void {
  try {
    storage.set(`token_${provider}`, JSON.stringify(token));
  } catch (error) {
    console.error('Failed to persist notification token:', error);
  }
}

export function getPersistedToken(provider: NotificationProvider): NotificationToken | null {
  try {
    const tokenData = storage.getString(`token_${provider}`);
    return tokenData ? JSON.parse(tokenData) : null;
  } catch (error) {
    console.error('Failed to get persisted token:', error);
    return null;
  }
}

export function hasTokenChanged(provider: NotificationProvider, newToken: string): boolean {
  const existing = getPersistedToken(provider);
  return !existing || existing.token !== newToken;
}

export async function initializeTokenManagement(provider: NotificationProvider): Promise<NotificationToken | null> {
  try {
    // V6 Business Logic: Initialize token management with refresh handling
    const token = await getNotificationToken(provider);
    if (!token) {
      console.warn('Failed to get initial notification token');
      return null;
    }

    // Check if token has changed
    if (hasTokenChanged(provider, token)) {
      const notificationToken: NotificationToken = {
        provider,
        token,
        modifiedAt: new Date().toISOString(),
        isActive: true,
      };

      // Persist the new token
      persistNotificationToken(provider, notificationToken);
      console.log('Notification token updated and persisted');
      return notificationToken;
    }

    // Return existing token
    const existingToken = getPersistedToken(provider);
    console.log('Using existing notification token');
    return existingToken;
  } catch (error) {
    console.error('Failed to initialize token management:', error);
    return null;
  }
}

export function setupTokenRefreshListener(provider: NotificationProvider, onTokenRefresh?: (token: string) => void): () => void {
  // V6 Business Logic: Listen for token refresh events
  const unsubscribe = messaging().onTokenRefresh((token) => {
    console.log('Notification token refreshed:', token ? 'present' : 'null');

    if (token && hasTokenChanged(provider, token)) {
      const notificationToken: NotificationToken = {
        provider,
        token,
        modifiedAt: new Date().toISOString(),
        isActive: true,
      };

      persistNotificationToken(provider, notificationToken);

      // Notify callback if provided (for installation system integration)
      if (onTokenRefresh) {
        onTokenRefresh(token);
      }
    }
  });

  return unsubscribe;
}

// =============================================================================
// CHANNEL MANAGEMENT
// =============================================================================

export async function createNotificationChannels(channels: NotificationChannel[]): Promise<void> {
  try {
    // V6 Business Logic: Create Android notification channels
    if (Platform.OS === 'android') {
      for (const channel of channels) {
        await notifee.createChannel({
          id: channel.id,
          name: channel.name,
          description: channel.description,
          importance: channel.importance || AndroidImportance.DEFAULT,
          visibility: channel.visibility || AndroidVisibility.PUBLIC,
          sound: channel.sound,
          vibration: channel.vibration !== false,
          lights: channel.lights !== false,
          badge: channel.badge !== false,
        });
      }
      console.log(`Created ${channels.length} notification channels`);
    }
  } catch (error) {
    console.error('Failed to create notification channels:', error);
    throw error;
  }
}

export async function deleteNotificationChannel(channelId: string): Promise<void> {
  try {
    if (Platform.OS === 'android') {
      await notifee.deleteChannel(channelId);
      console.log(`Deleted notification channel: ${channelId}`);
    }
  } catch (error) {
    console.warn(`Failed to delete notification channel ${channelId}:`, error);
  }
}

// =============================================================================
// NOTIFICATION SETTINGS
// =============================================================================

export async function getNotificationSettings(): Promise<NotificationSettings> {
  try {
    const settings = await notifee.getNotificationSettings();

    return {
      authorizationStatus: mapAuthorizationStatus(settings.authorizationStatus),
      sound: settings.ios?.sound || 0,
      badge: settings.ios?.badge || 0,
      alert: settings.ios?.alert || 0,
      criticalAlert: settings.ios?.criticalAlert || 0,
      provisional: (settings.ios as any)?.provisional || 0,
      lockScreen: settings.ios?.lockScreen || 0,
      notificationCenter: settings.ios?.notificationCenter || 0,
    };
  } catch (error) {
    console.warn('Failed to get notification settings:', error);
    return {
      authorizationStatus: 'denied',
      sound: 0,
      badge: 0,
      alert: 0,
      criticalAlert: 0,
      provisional: 0,
      lockScreen: 0,
      notificationCenter: 0,
    };
  }
}

function mapAuthorizationStatus(status: number): 'authorized' | 'denied' | 'notDetermined' | 'provisional' {
  switch (status) {
    case 2: return 'authorized';
    case 1: return 'denied';
    case 3: return 'provisional';
    default: return 'notDetermined';
  }
}

// =============================================================================
// BADGE MANAGEMENT
// =============================================================================

export async function setBadgeCount(count: number): Promise<void> {
  try {
    // Use provider if available for platform-specific handling
    const provider = getCurrentProvider();
    if (provider) {
      await provider.setBadgeCount(count);
      console.log(`✅ Badge count set to ${count} via provider`);
      return;
    }

    // Fallback to notifee
    await notifee.setBadgeCount(count);
    console.log(`✅ Badge count set to ${count} via notifee`);
  } catch (error) {
    console.warn('Failed to set badge count:', error);
  }
}

export async function getBadgeCount(): Promise<number> {
  try {
    // Use provider if available for platform-specific handling
    const provider = getCurrentProvider();
    if (provider) {
      const count = await provider.getBadgeCount();
      console.log(`📱 Badge count retrieved: ${count} via provider`);
      return count;
    }

    // Fallback to notifee
    const count = await notifee.getBadgeCount();
    console.log(`📱 Badge count retrieved: ${count} via notifee`);
    return count;
  } catch (error) {
    console.warn('Failed to get badge count:', error);
    return 0;
  }
}

export function calculateBadgeCount(notifications: NotificationPayload[]): number {
  // V6 Business Logic: Sophisticated badge calculation
  const unreadCount = notifications.filter(n => !n.read).length;
  const criticalCount = notifications.filter(n => n.priority === 'high' && !n.read).length;
  
  // Prioritize critical notifications in badge count
  return Math.max(unreadCount, criticalCount);
}

// =============================================================================
// NOTIFICATION CHANNELS (Android)
// =============================================================================

export async function createNotificationChannel(channel: NotificationChannel): Promise<void> {
  if (Platform.OS !== 'android') return;
  
  try {
    await notifee.createChannel({
      id: channel.id,
      name: channel.name,
      description: channel.description,
      importance: mapImportance(channel.importance),
      visibility: mapVisibility(channel.visibility),
      sound: channel.sound,
      vibration: channel.vibration,
      lights: channel.lights,
      badge: channel.badge,
    });
  } catch (error) {
    console.warn(`Failed to create notification channel ${channel.id}:`, error);
  }
}

function mapImportance(importance: string | number): AndroidImportance {
  if (typeof importance === 'number') {
    return importance as AndroidImportance;
  }
  switch (importance) {
    case 'high': return AndroidImportance.HIGH;
    case 'default': return AndroidImportance.DEFAULT;
    case 'low': return AndroidImportance.LOW;
    case 'min': return AndroidImportance.MIN;
    default: return AndroidImportance.DEFAULT;
  }
}

function mapVisibility(visibility: string | number | undefined): AndroidVisibility {
  if (typeof visibility === 'number') {
    return visibility as AndroidVisibility;
  }
  if (!visibility) return AndroidVisibility.PRIVATE;
  switch (visibility) {
    case 'public': return AndroidVisibility.PUBLIC;
    case 'private': return AndroidVisibility.PRIVATE;
    case 'secret': return AndroidVisibility.SECRET;
    default: return AndroidVisibility.PRIVATE;
  }
}

// =============================================================================
// NOTIFICATION DISPLAY
// =============================================================================

export async function displayNotification(notification: NotificationPayload): Promise<void> {
  try {
    // Use provider if available for platform-specific handling
    const provider = getCurrentProvider();
    if (provider) {
      const notificationData: NotificationData = {
        id: notification.id || `notification-${Date.now()}`,
        title: notification.title,
        subtitle: notification.subtitle,
        body: notification.body,
        image: notification.data?.image,
        sound: notification.sound,
        data: notification.data || {},
        options: {
          sync: notification.data?.sync,
          banner: notification.data?.banner,
          nav: notification.data?.nav,
        },
      };

      await provider.displayNotification(notificationData);
      return;
    }

    // Fallback to notifee for direct display
    await notifee.displayNotification({
      id: notification.id || `notification-${Date.now()}`,
      title: notification.title,
      subtitle: notification.subtitle,
      body: notification.body,
      data: notification.data,
      android: {
        channelId: notification.channelId || 'default',
        importance: mapImportance(notification.priority || 'default'),
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
      },
      ios: {
        sound: notification.sound,
        critical: notification.priority === 'critical',
      },
    });
  } catch (error) {
    console.warn('Failed to display notification:', error);
    throw error;
  }
}

// =============================================================================
// NOTIFICATION DEDUPLICATION
// =============================================================================

export function deduplicateNotifications(notifications: NotificationPayload[]): NotificationPayload[] {
  // V6 Business Logic: Sophisticated deduplication
  const seen = new Set<string>();
  const deduplicated: NotificationPayload[] = [];
  
  for (const notification of notifications) {
    const key = generateDeduplicationKey(notification);
    if (!seen.has(key)) {
      seen.add(key);
      deduplicated.push(notification);
    }
  }
  
  return deduplicated;
}

function generateDeduplicationKey(notification: NotificationPayload): string {
  // Create unique key based on content and timing
  const contentHash = `${notification.title}-${notification.body}`;
  const timeWindow = Math.floor((notification.timestamp || Date.now()) / (5 * 60 * 1000)); // 5-minute windows
  return `${contentHash}-${timeWindow}`;
}

// =============================================================================
// CLEANUP UTILITIES
// =============================================================================

export async function cleanupOldNotifications(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<void> {
  try {
    const notifications = await notifee.getDisplayedNotifications();
    const cutoff = Date.now() - maxAge;
    
    for (const notification of notifications) {
      const timestamp = notification.notification.data?.timestamp as number;
      if (timestamp && timestamp < cutoff && notification.notification.id) {
        await notifee.cancelNotification(notification.notification.id);
      }
    }
  } catch (error) {
    console.warn('Failed to cleanup old notifications:', error);
  }
}

// =============================================================================
// LOCAL NOTIFICATION SUPPORT
// =============================================================================

export interface LocalNotificationOptions {
  id?: string;
  title: string;
  body: string;
  subtitle?: string;
  data?: any;
  trigger?: {
    type: 'timestamp' | 'interval';
    timestamp?: number;
    timeInterval?: number;
    repeats?: boolean;
  };
  sound?: string;
  badge?: number;
  image?: string;
  channelId?: string;
}

export async function scheduleLocalNotification(options: LocalNotificationOptions): Promise<string> {
  try {
    const notificationId = options.id || `local-${Date.now()}`;

    const notification = {
      id: notificationId,
      title: options.title,
      subtitle: options.subtitle,
      body: options.body,
      data: {
        ...options.data,
        timestamp: Date.now(),
        isLocal: true,
      },
      android: {
        channelId: options.channelId || 'default',
        sound: options.sound,
        pressAction: {
          id: 'default',
          launchActivity: 'default',
        },
      },
      ios: {
        sound: options.sound,
        badge: options.badge,
        attachments: options.image ? [{ url: options.image }] : undefined,
      },
    };

    if (options.trigger) {
      let trigger;

      if (options.trigger.type === 'timestamp' && options.trigger.timestamp) {
        trigger = {
          type: TriggerType.TIMESTAMP,
          timestamp: options.trigger.timestamp,
          alarmManager: {
            allowWhileIdle: true,
          },
        };
      } else if (options.trigger.type === 'interval' && options.trigger.timeInterval) {
        trigger = {
          type: TriggerType.TIMESTAMP,
          timestamp: Date.now() + options.trigger.timeInterval,
          repeatFrequency: options.trigger.repeats ? RepeatFrequency.DAILY : undefined,
          alarmManager: {
            allowWhileIdle: true,
          },
        };
      }

      if (trigger) {
        await notifee.createTriggerNotification(notification, trigger);
        console.log(`✅ Scheduled local notification: ${notificationId}`);
      } else {
        await notifee.displayNotification(notification);
        console.log(`✅ Displayed immediate local notification: ${notificationId}`);
      }
    } else {
      await notifee.displayNotification(notification);
      console.log(`✅ Displayed immediate local notification: ${notificationId}`);
    }

    return notificationId;
  } catch (error) {
    console.error('❌ Failed to schedule local notification:', error);
    throw error;
  }
}

export async function cancelLocalNotification(id: string): Promise<void> {
  try {
    await notifee.cancelNotification(id);
    console.log(`✅ Cancelled local notification: ${id}`);
  } catch (error) {
    console.warn('Failed to cancel local notification:', error);
  }
}

export async function cancelAllLocalNotifications(): Promise<void> {
  try {
    await notifee.cancelAllNotifications();
    console.log('✅ Cancelled all local notifications');
  } catch (error) {
    console.warn('Failed to cancel all local notifications:', error);
  }
}

export async function getScheduledNotifications(): Promise<any[]> {
  try {
    const scheduled = await notifee.getTriggerNotifications();
    console.log(`📋 Found ${scheduled.length} scheduled notifications`);
    return scheduled;
  } catch (error) {
    console.warn('Failed to get scheduled notifications:', error);
    return [];
  }
}

export async function getDisplayedNotifications(): Promise<any[]> {
  try {
    const displayed = await notifee.getDisplayedNotifications();
    console.log(`📋 Found ${displayed.length} displayed notifications`);
    return displayed;
  } catch (error) {
    console.warn('Failed to get displayed notifications:', error);
    return [];
  }
}

// =============================================================================
// FOREGROUND DETECTION
// =============================================================================

export function isAppInForeground(): boolean {
  // This will be enhanced with AppState integration
  return true; // Simplified for now
}

export function shouldShowBanner(notification: NotificationPayload): boolean {
  // V6 Business Logic: Banner display logic
  if (!isAppInForeground()) return false;
  
  return notification.priority === 'high' || 
         notification.priority === 'critical' ||
         notification.showInForeground === true;
}

// =============================================================================
// ERROR HANDLING
// =============================================================================

export function isNotificationError(error: any): boolean {
  if (!error) return false;
  
  const notificationErrorCodes = ['PERMISSION_DENIED', 'TOKEN_ERROR', 'DISPLAY_ERROR'];
  const notificationErrorMessages = ['notification', 'permission', 'token'];
  
  const errorCode = error.code || '';
  const errorMessage = (error.message || '').toLowerCase();
  
  return notificationErrorCodes.includes(errorCode) || 
         notificationErrorMessages.some(msg => errorMessage.includes(msg));
}

// =============================================================================
// STATE PERSISTENCE
// =============================================================================

export function persistNotificationState(state: any): void {
  try {
    storage.set('notification_state', JSON.stringify(state));
  } catch (error) {
    console.error('Failed to persist notification state:', error);
  }
}

export function getPersistedNotificationState(): any {
  try {
    const stateData = storage.getString('notification_state');
    return stateData ? JSON.parse(stateData) : null;
  } catch (error) {
    console.error('Failed to get persisted notification state:', error);
    return null;
  }
}
