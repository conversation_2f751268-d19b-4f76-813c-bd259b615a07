// V7 Notifications Redux Slice - Pure Redux Toolkit Implementation
// Business Logic Extracted from V6 src/lib/common/notify.js

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Platform } from 'react-native';
import {
  NotificationsState,
  NotificationProvider,
  NotificationData,
  ProcessedNotification,
  NotificationToken,
  InitializeNotificationsPayload,
  ProcessNotificationPayload,
  UpdateBadgePayload,
  SetTokenPayload,
  HandleNotificationPayload,
  NotificationError,
  NotificationErrorCodes,
  NotificationChannels,
  NotificationImportance,
} from '../types';
import * as NotificationService from '../services/NotificationService';

// Initial State
const initialState: NotificationsState = {
  activeProvider: null,
  providers: {},
  tokens: [],
  currentToken: null,
  handledNotifications: [],
  receivedNotifications: [],
  processingQueue: [],
  badges: {
    cardBadges: {},
    appBadge: 0,
    lastUpdated: new Date().toISOString(),
  },
  history: [],
  channels: [
    {
      id: NotificationChannels.PERKD,
      name: NotificationChannels.PERKD,
      description: NotificationChannels.PERKD,
      importance: NotificationImportance.HIGH,
      sound: 'chord',
    },
    {
      id: NotificationChannels.ALERTS,
      name: NotificationChannels.ALERTS,
      description: NotificationChannels.ALERTS,
      importance: NotificationImportance.HIGH,
    },
  ],
  isActive: false,
  isInitialized: false,
  lastSync: null,
  error: null,
  isLoading: false,
  bannerEnabled: true,
  soundEnabled: true,
};

// Async Thunks for Complex Operations

export const initializeNotifications = createAsyncThunk(
  'notifications/initialize',
  async ({ platform, region }: InitializeNotificationsPayload, { rejectWithValue }) => {
    try {
      // V6 Business Logic: Dynamic provider selection
      const selectedProvider = NotificationService.selectNotificationProvider();

      // V6 Business Logic: Create Android channels first
      if (Platform.OS === 'android') {
        await NotificationService.createNotificationChannels([
          {
            id: NotificationChannels.PERKD,
            name: NotificationChannels.PERKD,
            description: NotificationChannels.PERKD,
            importance: NotificationImportance.HIGH,
            sound: 'chord',
          },
          {
            id: NotificationChannels.ALERTS,
            name: NotificationChannels.ALERTS,
            description: NotificationChannels.ALERTS,
            importance: NotificationImportance.HIGH,
          },
        ]);
      }

      // V6 Business Logic: Initialize token management
      const token = await NotificationService.initializeTokenManagement(selectedProvider);

      return {
        provider: selectedProvider,
        token,
        initialized: true,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return rejectWithValue({
        code: NotificationErrorCodes.PROVIDER_SELECTION_FAILED,
        message: error instanceof Error ? error.message : 'Provider initialization failed',
        context: { platform, region },
      });
    }
  }
);

export const refreshNotificationToken = createAsyncThunk(
  'notifications/refreshToken',
  async (provider: NotificationProvider, { rejectWithValue }) => {
    try {
      // V6 Business Logic: Token refresh with change detection
      const token = await NotificationService.getNotificationToken(provider);

      if (!token) {
        throw new Error('Failed to retrieve notification token');
      }

      // Check if token has changed
      if (NotificationService.hasTokenChanged(provider, token)) {
        const notificationToken = {
          provider,
          token,
          modifiedAt: new Date().toISOString(),
          isActive: true,
        };

        NotificationService.persistNotificationToken(provider, notificationToken);

        return {
          provider,
          token: notificationToken,
          changed: true,
          timestamp: new Date().toISOString(),
        };
      }

      return {
        provider,
        token: NotificationService.getPersistedToken(provider),
        changed: false,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return rejectWithValue({
        code: NotificationErrorCodes.TOKEN_SYNC_FAILED,
        message: error instanceof Error ? error.message : 'Token refresh failed',
        provider,
      });
    }
  }
);

export const startNotificationEventHandling = createAsyncThunk(
  'notifications/startEventHandling',
  async (_, { rejectWithValue }) => {
    try {
      // V6 Business Logic: Start comprehensive event handling
      const { startNotificationEventHandling } = await import('../services/NotificationEventHandler');

      const started = startNotificationEventHandling();

      if (!started) {
        throw new Error('Failed to start notification event handling');
      }

      return {
        eventHandlingActive: true,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return rejectWithValue({
        code: NotificationErrorCodes.EVENT_HANDLING_FAILED,
        message: error instanceof Error ? error.message : 'Failed to start event handling',
      });
    }
  }
);

export const processNotification = createAsyncThunk(
  'notifications/process',
  async ({ notification, tapped, inApp }: ProcessNotificationPayload, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { notifications: NotificationsState };

      // V6 Business Logic: Deduplication check
      if (state.notifications.handledNotifications.includes(notification.id)) {
        return null; // Already handled
      }

      // V6 Business Logic: Use comprehensive action service
      const { processNotificationActions } = await import('../services/NotificationActionService');

      const processedNotification = await processNotificationActions(
        {
          id: notification.id,
          title: notification.title,
          subtitle: notification.subtitle,
          body: notification.body,
          data: notification.data,
          timestamp: Date.now(),
        },
        tapped,
        inApp
      );

      return processedNotification;
    } catch (error) {
      return rejectWithValue({
        code: NotificationErrorCodes.NOTIFICATION_PROCESSING_FAILED,
        message: error instanceof Error ? error.message : 'Notification processing failed',
        context: { notificationId: notification.id },
      });
    }
  }
);

// Main Slice
const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    // V6 Business Logic: Provider management
    setActiveProvider: (state, action: PayloadAction<NotificationProvider>) => {
      state.activeProvider = action.payload;
    },

    updateProviderStatus: (state, action: PayloadAction<{ provider: NotificationProvider; registered: boolean; available: boolean; error?: string }>) => {
      const { provider, registered, available, error } = action.payload;
      state.providers[provider] = { registered, available, error };
    },

    // V6 Business Logic: Token management with deduplication
    setNotificationToken: (state, action: PayloadAction<SetTokenPayload>) => {
      const { provider, token } = action.payload;
      
      // Check for existing token
      const existingTokenIndex = state.tokens.findIndex(t => t.provider === provider);
      const tokenData: NotificationToken = {
        provider,
        token,
        modifiedAt: new Date().toISOString(),
        isActive: true,
      };
      
      if (existingTokenIndex !== -1) {
        // V6 Business Logic: Deduplication check
        if (state.tokens[existingTokenIndex].token !== token) {
          state.tokens[existingTokenIndex] = tokenData;
          state.currentToken = token;
        }
      } else {
        state.tokens.push(tokenData);
        state.currentToken = token;
      }
    },

    // V6 Business Logic: Notification state tracking
    markNotificationHandled: (state, action: PayloadAction<string>) => {
      const notificationId = action.payload;
      if (!state.handledNotifications.includes(notificationId)) {
        state.handledNotifications.push(notificationId);
        
        // V6 Business Logic: Cleanup old entries to prevent memory leaks
        if (state.handledNotifications.length > 1000) {
          state.handledNotifications = state.handledNotifications.slice(-500);
        }
      }
    },

    markNotificationReceived: (state, action: PayloadAction<string>) => {
      const notificationId = action.payload;
      if (!state.receivedNotifications.includes(notificationId)) {
        state.receivedNotifications.push(notificationId);
        
        // V6 Business Logic: Cleanup old entries
        if (state.receivedNotifications.length > 1000) {
          state.receivedNotifications = state.receivedNotifications.slice(-500);
        }
      }
    },

    // V6 Business Logic: Badge management with aggregation
    updateCardBadge: (state, action: PayloadAction<UpdateBadgePayload>) => {
      const { cardId, count } = action.payload;
      state.badges.cardBadges[cardId] = count;
      
      // V6 Business Logic: Calculate app badge as sum of all card badges
      state.badges.appBadge = Object.values(state.badges.cardBadges).reduce((total, cardCount) => total + cardCount, 0);
      state.badges.lastUpdated = new Date().toISOString();
    },

    refreshAllBadges: (state, action: PayloadAction<Record<string, number>>) => {
      state.badges.cardBadges = action.payload;
      state.badges.appBadge = Object.values(action.payload).reduce((total, count) => total + count, 0);
      state.badges.lastUpdated = new Date().toISOString();
    },

    // Notification history management
    addToHistory: (state, action: PayloadAction<ProcessedNotification>) => {
      const notification = action.payload;
      state.history.unshift({
        id: notification.id,
        notification,
        timestamp: new Date().toISOString(),
      });
      
      // Keep only recent 100 notifications
      if (state.history.length > 100) {
        state.history = state.history.slice(0, 100);
      }
    },

    removeFromHistory: (state, action: PayloadAction<string>) => {
      const notificationId = action.payload;
      state.history = state.history.filter(item => item.id !== notificationId);
    },

    clearHistory: (state) => {
      state.history = [];
    },

    // State management
    setActive: (state, action: PayloadAction<boolean>) => {
      state.isActive = action.payload;
    },

    setSettings: (state, action: PayloadAction<{ bannerEnabled?: boolean; soundEnabled?: boolean }>) => {
      const { bannerEnabled, soundEnabled } = action.payload;
      if (bannerEnabled !== undefined) state.bannerEnabled = bannerEnabled;
      if (soundEnabled !== undefined) state.soundEnabled = soundEnabled;
    },

    // Error handling
    setError: (state, action: PayloadAction<NotificationError>) => {
      state.error = action.payload.message;
      state.isLoading = false;
    },

    clearError: (state) => {
      state.error = null;
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Initialize Notifications
      .addCase(initializeNotifications.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(initializeNotifications.fulfilled, (state, action) => {
        const { provider, timestamp } = action.payload;
        state.activeProvider = provider;
        state.isInitialized = true;
        state.lastSync = timestamp;
        state.isLoading = false;
        state.error = null;
      })
      .addCase(initializeNotifications.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as any)?.message || action.error?.message || 'Initialization failed';
      })

      // Process Notification
      .addCase(processNotification.fulfilled, (state, action) => {
        if (action.payload) {
          const processedNotification = action.payload;

          // Mark as handled
          if (!state.handledNotifications.includes(processedNotification.id)) {
            state.handledNotifications.push(processedNotification.id);
          }

          // Add to history
          state.history.unshift({
            id: processedNotification.id,
            notification: processedNotification,
            timestamp: new Date().toISOString(),
          });

          // Add to processing queue if banner should be shown
          if (processedNotification.shouldShowBanner) {
            state.processingQueue.push(processedNotification);
          }
        }
      })

      // Refresh Token
      .addCase(refreshNotificationToken.fulfilled, (state, action) => {
        const { provider, token, changed } = action.payload;

        if (changed && token) {
          // Update token in state
          const existingIndex = state.tokens.findIndex(t => t.provider === provider);
          if (existingIndex !== -1) {
            state.tokens[existingIndex] = token;
          } else {
            state.tokens.push(token);
          }

          state.currentToken = token.token;
        }
      })

      // Start Event Handling
      .addCase(startNotificationEventHandling.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(startNotificationEventHandling.fulfilled, (state, action) => {
        state.isInitialized = true;
        state.isLoading = false;
        state.error = null;
      })
      .addCase(startNotificationEventHandling.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as any)?.message || action.error?.message || 'Failed to start event handling';
      });
  },
});

// V6 Business Logic Functions (Pure Functions)

// V6 Provider Selection Logic
function selectNotificationProvider(platform: string, region: string): NotificationProvider {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && global.selectNotificationProvider) {
    return global.selectNotificationProvider(platform, region);
  }

  if (platform === 'ios') return NotificationProvider.APNS;

  // V6 Business Logic: Market-based selection for Android
  return isChineseMarket(region) ? NotificationProvider.JPUSH : NotificationProvider.FCM;
}

// V6 Banner Display Logic
function shouldShowBanner(notification: NotificationData, inApp: boolean, tapped: boolean): boolean {
  return notification.options?.banner !== false && inApp && !tapped;
}

// Helper Functions (to be implemented)
async function loadNotificationProvider(provider: NotificationProvider): Promise<any> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && global.loadNotificationProvider) {
    return global.loadNotificationProvider(provider);
  }

  // Return service layer functions
  return {
    init: () => Promise.resolve(),
    register: () => NotificationService.getNotificationSettings(),
    getToken: () => NotificationService.getNotificationToken(provider),
    setToken: (token: any) => NotificationService.persistNotificationToken(provider, token),
  };
}

async function createNotificationChannels(): Promise<void> {
  // Create default channels for Android
  const defaultChannels = [
    {
      id: 'default',
      name: 'Default',
      description: 'Default notification channel',
      importance: 3, // AndroidImportance.DEFAULT
      visibility: 0, // AndroidVisibility.PRIVATE
      sound: 'default',
      vibration: true,
      lights: true,
      badge: true,
    },
    {
      id: 'high_priority',
      name: 'High Priority',
      description: 'High priority notifications',
      importance: 4, // AndroidImportance.HIGH
      visibility: 1, // AndroidVisibility.PUBLIC
      sound: 'default',
      vibration: true,
      lights: true,
      badge: true,
    },
  ];

  for (const channel of defaultChannels) {
    await NotificationService.createNotificationChannel(channel);
  }
}

async function handleNotificationSync(syncType: string): Promise<void> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && global.handleNotificationSync) {
    return global.handleNotificationSync(syncType);
  }

  try {
    // V7 Architecture: Sync system integration
    // TODO: Implement sync system integration with v7 architecture
    console.log('🔄 Notification sync requested:', syncType);

    // V7 Pattern: Use Redux actions instead of direct module imports
    // This should be handled by the sync feature slice when implemented
    if (syncType === 'cache') {
      console.log('📦 Cache sync requested - should dispatch to sync slice');
      // TODO: store.dispatch(syncSlice.actions.syncCache());
    } else if (syncType === 'all') {
      console.log('🔄 Full sync requested - should dispatch to sync slice');
      // TODO: store.dispatch(syncSlice.actions.syncAll());
    }

    console.log(`✅ Sync operation ${syncType} logged for v7 implementation`);
  } catch (error) {
    console.error(`❌ Failed to handle notification sync ${syncType}:`, error);
  }
}

async function handleNotificationEngagement(engageData: any): Promise<void> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && global.handleNotificationEngagement) {
    return global.handleNotificationEngagement(engageData);
  }

  try {
    // V7 Architecture: Engagement system integration
    // TODO: Implement engagement system integration with v7 architecture
    // For now, log the engagement data for debugging
    console.log('📊 Notification engagement data:', engageData);

    const { object, param } = engageData;

    if (object && param) {
      // V7 Pattern: Use Redux actions instead of direct model imports
      // This should be handled by the engagement feature slice when implemented
      console.log(`🎯 Engagement request: ${object} with param:`, param);

      // TODO: Dispatch engagement action to appropriate feature slice
      // Example: store.dispatch(engagementSlice.actions.handleEngagement({ object, param }));
    }
  } catch (error) {
    console.error('❌ Failed to handle notification engagement:', error);
  }
}

function isChineseMarket(region: string): boolean {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && global.isChineseMarket) {
    return global.isChineseMarket(region);
  }
  // V6 Business Logic: Support both 'CN' and 'China'
  return region === 'CN' || region === 'China';
}

export const {
  setActiveProvider,
  updateProviderStatus,
  setNotificationToken,
  markNotificationHandled,
  markNotificationReceived,
  updateCardBadge,
  refreshAllBadges,
  addToHistory,
  removeFromHistory,
  clearHistory,
  setActive,
  setSettings,
  setError,
  clearError,
  setLoading,
} = notificationsSlice.actions;

export default notificationsSlice.reducer;
