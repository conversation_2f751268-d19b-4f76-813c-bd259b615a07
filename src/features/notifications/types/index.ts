// V7 Notifications System Types - Extracted from V6 Business Rules

export enum NotificationProvider {
  APNS = 'apns',
  FCM = 'fcm',
  JPUSH = 'jpush',
}

export interface NotificationToken {
  provider: NotificationProvider;
  token: string;
  modifiedAt: string;
  isActive: boolean;
}

export interface NotificationChannel {
  id: string;
  name: string;
  description: string;
  importance: number;
  visibility?: number;
  sound?: string;
  vibration?: boolean;
  lights?: boolean;
  badge?: boolean;
}

export interface NotificationData {
  id: string;
  title: string;
  subtitle?: string;
  body: string;
  image?: string;
  sound?: string;
  data: any;
  options?: {
    sync?: string;
    banner?: boolean;
    nav?: any;
  };
}

export interface ProcessedNotification {
  id: string;
  title: string;
  subtitle?: string;
  body: string;
  data: any;
  receivedAt: string;
  handledAt?: string;
  tapped: boolean;
  inApp: boolean;
  shouldShowBanner: boolean;
  processed: boolean;
}

export interface NotificationBadge {
  cardId: string;
  unread: number;
  attention: boolean;
}

export interface BadgeState {
  cardBadges: Record<string, number>;
  appBadge: number;
  lastUpdated: string;
}

export interface NotificationHistory {
  id: string;
  notification: ProcessedNotification;
  action?: any;
  engagement?: any;
  navigation?: any;
  timestamp: string;
}

export interface NotificationsState {
  // Provider management
  activeProvider: NotificationProvider | null;
  providers: {
    [key in NotificationProvider]?: {
      registered: boolean;
      available: boolean;
      error?: string;
    };
  };
  
  // Token management
  tokens: NotificationToken[];
  currentToken: string | null;
  
  // Notification processing
  handledNotifications: string[];
  receivedNotifications: string[];
  processingQueue: ProcessedNotification[];
  
  // Badge management
  badges: BadgeState;
  
  // Notification history
  history: NotificationHistory[];
  
  // Channels (Android)
  channels: NotificationChannel[];
  
  // State management
  isActive: boolean;
  isInitialized: boolean;
  lastSync: string | null;
  
  // Error handling
  error: string | null;
  isLoading: boolean;
  
  // Settings
  bannerEnabled: boolean;
  soundEnabled: boolean;
}

// Business Logic Interfaces

export interface MultiProviderArchitecture {
  selectProvider(platform: string, region: string): NotificationProvider;
  loadProvider(providerName: NotificationProvider): Promise<NotificationProviderInstance>;
  getProviderCapabilities(provider: NotificationProvider): ProviderCapabilities;
}

export interface NotificationStateTracking {
  isNotificationHandled(id: string): boolean;
  markNotificationHandled(id: string): void;
  isNotificationReceived(id: string): boolean;
  markNotificationReceived(id: string): void;
  cleanupOldNotifications(): void;
}

export interface BadgeManagement {
  getCardBadge(cardId: string): number;
  getAppBadge(): number;
  updateCardBadge(cardId: string, count: number): void;
  refreshAllBadges(): void;
  syncWithPlatform(): void;
}

export interface NotificationProcessing {
  processNotification(notification: NotificationData, tapped: boolean, inApp: boolean): Promise<ProcessedNotification>;
  handleNotificationAction(notification: ProcessedNotification): Promise<void>;
  triggerEngagement(notification: ProcessedNotification): Promise<void>;
  handleNavigation(notification: ProcessedNotification): Promise<void>;
}

// Provider Interfaces

export interface NotificationProviderInstance {
  provider: NotificationProvider;
  register(): Promise<{ alert: boolean }>;
  getToken(): Promise<string>;
  setToken(token: string): void;
  init(): void;
  initialNotification(): Promise<NotificationData | null>;
}

export interface ProviderCapabilities {
  richNotifications: boolean;
  badgeSupport: boolean;
  soundSupport: boolean;
  imageSupport: boolean;
  actionSupport: boolean;
}

// Action Payloads

export interface InitializeNotificationsPayload {
  platform: string;
  region: string;
}

export interface RegisterProviderPayload {
  provider: NotificationProvider;
}

export interface ProcessNotificationPayload {
  notification: NotificationData;
  tapped: boolean;
  inApp: boolean;
}

export interface UpdateBadgePayload {
  cardId: string;
  count: number;
}

export interface SetTokenPayload {
  provider: NotificationProvider;
  token: string;
}

export interface HandleNotificationPayload {
  id: string;
  tapped: boolean;
}

// Additional types needed by NotificationService
export interface NotificationPayload {
  id?: string;
  title: string;
  subtitle?: string;
  body: string;
  data?: any;
  badge?: number;
  sound?: string;
  read?: boolean;
  priority?: string;
  channelId?: string;
  timestamp?: number;
  showInForeground?: boolean;
}

export interface NotificationSettings {
  authorizationStatus: string;
  sound: number;
  badge: number;
  alert: number;
  criticalAlert: number;
  provisional: number;
  lockScreen: number;
  notificationCenter: number;
}

// Remove this type alias - use NotificationProvider enum directly

// Constants

export const NotificationChannels = {
  PERKD: 'Perkd',
  ALERTS: 'Alerts',
} as const;

export const NotificationImportance = {
  HIGH: 4,
  DEFAULT: 3,
  LOW: 2,
  MIN: 1,
} as const;

export const NotificationSounds = {
  CHORD: 'chord',
  DEFAULT: 'default',
} as const;

// Error Types

export interface NotificationError {
  code: string;
  message: string;
  provider?: NotificationProvider;
  context?: any;
}

export const NotificationErrorCodes = {
  PROVIDER_SELECTION_FAILED: 'Provider selection failed',
  PROVIDER_REGISTRATION_FAILED: 'Provider registration failed',
  TOKEN_SYNC_FAILED: 'Token sync failed',
  NOTIFICATION_PROCESSING_FAILED: 'Notification processing failed',
  BADGE_UPDATE_FAILED: 'Badge update failed',
  CHANNEL_CREATION_FAILED: 'Channel creation failed',
  PERMISSION_DENIED: 'Notification permission denied',
  EVENT_HANDLING_FAILED: 'Event handling failed',
} as const;

// Utility Types

export type NotificationProviderType = typeof NotificationProvider[keyof typeof NotificationProvider];
export type NotificationChannelType = typeof NotificationChannels[keyof typeof NotificationChannels];
export type NotificationImportanceType = typeof NotificationImportance[keyof typeof NotificationImportance];
export type NotificationErrorCode = typeof NotificationErrorCodes[keyof typeof NotificationErrorCodes];

// Helper Functions Types

export interface NotificationHelpers {
  isNotificationValid(notification: NotificationData): boolean;
  shouldShowBanner(notification: NotificationData, inApp: boolean): boolean;
  shouldTriggerSync(notification: NotificationData): boolean;
  extractNavigationData(notification: NotificationData): any;
  extractEngagementData(notification: NotificationData): any;
}

// Market Detection

export interface MarketDetection {
  isChineseMarket(): boolean;
  getMarketSpecificProvider(): NotificationProvider;
  hasGoogleServices(): boolean;
}

// Notification Parsing

export interface NotificationParser {
  parseAPNsNotification(data: any): NotificationData;
  parseFCMNotification(data: any): NotificationData;
  parseJPushNotification(data: any): NotificationData;
  parseNotifeeNotification(data: any): NotificationData;
}

// Local Notifications

export interface LocalNotificationData {
  id: string;
  title: string;
  body: string;
  data: any;
  trigger?: {
    type: 'timeInterval' | 'calendar';
    timeInterval?: number;
    date?: Date;
    repeats?: boolean;
  };
  ios?: {
    sound?: string;
    badge?: number;
    attachments?: any[];
  };
  android?: {
    channelId: string;
    sound?: string;
    importance?: number;
    autoCancel?: boolean;
  };
}

export interface LocalNotificationManager {
  scheduleNotification(notification: LocalNotificationData): Promise<void>;
  cancelNotification(id: string): Promise<void>;
  cancelAllNotifications(): Promise<void>;
  getPendingNotifications(): Promise<LocalNotificationData[]>;
}
