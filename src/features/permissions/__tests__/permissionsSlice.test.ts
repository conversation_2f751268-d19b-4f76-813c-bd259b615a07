/**
 * V7 Permissions Redux Slice Tests
 * 
 * Comprehensive tests for Redux Toolkit slice including action creators, reducers,
 * async thunks, state transitions, and error handling
 */

import { configureStore } from '@reduxjs/toolkit';
import permissionsReducer, {
  updatePermissionStatus,
  setBackgroundState,
  addToRequestQueue,
  removeFromRequestQueue,
  setActiveRequest,
  setError,
  clearError,
  setLoading,
  setInitialized,
  checkPermission,
  requestPermission,
  refreshPermissions,
} from '../store/permissionsSlice';
import {
  PermissionsState,
  Permission,
  PermissionFeature,
  PermissionStatus,
  PermissionOptions,
  UpdatePermissionPayload,
  SetBackgroundStatePayload,
  RequestPermissionPayload,
  RefreshPermissionsPayload,
  PermissionErrorCodes,
} from '../types';

// =============================================================================
// TEST STORE SETUP
// =============================================================================

function createTestStore(initialState?: Partial<PermissionsState>) {
  return configureStore({
    reducer: {
      permissions: permissionsReducer,
    },
    preloadedState: {
      permissions: {
        permissions: [],
        requestQueue: [],
        activeRequest: null,
        isInitialized: false,
        lastSync: null,
        lastRefresh: null,
        error: null,
        isLoading: false,
        isInBackground: false,
        ...initialState,
      },
    },
  });
}

// =============================================================================
// MOCK DATA
// =============================================================================

const mockPermission: Permission = {
  feature: PermissionFeature.CAMERA,
  status: PermissionStatus.ALLOWED,
  options: [],
  grantedAt: '2024-01-01T00:00:00Z',
  revokedAt: null,
  lastChecked: '2024-01-01T00:00:00Z',
};

const mockPermissionOptions: PermissionOptions = {
  showRationale: true,
  type: 'whenInUse',
};

// =============================================================================
// MOCK FUNCTIONS
// =============================================================================

// Mock the helper functions
global.checkPermissionStatus = jest.fn();
global.checkAndRequestPermission = jest.fn();
global.detectPermissionChanges = jest.fn();

// =============================================================================
// REDUX SLICE TESTS
// =============================================================================

describe('Permissions Redux Slice Tests', () => {
  
  // =============================================================================
  // INITIAL STATE TESTS
  // =============================================================================
  
  describe('Initial State', () => {
    test('should have correct initial state', () => {
      const store = createTestStore();
      const state = store.getState().permissions;
      
      expect(state.permissions).toEqual([]);
      expect(state.requestQueue).toEqual([]);
      expect(state.activeRequest).toBeNull();
      expect(state.isInitialized).toBe(false);
      expect(state.lastSync).toBeNull();
      expect(state.lastRefresh).toBeNull();
      expect(state.error).toBeNull();
      expect(state.isLoading).toBe(false);
      expect(state.isInBackground).toBe(false);
    });
  });
  
  // =============================================================================
  // SYNCHRONOUS ACTION TESTS
  // =============================================================================
  
  describe('Synchronous Actions', () => {
    test('should update permission status', () => {
      const store = createTestStore();
      
      const updatePayload: UpdatePermissionPayload = {
        feature: PermissionFeature.CAMERA,
        status: PermissionStatus.ALLOWED,
        options: [],
      };
      
      store.dispatch(updatePermissionStatus(updatePayload));
      
      const state = store.getState().permissions;
      expect(state.permissions).toHaveLength(1);
      expect(state.permissions[0].feature).toBe(PermissionFeature.CAMERA);
      expect(state.permissions[0].status).toBe(PermissionStatus.ALLOWED);
      expect(state.permissions[0].grantedAt).toBeTruthy();
      expect(state.permissions[0].revokedAt).toBeNull();
    });
    
    test('should update existing permission', () => {
      const store = createTestStore({
        permissions: [mockPermission],
      });
      
      const updatePayload: UpdatePermissionPayload = {
        feature: PermissionFeature.CAMERA,
        status: PermissionStatus.BLOCKED,
      };
      
      store.dispatch(updatePermissionStatus(updatePayload));
      
      const state = store.getState().permissions;
      expect(state.permissions).toHaveLength(1);
      expect(state.permissions[0].status).toBe(PermissionStatus.BLOCKED);
      expect(state.permissions[0].grantedAt).toBeNull();
      expect(state.permissions[0].revokedAt).toBeTruthy();
    });
    
    test('should set background state', () => {
      const store = createTestStore();
      
      const backgroundPayload: SetBackgroundStatePayload = {
        isInBackground: true,
      };
      
      store.dispatch(setBackgroundState(backgroundPayload));
      
      const state = store.getState().permissions;
      expect(state.isInBackground).toBe(true);
    });
    
    test('should manage request queue', () => {
      const store = createTestStore();
      
      // Add to queue
      store.dispatch(addToRequestQueue({
        feature: PermissionFeature.CAMERA,
        options: mockPermissionOptions,
      }));
      
      let state = store.getState().permissions;
      expect(state.requestQueue).toHaveLength(1);
      expect(state.requestQueue[0].feature).toBe(PermissionFeature.CAMERA);
      expect(state.requestQueue[0].status).toBe('pending');
      
      // Remove from queue
      store.dispatch(removeFromRequestQueue(PermissionFeature.CAMERA));
      
      state = store.getState().permissions;
      expect(state.requestQueue).toHaveLength(0);
    });
    
    test('should set active request', () => {
      const store = createTestStore();
      
      store.dispatch(setActiveRequest({
        feature: PermissionFeature.CAMERA,
        options: mockPermissionOptions,
      }));
      
      let state = store.getState().permissions;
      expect(state.activeRequest).toBeTruthy();
      expect(state.activeRequest?.feature).toBe(PermissionFeature.CAMERA);
      expect(state.activeRequest?.status).toBe('pending');
      
      // Clear active request
      store.dispatch(setActiveRequest(null));
      
      state = store.getState().permissions;
      expect(state.activeRequest).toBeNull();
    });
    
    test('should handle error states', () => {
      const store = createTestStore();
      const error = {
        code: PermissionErrorCodes.REQUEST_FAILED,
        message: 'Permission request failed',
        feature: PermissionFeature.CAMERA,
      };
      
      store.dispatch(setError(error));
      let state = store.getState().permissions;
      expect(state.error).toBe(error.message);
      expect(state.isLoading).toBe(false);
      
      store.dispatch(clearError());
      state = store.getState().permissions;
      expect(state.error).toBeNull();
    });
    
    test('should handle loading and initialization states', () => {
      const store = createTestStore();
      
      store.dispatch(setLoading(true));
      let state = store.getState().permissions;
      expect(state.isLoading).toBe(true);
      
      store.dispatch(setInitialized(true));
      state = store.getState().permissions;
      expect(state.isInitialized).toBe(true);
    });
  });
  
  // =============================================================================
  // ASYNC THUNK TESTS
  // =============================================================================
  
  describe('Async Thunks', () => {
    beforeEach(() => {
      // Reset mocks
      jest.clearAllMocks();
    });
    
    test('should handle checkPermission success', async () => {
      global.checkPermissionStatus = jest.fn().mockResolvedValue({
        status: PermissionStatus.ALLOWED,
        options: [],
      });
      
      const store = createTestStore();
      
      const action = await store.dispatch(checkPermission(PermissionFeature.CAMERA));
      
      expect(action.type).toBe('permissions/check/fulfilled');
      expect(global.checkPermissionStatus).toHaveBeenCalledWith(PermissionFeature.CAMERA);
      
      const state = store.getState().permissions;
      expect(state.permissions).toHaveLength(1);
      expect(state.permissions[0].feature).toBe(PermissionFeature.CAMERA);
      expect(state.permissions[0].status).toBe(PermissionStatus.ALLOWED);
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });
    
    test('should handle checkPermission failure', async () => {
      global.checkPermissionStatus = jest.fn().mockRejectedValue(new Error('Check failed'));
      
      const store = createTestStore();
      
      const action = await store.dispatch(checkPermission(PermissionFeature.CAMERA));
      
      expect(action.type).toBe('permissions/check/rejected');
      
      const state = store.getState().permissions;
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeTruthy();
    });
    
    test('should handle requestPermission success', async () => {
      global.checkAndRequestPermission = jest.fn().mockResolvedValue(true);
      
      const store = createTestStore();
      
      const requestPayload: RequestPermissionPayload = {
        feature: PermissionFeature.CAMERA,
        options: mockPermissionOptions,
      };
      
      const action = await store.dispatch(requestPermission(requestPayload));
      
      expect(action.type).toBe('permissions/request/fulfilled');
      expect(global.checkAndRequestPermission).toHaveBeenCalledWith(
        PermissionFeature.CAMERA,
        mockPermissionOptions
      );
      
      const state = store.getState().permissions;
      expect(state.permissions).toHaveLength(1);
      expect(state.permissions[0].status).toBe(PermissionStatus.ALLOWED);
      expect(state.activeRequest).toBeNull();
      expect(state.isLoading).toBe(false);
    });
    
    test('should handle requestPermission failure', async () => {
      global.checkAndRequestPermission = jest.fn().mockRejectedValue(new Error('Request failed'));
      
      const store = createTestStore();
      
      const requestPayload: RequestPermissionPayload = {
        feature: PermissionFeature.CAMERA,
        options: mockPermissionOptions,
      };
      
      const action = await store.dispatch(requestPermission(requestPayload));
      
      expect(action.type).toBe('permissions/request/rejected');
      
      const state = store.getState().permissions;
      expect(state.activeRequest).toBeNull();
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeTruthy();
    });
    
    test('should prevent background requests', async () => {
      const store = createTestStore({
        isInBackground: true,
      });
      
      const requestPayload: RequestPermissionPayload = {
        feature: PermissionFeature.CAMERA,
        options: mockPermissionOptions,
      };
      
      const action = await store.dispatch(requestPermission(requestPayload));
      
      expect(action.type).toBe('permissions/request/rejected');
      expect(action.payload?.message).toContain('background');
    });
    
    test('should handle refreshPermissions for single feature', async () => {
      global.checkPermissionStatus = jest.fn().mockResolvedValue({
        status: PermissionStatus.ALLOWED,
        options: [],
      });
      global.detectPermissionChanges = jest.fn().mockReturnValue([]);
      
      const store = createTestStore({
        permissions: [mockPermission],
      });
      
      const refreshPayload: RefreshPermissionsPayload = {
        feature: PermissionFeature.CAMERA,
      };
      
      const action = await store.dispatch(refreshPermissions(refreshPayload));
      
      expect(action.type).toBe('permissions/refresh/fulfilled');
      expect(global.checkPermissionStatus).toHaveBeenCalledWith(PermissionFeature.CAMERA);
      
      const state = store.getState().permissions;
      expect(state.lastRefresh).toBeTruthy();
      expect(state.isLoading).toBe(false);
    });
    
    test('should handle refreshPermissions for all features', async () => {
      global.checkPermissionStatus = jest.fn().mockResolvedValue({
        status: PermissionStatus.ALLOWED,
        options: [],
      });
      global.detectPermissionChanges = jest.fn().mockReturnValue([]);
      
      const store = createTestStore();
      
      const action = await store.dispatch(refreshPermissions({}));
      
      expect(action.type).toBe('permissions/refresh/fulfilled');
      // Should be called for all permission features
      expect(global.checkPermissionStatus).toHaveBeenCalledTimes(11); // Number of features in PERMISSION_FEATURES
    });
  });
  
  // =============================================================================
  // STATE TRANSITION TESTS
  // =============================================================================
  
  describe('State Transitions', () => {
    test('should handle loading states correctly during async operations', async () => {
      global.checkPermissionStatus = jest.fn().mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve({ status: PermissionStatus.ALLOWED, options: [] }), 100))
      );
      
      const store = createTestStore();
      
      // Start async operation
      const promise = store.dispatch(checkPermission(PermissionFeature.CAMERA));
      
      // Check pending state
      let state = store.getState().permissions;
      expect(state.isLoading).toBe(true);
      expect(state.error).toBeNull();
      
      // Wait for completion
      await promise;
      
      // Check fulfilled state
      state = store.getState().permissions;
      expect(state.isLoading).toBe(false);
      expect(state.error).toBeNull();
    });
    
    test('should maintain state immutability', () => {
      const store = createTestStore();
      const initialState = store.getState().permissions;
      
      store.dispatch(updatePermissionStatus({
        feature: PermissionFeature.CAMERA,
        status: PermissionStatus.ALLOWED,
      }));
      
      const newState = store.getState().permissions;
      expect(newState).not.toBe(initialState);
      expect(newState.permissions).not.toBe(initialState.permissions);
    });
    
    test('should handle concurrent permission requests', async () => {
      global.checkAndRequestPermission = jest.fn().mockResolvedValue(true);
      
      const store = createTestStore();
      
      // Start multiple requests
      const cameraPromise = store.dispatch(requestPermission({
        feature: PermissionFeature.CAMERA,
        options: {},
      }));
      
      const photoPromise = store.dispatch(requestPermission({
        feature: PermissionFeature.PHOTO,
        options: {},
      }));
      
      // Wait for both to complete
      await Promise.all([cameraPromise, photoPromise]);
      
      const state = store.getState().permissions;
      expect(state.permissions).toHaveLength(2);
      expect(state.activeRequest).toBeNull();
    });
  });
  
  // =============================================================================
  // EDGE CASE TESTS
  // =============================================================================
  
  describe('Edge Cases', () => {
    test('should handle permission status updates with timestamps', () => {
      const store = createTestStore();
      
      // Grant permission
      store.dispatch(updatePermissionStatus({
        feature: PermissionFeature.CAMERA,
        status: PermissionStatus.ALLOWED,
      }));
      
      let state = store.getState().permissions;
      expect(state.permissions[0].grantedAt).toBeTruthy();
      expect(state.permissions[0].revokedAt).toBeNull();
      
      // Revoke permission
      store.dispatch(updatePermissionStatus({
        feature: PermissionFeature.CAMERA,
        status: PermissionStatus.BLOCKED,
      }));
      
      state = store.getState().permissions;
      expect(state.permissions[0].grantedAt).toBeNull();
      expect(state.permissions[0].revokedAt).toBeTruthy();
    });
    
    test('should handle limited permission status', () => {
      const store = createTestStore();
      
      store.dispatch(updatePermissionStatus({
        feature: PermissionFeature.LOCATION,
        status: PermissionStatus.LIMITED,
        options: ['whenInUse'],
      }));
      
      const state = store.getState().permissions;
      expect(state.permissions[0].status).toBe(PermissionStatus.LIMITED);
      expect(state.permissions[0].grantedAt).toBeTruthy();
      expect(state.permissions[0].options).toEqual(['whenInUse']);
    });
  });
});
