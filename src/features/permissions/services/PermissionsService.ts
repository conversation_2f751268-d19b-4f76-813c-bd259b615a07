// V7 Permissions Service - Platform Integration Layer
// Simple, robust implementation with react-native-permissions

import { Platform, AppState } from 'react-native';
import {
  check,
  request,
  openSettings,
  PERMISSIONS,
  RESULTS,
  Permission as RNPermission,
  PermissionStatus as RNPermissionStatus,
} from 'react-native-permissions';
import {
  PermissionFeature,
  PermissionStatus,
  Permission,
  PermissionOptions,
  PLATFORM_PERMISSION_MAP,
  NATIVE_STATUS_MAP,
} from '../types';

// =============================================================================
// PERMISSION STATUS CHECKING
// =============================================================================

export async function checkPermissionStatus(feature: PermissionFeature): Promise<{
  status: PermissionStatus;
  options: string[];
}> {
  try {
    const platformPermission = getPlatformPermission(feature);
    if (!platformPermission) {
      return { status: PermissionStatus.UNAVAILABLE, options: [] };
    }

    const result = await check(platformPermission);
    const status = normalizePermissionStatus(result);
    const options = getPermissionOptions(feature, result);

    return { status, options };
  } catch (error) {
    console.warn(`Failed to check permission ${feature}:`, error);
    return { status: PermissionStatus.UNKNOWN, options: [] };
  }
}

// =============================================================================
// PERMISSION REQUESTING
// =============================================================================

export async function requestPermission(
  feature: PermissionFeature,
  options: PermissionOptions = {}
): Promise<boolean> {
  try {
    const platformPermission = getPlatformPermission(feature);
    if (!platformPermission) {
      return false;
    }

    // Check current status first
    const currentStatus = await check(platformPermission);
    if (currentStatus === RESULTS.GRANTED || currentStatus === RESULTS.LIMITED) {
      return true;
    }

    // If blocked, direct to settings
    if (currentStatus === RESULTS.BLOCKED) {
      if (options.showRationale) {
        await openSettings();
      }
      return false;
    }

    // Request permission
    const result = await request(platformPermission);
    return result === RESULTS.GRANTED || result === RESULTS.LIMITED;
  } catch (error) {
    console.warn(`Failed to request permission ${feature}:`, error);
    return false;
  }
}

// =============================================================================
// PERMISSION MAPPING
// =============================================================================

function getPlatformPermission(feature: PermissionFeature): RNPermission | null {
  const mapping = PLATFORM_PERMISSION_MAP[feature];
  if (!mapping) return null;

  const platform = Platform.OS as 'ios' | 'android';
  let permission = mapping[platform];

  // Handle Android version-specific permissions
  if (platform === 'android' && mapping.androidVersionSpecific) {
    const androidVersion = Platform.Version as number;
    for (const [version, versionPermission] of Object.entries(mapping.androidVersionSpecific)) {
      if (androidVersion >= parseInt(version, 10)) {
        permission = versionPermission;
      }
    }
  }

  // Convert string to actual permission constant
  return getPermissionConstant(permission);
}

function getPermissionConstant(permissionString: string): RNPermission | null {
  // Map permission strings to react-native-permissions constants
  const permissionMap: Record<string, RNPermission> = {
    // iOS permissions
    'ios.permission.CAMERA': PERMISSIONS.IOS.CAMERA,
    'ios.permission.PHOTO_LIBRARY': PERMISSIONS.IOS.PHOTO_LIBRARY,
    'ios.permission.CONTACTS': PERMISSIONS.IOS.CONTACTS,
    'ios.permission.CALENDARS': PERMISSIONS.IOS.CALENDARS,
    'ios.permission.LOCATION_WHEN_IN_USE': PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
    'ios.permission.LOCATION_ALWAYS': PERMISSIONS.IOS.LOCATION_ALWAYS,
    
    // Android permissions
    'android.permission.CAMERA': PERMISSIONS.ANDROID.CAMERA,
    'android.permission.READ_EXTERNAL_STORAGE': PERMISSIONS.ANDROID.READ_EXTERNAL_STORAGE,
    'android.permission.READ_MEDIA_IMAGES': PERMISSIONS.ANDROID.READ_MEDIA_IMAGES,
    'android.permission.READ_CONTACTS': PERMISSIONS.ANDROID.READ_CONTACTS,
    'android.permission.WRITE_CALENDAR': PERMISSIONS.ANDROID.WRITE_CALENDAR,
    'android.permission.ACCESS_FINE_LOCATION': PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    'android.permission.ACCESS_COARSE_LOCATION': PERMISSIONS.ANDROID.ACCESS_COARSE_LOCATION,
  };

  return permissionMap[permissionString] || null;
}

// =============================================================================
// STATUS NORMALIZATION
// =============================================================================

export function normalizePermissionStatus(
  nativeStatus: RNPermissionStatus,
  platform: string = Platform.OS
): PermissionStatus {
  // Use the mapping from types
  const statusMap = NATIVE_STATUS_MAP[nativeStatus];
  
  if (statusMap) {
    // Handle platform-specific logic for denied status
    if (nativeStatus === RESULTS.DENIED && platform === 'ios') {
      return PermissionStatus.UNKNOWN;
    }
    return statusMap;
  }

  // Fallback
  return PermissionStatus.UNKNOWN;
}

// =============================================================================
// PERMISSION OPTIONS
// =============================================================================

function getPermissionOptions(feature: PermissionFeature, status: RNPermissionStatus): string[] {
  const options: string[] = [];

  // Add platform-specific options based on feature and status
  if (feature === PermissionFeature.LOCATION) {
    if (status === RESULTS.GRANTED) {
      options.push('whileInApp');
      if (Platform.OS === 'ios') {
        // Check if always permission is also granted
        options.push('fullAccuracy');
      }
    }
  }

  if (feature === PermissionFeature.PHOTO && status === RESULTS.LIMITED) {
    options.push('limited');
  }

  return options;
}

// =============================================================================
// PERMISSION VALIDATION
// =============================================================================

export function isPermissionRequestable(permission: Permission | null): boolean {
  if (!permission) return true;
  
  return permission.status === PermissionStatus.UNKNOWN ||
         permission.status === PermissionStatus.DENIED;
}

export function isPermissionBlocked(permission: Permission | null): boolean {
  return permission?.status === PermissionStatus.BLOCKED;
}

export function isPermissionGranted(permission: Permission | null): boolean {
  return permission?.status === PermissionStatus.ALLOWED ||
         permission?.status === PermissionStatus.LIMITED;
}

// =============================================================================
// PROGRESSIVE PERMISSION FLOW
// =============================================================================

export function shouldRequestPermission(
  permission: Permission | null,
  options: PermissionOptions
): {
  shouldRequest: boolean;
  action: 'grant' | 'request' | 'settings' | 'rationale';
  reason: string;
} {
  // V6 Business Logic: Progressive permission flow
  
  if (!permission || permission.status === PermissionStatus.UNKNOWN) {
    return {
      shouldRequest: true,
      action: 'request',
      reason: 'Direct permission request',
    };
  }

  if (permission.status === PermissionStatus.ALLOWED || permission.status === PermissionStatus.LIMITED) {
    return {
      shouldRequest: false,
      action: 'grant',
      reason: 'Already granted',
    };
  }

  if (permission.status === PermissionStatus.BLOCKED) {
    return {
      shouldRequest: false,
      action: 'settings',
      reason: 'Permission blocked - redirect to settings',
    };
  }

  if (permission.status === PermissionStatus.DENIED) {
    if (options.showRationale) {
      return {
        shouldRequest: false,
        action: 'rationale',
        reason: 'Show rationale before request',
      };
    }
    return {
      shouldRequest: true,
      action: 'request',
      reason: 'Retry permission request',
    };
  }

  if (permission.status === PermissionStatus.UNAVAILABLE) {
    return {
      shouldRequest: false,
      action: 'grant',
      reason: 'Feature unavailable on device',
    };
  }

  return {
    shouldRequest: true,
    action: 'request',
    reason: 'Default permission request',
  };
}

// =============================================================================
// BACKGROUND STATE MANAGEMENT
// =============================================================================

export function isAppInBackground(): boolean {
  return AppState.currentState === 'background';
}

export function canRequestInBackground(): boolean {
  // V6 Business Logic: Prevent background permission requests
  return false;
}

// =============================================================================
// PERMISSION CHANGE DETECTION
// =============================================================================

export function detectPermissionChanges(
  existing: Permission[],
  current: Permission[]
): Permission[] {
  const changes: Permission[] = [];
  
  for (const currentPerm of current) {
    const existingPerm = existing.find(p => p.feature === currentPerm.feature);
    
    if (!existingPerm || existingPerm.status !== currentPerm.status) {
      changes.push(currentPerm);
    }
  }
  
  return changes;
}

// =============================================================================
// SETTINGS NAVIGATION
// =============================================================================

export async function openPermissionSettings(): Promise<void> {
  try {
    await openSettings();
  } catch (error) {
    console.warn('Failed to open permission settings:', error);
  }
}
