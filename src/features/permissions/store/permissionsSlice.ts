// V7 Permissions Redux Slice - Pure Redux Toolkit Implementation
// Business Logic Extracted from V6 src/lib/common/permissions.js

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Platform, AppState } from 'react-native';
import {
  PermissionsState,
  Permission,
  PermissionFeature,
  PermissionStatus,
  PermissionOptions,
  RequestPermissionPayload,
  UpdatePermissionPayload,
  RefreshPermissionsPayload,
  SetBackgroundStatePayload,
  PermissionError,
  PermissionErrorCodes,
  PERMISSION_FEATURES,
  NATIVE_STATUS_MAP,
  PLATFORM_PERMISSION_MAP,
} from '../types';
import * as PermissionsService from '../services/PermissionsService';

// Initial State
const initialState: PermissionsState = {
  permissions: [],
  requestQueue: [],
  activeRequest: null,
  isInitialized: false,
  lastSync: null,
  lastRefresh: null,
  error: null,
  isLoading: false,
  isInBackground: false,
};

// Async Thunks for Complex Operations

export const checkPermission = createAsyncThunk(
  'permissions/check',
  async (feature: PermissionFeature, { rejectWithValue }) => {
    try {
      const result = await checkPermissionStatus(feature);
      const now = new Date().toISOString();
      return {
        feature,
        ...result,
        lastChecked: now,
        grantedAt: result.status === PermissionStatus.ALLOWED ? now : null,
        revokedAt: result.status === PermissionStatus.BLOCKED ? now : null,
      };
    } catch (error) {
      return rejectWithValue({
        code: PermissionErrorCodes.CHECK_FAILED,
        message: error instanceof Error ? error.message : 'Permission check failed',
        feature,
      });
    }
  }
);

export const requestPermission = createAsyncThunk(
  'permissions/request',
  async ({ feature, options = {} }: RequestPermissionPayload, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { permissions: PermissionsState };
      
      // V6 Business Logic: Prevent background requests
      if (state.permissions.isInBackground) {
        throw new Error('Cannot request permission in background');
      }
      
      // V6 Business Logic: Progressive permission flow
      const result = await checkAndRequestPermission(feature, options);
      
      return {
        feature,
        granted: result,
        requestedAt: new Date().toISOString(),
      };
    } catch (error) {
      return rejectWithValue({
        code: PermissionErrorCodes.REQUEST_FAILED,
        message: error instanceof Error ? error.message : 'Permission request failed',
        feature,
      });
    }
  }
);

export const refreshPermissions = createAsyncThunk(
  'permissions/refresh',
  async ({ feature }: RefreshPermissionsPayload = {}, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { permissions: PermissionsState };
      const existingPermissions = state.permissions.permissions;
      
      // V6 Business Logic: Refresh specific feature or all features
      const featuresToRefresh = feature ? [feature] : PERMISSION_FEATURES;
      
      const currentPermissions = await Promise.all(
        featuresToRefresh.map(async (f) => {
          const result = await checkPermissionStatus(f);
          const now = new Date().toISOString();
          return {
            feature: f,
            ...result,
            lastChecked: now,
            grantedAt: result.status === PermissionStatus.ALLOWED ? now : null,
            revokedAt: result.status === PermissionStatus.BLOCKED ? now : null,
          };
        })
      );
      
      // V6 Business Logic: Detect changes and emit events
      const changes = detectPermissionChanges(existingPermissions, currentPermissions);
      
      return {
        permissions: currentPermissions,
        changes,
        refreshedAt: new Date().toISOString(),
      };
    } catch (error) {
      return rejectWithValue({
        code: PermissionErrorCodes.CHECK_FAILED,
        message: error instanceof Error ? error.message : 'Permission refresh failed',
      });
    }
  }
);

// Main Slice
const permissionsSlice = createSlice({
  name: 'permissions',
  initialState,
  reducers: {
    // V6 Business Logic: Update permission status with timestamp tracking
    updatePermissionStatus: (state, action: PayloadAction<UpdatePermissionPayload>) => {
      const { feature, status, options = [], grantedAt, revokedAt } = action.payload;
      const existingIndex = state.permissions.findIndex(p => p.feature === feature);
      
      const now = new Date().toISOString();
      const permissionUpdate: Permission = {
        feature,
        status,
        options,
        grantedAt: (status === PermissionStatus.ALLOWED || status === PermissionStatus.LIMITED) ? (grantedAt || now) : null,
        revokedAt: (status === PermissionStatus.BLOCKED || status === PermissionStatus.DENIED) ? (revokedAt || now) : null,
        lastChecked: now,
      };
      
      if (existingIndex !== -1) {
        state.permissions[existingIndex] = permissionUpdate;
      } else {
        state.permissions.push(permissionUpdate);
      }
    },

    // V6 Business Logic: Background state management
    setBackgroundState: (state, action: PayloadAction<SetBackgroundStatePayload>) => {
      state.isInBackground = action.payload.isInBackground;
    },

    // Request queue management
    addToRequestQueue: (state, action: PayloadAction<{ feature: PermissionFeature; options: PermissionOptions }>) => {
      const { feature, options } = action.payload;
      state.requestQueue.push({
        feature,
        options,
        requestedAt: new Date().toISOString(),
        status: 'pending',
      });
    },

    removeFromRequestQueue: (state, action: PayloadAction<PermissionFeature>) => {
      const feature = action.payload;
      state.requestQueue = state.requestQueue.filter(req => req.feature !== feature);
    },

    setActiveRequest: (state, action: PayloadAction<{ feature: PermissionFeature; options: PermissionOptions } | null>) => {
      if (action.payload) {
        state.activeRequest = {
          feature: action.payload.feature,
          options: action.payload.options,
          requestedAt: new Date().toISOString(),
          status: 'pending',
        };
      } else {
        state.activeRequest = null;
      }
    },

    // Error handling
    setError: (state, action: PayloadAction<PermissionError>) => {
      state.error = action.payload.message;
      state.isLoading = false;
    },

    clearError: (state) => {
      state.error = null;
    },

    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Initialization
    setInitialized: (state, action: PayloadAction<boolean>) => {
      state.isInitialized = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Check Permission
      .addCase(checkPermission.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(checkPermission.fulfilled, (state, action) => {
        const permission = action.payload;
        const existingIndex = state.permissions.findIndex(p => p.feature === permission.feature);
        
        if (existingIndex !== -1) {
          state.permissions[existingIndex] = permission;
        } else {
          state.permissions.push(permission);
        }
        
        state.isLoading = false;
        state.error = null;
      })
      .addCase(checkPermission.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as any)?.message || action.error?.message || 'Permission check failed';
      })
      
      // Request Permission
      .addCase(requestPermission.pending, (state, action) => {
        state.isLoading = true;
        state.error = null;
        
        // Set active request
        state.activeRequest = {
          feature: action.meta.arg.feature,
          options: action.meta.arg.options || {},
          requestedAt: new Date().toISOString(),
          status: 'pending',
        };
      })
      .addCase(requestPermission.fulfilled, (state, action) => {
        const { feature, granted } = action.payload;
        
        // Update permission status based on result
        const status = granted ? PermissionStatus.ALLOWED : PermissionStatus.DENIED;
        const now = new Date().toISOString();
        
        const existingIndex = state.permissions.findIndex(p => p.feature === feature);
        const permissionUpdate: Permission = {
          feature,
          status,
          options: [],
          grantedAt: granted ? now : null,
          revokedAt: !granted ? now : null,
          lastChecked: now,
        };
        
        if (existingIndex !== -1) {
          state.permissions[existingIndex] = permissionUpdate;
        } else {
          state.permissions.push(permissionUpdate);
        }
        
        state.activeRequest = null;
        state.isLoading = false;
        state.error = null;
      })
      .addCase(requestPermission.rejected, (state, action) => {
        state.activeRequest = null;
        state.isLoading = false;
        state.error = (action.payload as any)?.message || action.error?.message || 'Permission request failed';
      })
      
      // Refresh Permissions
      .addCase(refreshPermissions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(refreshPermissions.fulfilled, (state, action) => {
        const { permissions, refreshedAt } = action.payload;
        
        // Update permissions with refreshed data
        permissions.forEach(permission => {
          const existingIndex = state.permissions.findIndex(p => p.feature === permission.feature);
          if (existingIndex !== -1) {
            state.permissions[existingIndex] = permission;
          } else {
            state.permissions.push(permission);
          }
        });
        
        state.lastRefresh = refreshedAt;
        state.isLoading = false;
        state.error = null;
      })
      .addCase(refreshPermissions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as any)?.message || action.error?.message || 'Permission refresh failed';
      });
  },
});

// V6 Business Logic Functions (Pure Functions)

// V6 Progressive Permission Request Logic
async function checkAndRequestPermission(feature: PermissionFeature, options: PermissionOptions): Promise<boolean> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && global.checkAndRequestPermission) {
    return global.checkAndRequestPermission(feature, options);
  }

  const { showRationale, type } = options;
  const status = await checkPermissionStatus(feature);

  // V6 Business Logic: Early return for already granted permissions
  if (status.status === PermissionStatus.ALLOWED || status.status === PermissionStatus.LIMITED) {
    return true;
  }

  // V6 Business Logic: Show rationale if requested
  if (showRationale) {
    return showRationaleDialog(feature, Array.isArray(type) ? type[0] : type);
  }

  // V6 Business Logic: Handle blocked/unavailable states
  if (status.status === PermissionStatus.BLOCKED || status.status === PermissionStatus.UNAVAILABLE) {
    return showSettingsDialog(feature);
  }

  // V6 Business Logic: Direct request for unknown/denied
  return requestNativePermission(feature, Array.isArray(type) ? type[0] : type);
}

// Helper Functions (to be implemented)
async function checkPermissionStatus(feature: PermissionFeature): Promise<{ status: PermissionStatus; options: string[] }> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && global.checkPermissionStatus) {
    return global.checkPermissionStatus(feature);
  }
  return PermissionsService.checkPermissionStatus(feature);
}

async function requestNativePermission(feature: PermissionFeature, type?: string): Promise<boolean> {
  // Use global mock function if available (for testing)
  if (typeof global !== 'undefined' && global.requestNativePermission) {
    return global.requestNativePermission(feature, type);
  }
  return PermissionsService.requestPermission(feature, { showRationale: false });
}

async function showRationaleDialog(feature: PermissionFeature, type?: string): Promise<boolean> {
  // TODO: Implement rationale dialog
  return false;
}

async function showSettingsDialog(feature: PermissionFeature): Promise<boolean> {
  // TODO: Implement settings dialog
  return false;
}

function detectPermissionChanges(existing: Permission[], current: Permission[]): Permission[] {
  return PermissionsService.detectPermissionChanges(existing, current);
}

export const {
  updatePermissionStatus,
  setBackgroundState,
  addToRequestQueue,
  removeFromRequestQueue,
  setActiveRequest,
  setError,
  clearError,
  setLoading,
  setInitialized,
} = permissionsSlice.actions;

export default permissionsSlice.reducer;
