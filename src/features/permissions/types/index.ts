// V7 Permissions System Types - Extracted from V6 Business Rules

export enum PermissionStatus {
  UNKNOWN = 'unknown',      // Before request & requestable
  DENIED = 'denied',        // Denied by user, can ask again (Android)
  BLOCKED = 'blocked',      // Denied by user, blocked (need settings)
  LIMITED = 'limited',      // Limited access granted
  ALLOWED = 'allowed',      // Full access granted
  UNAVAILABLE = 'unavailable', // Feature not available on device
}

export enum PermissionFeature {
  CAMERA = 'camera',
  PHOTO = 'photo',
  CONTACTS = 'contacts',
  CALENDARS = 'calendars',
  WRITE_CALENDAR = 'write_calendar',
  READ_CALENDAR = 'read_calendar',
  LOCATION = 'location',
  NOTIFICATIONS = 'notifications',
  BACKGROUND_FETCH = 'background_fetch',
  BIOMETRICS = 'biometrics',
  BLUETOOTH = 'bluetooth',
  NFC = 'nfc',
  CELLULAR_DATA = 'cellular_data',
}

export interface Permission {
  feature: PermissionFeature;
  status: PermissionStatus;
  options: string[];
  grantedAt: string | null;
  revokedAt: string | null;
  lastChecked: string;
}

export interface PermissionOptions {
  showRationale?: boolean;
  type?: string | string[];
}

export interface PermissionRequest {
  feature: PermissionFeature;
  options: PermissionOptions;
  requestedAt: string;
  status: 'pending' | 'completed' | 'failed';
}

export interface PermissionsState {
  // Core permissions data
  permissions: Permission[];
  
  // Request management
  requestQueue: PermissionRequest[];
  activeRequest: PermissionRequest | null;
  
  // State management
  isInitialized: boolean;
  lastSync: string | null;
  lastRefresh: string | null;
  
  // Error handling
  error: string | null;
  isLoading: boolean;
  
  // Background state handling
  isInBackground: boolean;
}

// Business Logic Interfaces

export interface ProgressivePermissionFlow {
  checkAndRequest(feature: PermissionFeature, options: PermissionOptions): Promise<boolean>;
  showRationaleDialog(feature: PermissionFeature, type?: string): Promise<boolean>;
  showSettingsDialog(feature: PermissionFeature): Promise<boolean>;
}

export interface CrossPlatformMapping {
  mapFeatureToNativePermission(feature: PermissionFeature): string | null;
  isFeatureAvailable(feature: PermissionFeature): boolean;
  getAndroidVersionSpecificPermission(feature: PermissionFeature): string;
}

export interface StatusNormalization {
  normalizePermissionStatus(nativeStatus: string, platform: string): PermissionStatus;
  isRequestable(status: PermissionStatus): boolean;
  requiresSettings(status: PermissionStatus): boolean;
}

export interface PermissionStateReconciliation {
  reconcilePermissionStates(): Promise<Permission[]>;
  detectPermissionChanges(existing: Permission[], current: Permission[]): Permission[];
  handleEdgeCases(permission: Permission): Permission;
}

// Action Payloads

export interface CheckPermissionPayload {
  feature: PermissionFeature;
}

export interface RequestPermissionPayload {
  feature: PermissionFeature;
  options?: PermissionOptions;
}

export interface UpdatePermissionPayload {
  feature: PermissionFeature;
  status: PermissionStatus;
  options?: string[];
  grantedAt?: string | null;
  revokedAt?: string | null;
}

export interface RefreshPermissionsPayload {
  feature?: PermissionFeature; // If undefined, refresh all
}

export interface SetBackgroundStatePayload {
  isInBackground: boolean;
}

// Cross-Platform Permission Mapping

export interface PlatformPermissionMap {
  ios: string;
  android: string;
  androidVersionSpecific?: {
    [version: number]: string;
  };
}

export const PLATFORM_PERMISSION_MAP: Record<PermissionFeature, PlatformPermissionMap> = {
  [PermissionFeature.CAMERA]: {
    ios: 'ios.permission.CAMERA',
    android: 'android.permission.CAMERA',
  },
  [PermissionFeature.PHOTO]: {
    ios: 'ios.permission.PHOTO_LIBRARY',
    android: 'android.permission.READ_EXTERNAL_STORAGE',
    androidVersionSpecific: {
      33: 'android.permission.READ_MEDIA_IMAGES', // Android 13+
    },
  },
  [PermissionFeature.CONTACTS]: {
    ios: 'ios.permission.CONTACTS',
    android: 'android.permission.READ_CONTACTS',
  },
  [PermissionFeature.CALENDARS]: {
    ios: 'ios.permission.CALENDARS',
    android: 'android.permission.WRITE_CALENDAR',
  },
  [PermissionFeature.LOCATION]: {
    ios: 'ios.permission.LOCATION_WHEN_IN_USE',
    android: 'android.permission.ACCESS_FINE_LOCATION',
  },
  [PermissionFeature.BIOMETRICS]: {
    ios: 'ios.permission.FACE_ID',
    android: '', // Not applicable on Android
  },
  [PermissionFeature.BLUETOOTH]: {
    ios: 'ios.permission.BLUETOOTH',
    android: 'android.permission.BLUETOOTH_SCAN',
  },
  // Add other mappings...
} as any;

// Native Status Mapping

export const NATIVE_STATUS_MAP: Record<string, PermissionStatus> = {
  // React Native Permissions results
  'denied': PermissionStatus.UNKNOWN,
  'limited': PermissionStatus.LIMITED,
  'granted': PermissionStatus.ALLOWED,
  'blocked': PermissionStatus.BLOCKED,
  'unavailable': PermissionStatus.UNAVAILABLE,

  // Background Fetch specific
  'available': PermissionStatus.ALLOWED,
  'restricted': PermissionStatus.UNAVAILABLE,
};

// Permission Features List

export const PERMISSION_FEATURES = [
  PermissionFeature.CAMERA,
  PermissionFeature.PHOTO,
  PermissionFeature.CONTACTS,
  PermissionFeature.CALENDARS,
  PermissionFeature.LOCATION,
  PermissionFeature.BIOMETRICS,
  PermissionFeature.NOTIFICATIONS,
  PermissionFeature.BACKGROUND_FETCH,
  PermissionFeature.CELLULAR_DATA,
  PermissionFeature.BLUETOOTH,
  PermissionFeature.NFC,
] as const;

// Location Options

export const LocationOptions = {
  WHILE_IN_APP: 'whenInUse',
  ALWAYS: 'always',
  FULL_ACCURACY: 'fullAccuracy',
} as const;

// Error Types

export interface PermissionError {
  code: string;
  message: string;
  feature?: PermissionFeature;
  context?: any;
}

export const PermissionErrorCodes = {
  REQUEST_FAILED: 'Permission request failed',
  CHECK_FAILED: 'Permission check failed',
  BACKGROUND_REQUEST: 'Cannot request permission in background',
  FEATURE_UNAVAILABLE: 'Feature not available on device',
  RATIONALE_REJECTED: 'User rejected rationale',
  SETTINGS_REQUIRED: 'Settings access required',
} as const;

// Utility Types

export type PermissionFeatureType = typeof PermissionFeature[keyof typeof PermissionFeature];
export type PermissionStatusType = typeof PermissionStatus[keyof typeof PermissionStatus];
export type LocationOption = typeof LocationOptions[keyof typeof LocationOptions];
export type PermissionErrorCode = typeof PermissionErrorCodes[keyof typeof PermissionErrorCodes];

// Helper Functions Types

export interface PermissionHelpers {
  isPermissionGranted(permission: Permission): boolean;
  isPermissionRequestable(permission: Permission): boolean;
  isPermissionBlocked(permission: Permission): boolean;
  canShowRationale(permission: Permission): boolean;
  requiresSystemSettings(permission: Permission): boolean;
}
