/**
 * Profile Screen
 * Simple Me/Profile tab implementation
 */

import React, { useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, SafeAreaView } from 'react-native';
import { useAppSelector, useAppDispatch } from '../../core/store/hooks';
import { Button, ProfileImage, ListItem } from '../../shared/ui';
import {
  setCurrentPerson,
  setProfileEditing,
  loadCurrentPersonOptimistic,
  updatePersonProfileOptimistic,
  selectCurrentPerson,
  selectPersonDataState,
  selectProfileIsEditing,
  selectCurrentPersonDisplayInfo
} from '../../core/store/slices/person';

export const ProfileScreen: React.FC = () => {
  const dispatch = useAppDispatch();

  // Use new selectors
  const person = useAppSelector(selectCurrentPerson);
  const { loading: isLoading } = useAppSelector(selectPersonDataState);
  const isEditing = useAppSelector(selectProfileIsEditing);
  const displayInfo = useAppSelector(selectCurrentPersonDisplayInfo);

  // Initialize with mock person data for demo
  useEffect(() => {
    if (!person) {
      // For demo, we'll just set a current person ID and load it
      dispatch(setCurrentPerson('1'));
      dispatch(loadCurrentPersonOptimistic('1'));
    }
  }, [person, dispatch]);

  const handleEditToggle = () => {
    dispatch(setProfileEditing(!isEditing));
  };

  const handleProfileImagePress = () => {
    // TODO: Implement image picker
    console.log('Profile image pressed');
  };

  const handleFieldPress = (field: string) => {
    // TODO: Implement field editing
    console.log(`Edit ${field}`);
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Profile</Text>
          <Button
            title={isEditing ? 'Done' : 'Edit'}
            onPress={handleEditToggle}
            variant="text"
            size="medium"
          />
        </View>

        {/* Profile Image Section */}
        <View style={styles.imageSection}>
          <ProfileImage
            image={person?.profile.profileImage ? {
              url: person.profile.profileImage.url || person.profile.profileImage.uri || '',
              uri: person.profile.profileImage.uri,
              width: person.profile.profileImage.width || 100,
              height: person.profile.profileImage.height || 100,
              size: person.profile.profileImage.size || 100,
            } : null}
            size={100}
            editable={isEditing}
            onPress={isEditing ? handleProfileImagePress : undefined}
            gender={person?.profile.gender}
          />
          <Text style={styles.displayName}>
            {displayInfo?.displayName || 'Add your name'}
          </Text>
          {displayInfo?.email && (
            <Text style={styles.email}>{displayInfo.email}</Text>
          )}
        </View>

        {/* Profile Details */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Personal Information</Text>
          
          <ListItem
            title="First Name"
            value={person?.profile.firstName || 'Not set'}
            onPress={isEditing ? () => handleFieldPress('firstName') : undefined}
            showChevron={isEditing}
          />

          <ListItem
            title="Last Name"
            value={person?.profile.lastName || 'Not set'}
            onPress={isEditing ? () => handleFieldPress('lastName') : undefined}
            showChevron={isEditing}
          />

          <ListItem
            title="Email"
            value={person?.profile.email || 'Not set'}
            onPress={isEditing ? () => handleFieldPress('email') : undefined}
            showChevron={isEditing}
          />

          <ListItem
            title="Phone"
            value={person?.profile.phone || 'Not set'}
            onPress={isEditing ? () => handleFieldPress('phone') : undefined}
            showChevron={isEditing}
          />
        </View>

        {/* Settings Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Settings</Text>
          
          <ListItem
            title="Notifications"
            onPress={() => handleFieldPress('notifications')}
            showChevron
          />
          
          <ListItem
            title="Privacy"
            onPress={() => handleFieldPress('privacy')}
            showChevron
          />
          
          <ListItem
            title="Security"
            onPress={() => handleFieldPress('security')}
            showChevron
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  scrollView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#C6C6C8',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
  },
  imageSection: {
    alignItems: 'center',
    paddingVertical: 32,
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
  },
  displayName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    marginTop: 12,
  },
  email: {
    fontSize: 16,
    color: '#8E8E93',
    marginTop: 4,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F2F2F7',
  },
});
