/**
 * Wallet Screen
 * Connected to Redux using new card slices architecture
 */

import React, { useEffect } from 'react';
import { View, Text, ScrollView, StyleSheet, SafeAreaView } from 'react-native';
import { useAppSelector, useAppDispatch } from '../../core/store/hooks';
import { Button, ListItem } from '../../shared/ui';

// Import new card slice actions and selectors
import {
  selectCardMetrics,
  selectFilteredCards,
  selectCardDataState,
  createCardOptimistic,
  setFilters,
  selectCard,
} from '../../core/store/slices/card';

export const WalletScreen: React.FC = () => {
  const dispatch = useAppDispatch();

  // Select data from new card slices
  const cards = useAppSelector(selectFilteredCards);
  const metrics = useAppSelector(selectCardMetrics);
  const { loading, error } = useAppSelector(selectCardDataState);

  // Load cards on mount (this would typically be done in App.tsx or a higher component)
  useEffect(() => {
    // In a real app, this would trigger loading from Realm
    // For now, we'll just set up some mock data
    console.log('WalletScreen mounted, cards:', cards.length);
  }, [cards.length]);

  const handleCardPress = (cardId: string) => {
    console.log('Card pressed:', cardId);
    dispatch(selectCard(cardId));
  };

  const handleAddCard = async () => {
    console.log('Add card pressed');
    try {
      await dispatch(createCardOptimistic({
        masterId: 'master-' + Date.now(),
        personId: 'person-1',
        displayName: 'New Card',
      })).unwrap();
    } catch (error) {
      console.error('Failed to create card:', error);
    }
  };

  const handleTopUp = () => {
    console.log('Top up pressed');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Wallet</Text>
          <Button
            title="Add Card"
            onPress={handleAddCard}
            variant="primary"
            size="small"
          />
        </View>

        {/* Balance Section */}
        <View style={styles.balanceSection}>
          <Text style={styles.balanceLabel}>Total Balance</Text>
          <Text style={styles.balanceAmount}>
            {cards.reduce((total, card) => {
              // Calculate total from cards with stored value
              const storedValue = card.storedValue?.balance || 0;
              return total + storedValue;
            }, 0).toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
          </Text>
          <Text style={styles.cardCount}>
            {metrics.totalCards} cards • {metrics.activeCards} active
          </Text>
          <Button
            title="Top Up"
            onPress={handleTopUp}
            variant="secondary"
            size="medium"
            style={styles.topUpButton}
          />
        </View>

        {/* Cards Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>My Cards</Text>

          {loading && (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Loading cards...</Text>
            </View>
          )}

          {error && (
            <View style={styles.errorContainer}>
              <Text style={styles.errorText}>Error: {error}</Text>
            </View>
          )}

          {!loading && !error && cards.length === 0 && (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No cards yet</Text>
              <Text style={styles.emptySubtext}>Add your first card to get started</Text>
            </View>
          )}

          {cards.map((card) => (
            <ListItem
              key={card.id}
              title={card.displayName || 'Unnamed Card'}
              subtitle={card.number ? `**** ${card.number.slice(-4)}` : 'No number'}
              value={
                card.storedValue?.balance
                  ? card.storedValue.balance.toLocaleString('en-US', { style: 'currency', currency: 'USD' })
                  : card.state === 'active' ? 'Active' : card.state
              }
              onPress={() => handleCardPress(card.id)}
              showChevron
            />
          ))}
        </View>

        {/* Recent Transactions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Transactions</Text>
          
          <ListItem
            title="Coffee Purchase"
            subtitle="Starbucks - Main St"
            value="-$4.50"
          />
          
          <ListItem
            title="Card Top-up"
            subtitle="Starbucks Card"
            value="+$25.00"
          />
          
          <ListItem
            title="Metro Ride"
            subtitle="Downtown to Airport"
            value="-$3.25"
          />
          
          <ListItem
            title="Gym Check-in"
            subtitle="FitLife Gym"
            value="Free"
          />
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <ListItem
            title="Transfer Money"
            onPress={() => console.log('Transfer pressed')}
            showChevron
          />
          
          <ListItem
            title="Payment History"
            onPress={() => console.log('History pressed')}
            showChevron
          />
          
          <ListItem
            title="Card Settings"
            onPress={() => console.log('Settings pressed')}
            showChevron
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#C6C6C8',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
  },
  balanceSection: {
    alignItems: 'center',
    paddingVertical: 32,
    backgroundColor: '#FFFFFF',
    marginBottom: 20,
  },
  balanceLabel: {
    fontSize: 16,
    color: '#8E8E93',
    marginBottom: 8,
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  cardCount: {
    fontSize: 14,
    color: '#8E8E93',
    marginBottom: 16,
  },
  topUpButton: {
    marginTop: 8,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  errorContainer: {
    padding: 20,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#8E8E93',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#8E8E93',
    textAlign: 'center',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F2F2F7',
  },
});
