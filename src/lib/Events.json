{"Account": {"updatedPerson": "account.person.updated"}, "Actions": {"doAction": "action.do", "updated": "updated", "deleted": "deleted"}, "Address": {"suggest": "address.suggest", "select": "address.select"}, "App": {"upgraded": "app.upgraded", "register": "app.register", "verify": "app.verify", "launch": "app.launch", "login": "app.login", "logout": "app.logout", "registrationCompleted": "app.registrationCompleted", "launched": "app.launch.offline", "resume": "app.resume", "pause": "app.pause", "active": "app.active", "inactive": "app.inactive", "background": "app.background", "modeChanged": "app.mode.changed", "orientationLockChanged": "app.orientationLock.changed", "badgeUpdated": "app.badge.updated", "splashShow": "app.splash.show", "splashHide": "app.splash.hide"}, "Applet": {"to": "applet.to", "inject": "applet.inject", "progress": "applet.progress", "ssoLogin": "applet.login.sso", "loaded": "applet.loaded", "loadError": "applet.load.error", "usage": "applet.usage", "search": "applet.search"}, "Bag": {"addItems": "bag.items.add", "updateItems": "bag.items.update", "configItems": "bag.items.config", "removeItems": "bag.items.remove", "itemsAdded": "bag.items.added", "itemsUpdated": "bag.items.updated", "removeOffer": "bag.offer.remove", "initiateCheckout": "bag.initiate<PERSON><PERSON><PERSON>out", "checkout": "bag.checkout", "changed": "bag.changed"}, "Bridge": {"dataChanged": "data.changed", "interestedChanged": "interested.changed", "cardCreated": "card.created", "cardUpdated": "card.updated", "cardDeleted": "card.deleted"}, "Card": {"created": "created", "updated": "updated", "update": "updated", "notified": "notified", "deleted": "deleted", "new": "card.new", "recover": "card.recover", "issued": "card.issued", "view": "card.view", "exit": "card.exit", "add": "card.add", "edit": "card.edit", "scanned": "card.scanned", "updateImage": "card.updateImage", "delete": "card.delete", "accept": "card.accept", "decline": "card.decline", "register": "card.register", "cancelRegister": "card.register.cancel", "share": "card.share", "cancelShare": "card.share.cancel", "clockIn": "card.clockIn", "flipDone": "card.flipDone", "inPortrait": "card.inPortrait", "outPortrait": "card.outPortrait", "inLandscape": "card.inLandscape", "outLandscape": "card.outLandscape", "transition": {"openStart": "card.transition.open.start", "openEnd": "card.transition.open.end", "closeStart": "card.transition.close.start", "closeEnd": "card.transition.close.end"}}, "CardMaster": {"created": "created", "updated": "updated", "deleted": "deleted"}, "Discover": {"view": "discover.view", "search": "discover.search", "usage": "discover.applet.usage"}, "Ticket": {"usage": "ticket.applet.usage"}, "Engage": {"applet": "engage.applet", "pbox": "engage.pbox", "rate": "engage.rate"}, "Images": {"uploaded": "images.uploaded"}, "Location": {"countryChanged": "location.country.changed", "positionChanged": "location.position.changed", "spotChanged": "location.spot.changed"}, "Merchant": {"created": "created", "updated": "updated", "deleted": "deleted"}, "Messaging": {"showHeader": "messaging.header.show", "hideHeader": "messaging.header.hide", "showNext": "messaging.pbox.next", "pboxShow": "messaging.pbox.show", "pboxAction": "messaging.pbox.action", "cleared": "messaging.cleared", "issueCard": "messaging.issue.card"}, "Message": {"updated": "updated", "deleted": "deleted", "new": "message.new", "received": "message.received", "seen": "message.seen", "view": "message.view", "usageCover": "message.cover.usage", "viewBody": "message.body.view", "usageBody": "message.body.usage"}, "Navigator": {"cloak": "navigator.cloak", "home": "navigator.<PERSON><PERSON><PERSON><PERSON>", "dismissOverlay": "navigator.<PERSON><PERSON><PERSON><PERSON>", "screenAppeared": "navigator.screenAppeared", "modalDismissed": "navigator.modalDismissed", "lightBoxDismissed": "navigator.lightBoxDismissed", "bottomTabSelected": "navigator.bottomTabSelected", "popToRoot": "navigator.popToRoot", "command": "navigator.command"}, "Network": {"changed": "network.changed", "online": "network.online"}, "Notify": {"received": "notify.received"}, "Offer": {"updated": "updated", "update": "updated", "deleted": "deleted", "delete": "deleted", "new": "offer.new", "create": "offer.new", "created": "offer.new", "read": "offer.read", "received": "offer.received", "view": "offer.view", "redeem": "offer.redeem", "redeemed": "offer.redeem", "shared": "offer.shared", "expired": "offer.expired"}, "Payment": {"callback": "payment.callback", "methodAdded": "payment.method.added", "methodRemoved": "payment.method.removed"}, "Permissions": {"changed": "permissions.changed"}, "Person": {"updated": "updated", "update": "updated", "create": "person.create", "verify": "person.verify", "login": "person.login"}, "Place": {"updated": "updated", "deleted": "deleted", "view": "place.view", "viewMap": "place.view.map", "viewNearest": "place.view.nearest", "edit": "place.edit", "sortChanged": "place.sort.changed", "list": "place.list", "navigate": "place.navigate", "call": "place.call", "etaClean": "place.eta.clean", "etaShow": "place.eta.show"}, "PlaceList": {"updated": "updated"}, "Preference": {"updated": "updated", "updatedOrder": "preference.order.updated", "updatedCustomSort": "preference.customsort.updated", "updatedScan": "preference.scan.updated", "updatedRegions": "preference.regions.updated", "updatedBadge": "preference.badge.updated"}, "Reward": {"updated": "updated", "deleted": "deleted", "new": "reward.new", "received": "reward.received", "view": "reward.view", "issueStamp": "reward.stamp.issue"}, "Scan": {"scanned": "scan.scanned"}, "Settings": {"created": "created", "updated": "updated", "deleted": "deleted"}, "Share": {"shared": "share.shared"}, "Shop": {"itemsUpdated": "shop.items.updated", "brandsItemsUpdated": "shop.brand.items.updated", "merchantViewed": "shop.merchants.viewed", "merchantsUpdated": "shop.merchants.updated", "viewBrand": "shop.brand.view", "viewLink": "shop.link.view", "viewProduct": "shop.product.view"}, "Sync": {"all": "sync.all", "cache": "sync.cache", "appEvent": "sync.appevent"}, "Track": {"watchdog": "track.watchdog"}, "UrlScheme": {"link": "urlscheme.link"}, "Widget": {"view": "widget.view", "exit": "widget.exit", "arrive": "widget.visit.arrive", "leave": "widget.visit.leave", "checkin": "widget.attend.checkin", "checkout": "widget.attend.checkout", "changed": "widget.changed", "connectWifi": "widget.wifi.connect", "usage": "widget.applet.usage"}, "WidgetData": {"created": "created", "updated": "updated", "deleted": "deleted"}}