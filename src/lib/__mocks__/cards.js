// Mock Cards module for testing
export const Cards = {
  register: jest.fn(() => Promise.resolve({
    id: 'card-123',
    success: true,
  })),
  accept: jest.fn(() => Promise.resolve({
    success: true,
  })),
  decline: jest.fn(() => Promise.resolve({
    success: true,
  })),
  registerIssued: jest.fn(() => Promise.resolve({
    id: 'card-123',
    success: true,
  })),
  pay: jest.fn(() => Promise.resolve({
    success: true,
  })),
  qualify: jest.fn(() => Promise.resolve({
    success: true,
  })),
  notified: jest.fn(() => Promise.resolve({
    success: true,
  })),
  cleanup: jest.fn(() => Promise.resolve({
    success: true,
  })),
};

export const Person = {
  merge: jest.fn(() => Promise.resolve({
    success: true,
  })),
};

export const Profile = {
  default: jest.fn(() => ({})),
};

export const Viewing = {
  CARDID: null,
  WIDGETKEY: [],
  setCardId: jest.fn(),
  cardId: jest.fn(() => null),
  setWidgetKey: jest.fn(),
  widget: jest.fn(() => null),
  widgets: jest.fn(() => []),
};

export const StoredValue = {
  get: jest.fn(() => ({})),
};

export const CardNumber = {
  format: jest.fn(() => ''),
  display: jest.fn(() => ({})),
};

export const DisplayName = {
  get: jest.fn(() => ''),
  update: jest.fn(() => Promise.resolve({})),
};

export const Display = {
  imageProps: jest.fn(() => ({})),
  cardImageSize: jest.fn(() => ({ width: 100, height: 60 })),
  defaultImage: jest.fn(() => ''),
};

export const Reminder = {
  message: jest.fn(() => ''),
};

export const Deferred = {
  notified: jest.fn(() => Promise.resolve({})),
  accept: jest.fn(() => Promise.resolve({})),
  decline: jest.fn(() => Promise.resolve({})),
};

export const isIrregular = jest.fn(() => false);
export const setImageCache = jest.fn();
export const getImageCache = jest.fn(() => null);
export const removeImageCache = jest.fn();
export const cacheCustomImage = jest.fn(() => Promise.resolve({}));
