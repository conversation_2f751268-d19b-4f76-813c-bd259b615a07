// Mock CardService for testing
export default {
  register: jest.fn(() => Promise.resolve({
    cardId: 'card-123',
    success: true,
  })),
  accept: jest.fn(() => Promise.resolve({
    success: true,
  })),
  decline: jest.fn(() => Promise.resolve({
    success: true,
  })),
  registerIssued: jest.fn(() => Promise.resolve({
    id: 'card-123',
    success: true,
  })),
  update: jest.fn(() => Promise.resolve({
    success: true,
  })),
  delete: jest.fn(() => Promise.resolve({
    success: true,
  })),
};
