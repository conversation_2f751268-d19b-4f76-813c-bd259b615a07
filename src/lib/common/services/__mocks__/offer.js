// Mock OfferService for testing
export default {
  request: jest.fn(() => Promise.resolve({
    success: true,
    offerId: 'offer-123',
  })),
  redeem: jest.fn(() => Promise.resolve({
    success: true,
    redeemed: true,
    redeemedAt: new Date(),
  })),
  share: jest.fn(() => Promise.resolve({
    success: true,
    shared: true,
    sharedAt: new Date(),
  })),
  fetch: jest.fn(() => Promise.resolve({
    success: true,
    offers: [],
  })),
  notified: jest.fn(() => Promise.resolve({
    success: true,
  })),
  shareToMany: jest.fn(() => Promise.resolve({
    success: true,
  })),
  shareCancel: jest.fn(() => Promise.resolve({
    success: true,
  })),
};
