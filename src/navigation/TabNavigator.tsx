/**
 * Tab Navigator
 * Simple, clean tab navigation using React Navigation
 */

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { NavigationContainer } from '@react-navigation/native';
import { Text } from 'react-native';

import { DiscoverScreen } from '../features/discover/DiscoverScreen';
import { WalletScreen } from '../features/wallet/WalletScreen';
import { ProfileScreen } from '../features/profile/ProfileScreen';
import NotificationDemoScreen from '../screens/NotificationDemoScreen';
import { RootTabParamList } from './types';

const Tab = createBottomTabNavigator<RootTabParamList>();

// Simple icon component using text emojis for now
const TabIcon: React.FC<{ name: string; focused: boolean }> = ({ name, focused }) => {
  const getIcon = () => {
    switch (name) {
      case 'Discover':
        return '🔍';
      case 'Wallet':
        return '💳';
      case 'Me':
        return '👤';
      case 'Notifications':
        return '🔔';
      default:
        return '•';
    }
  };

  return (
    <Text style={{ fontSize: focused ? 24 : 20, opacity: focused ? 1 : 0.6 }}>
      {getIcon()}
    </Text>
  );
};

export const TabNavigator: React.FC = () => {
  return (
    <NavigationContainer>
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused }) => (
            <TabIcon name={route.name} focused={focused} />
          ),
          tabBarActiveTintColor: '#007AFF',
          tabBarInactiveTintColor: '#8E8E93',
          tabBarStyle: {
            backgroundColor: '#FFFFFF',
            borderTopWidth: 1,
            borderTopColor: '#C6C6C8',
          },
          tabBarLabelStyle: {
            fontSize: 12,
            fontWeight: '500',
            marginTop: 4,
          },
          headerShown: false, // We handle headers in individual screens
        })}
        initialRouteName="Discover"
      >
        <Tab.Screen 
          name="Discover" 
          component={DiscoverScreen}
          options={{
            tabBarLabel: 'Discover',
          }}
        />
        <Tab.Screen 
          name="Wallet" 
          component={WalletScreen}
          options={{
            tabBarLabel: 'Wallet',
          }}
        />
        <Tab.Screen
          name="Me"
          component={ProfileScreen}
          options={{
            tabBarLabel: 'Me',
          }}
        />
        <Tab.Screen
          name="Notifications"
          component={NotificationDemoScreen}
          options={{
            tabBarLabel: 'Notifications',
          }}
        />
      </Tab.Navigator>
    </NavigationContainer>
  );
};
