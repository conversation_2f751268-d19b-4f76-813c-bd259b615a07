// V7 Notification Demo Screen
// Modern, simple and effective layout to demonstrate notification features

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Button } from '../shared/ui/Button';
import { ListItem } from '../shared/ui/ListItem';
import {
  initializeProvider,
  getCurrentProvider,
  getNotificationToken,
  requestNotificationPermissions,
  getNotificationPermissions,
  scheduleLocalNotification,
  cancelAllLocalNotifications,
  setBadgeCount,
  getBadgeCount,
  getScheduledNotifications,
} from '../features/notifications/services/NotificationService';
import { initializeNotificationEventHandlers } from '../features/notifications/services/NotificationEventHandler';
import { NotificationSettings } from '../features/notifications/types';

interface NotificationStatus {
  isInitialized: boolean;
  permissions: NotificationSettings | null;
  token: string | null;
  badgeCount: number;
  scheduledCount: number;
  provider: string | null;
}

export default function NotificationDemoScreen() {
  const [status, setStatus] = useState<NotificationStatus>({
    isInitialized: false,
    permissions: null,
    token: null,
    badgeCount: 0,
    scheduledCount: 0,
    provider: null,
  });
  const [loading, setLoading] = useState(false);
  const [eventHandlers, setEventHandlers] = useState<any>(null);

  useEffect(() => {
    initializeNotificationSystem();
    return () => {
      if (eventHandlers) {
        eventHandlers.cleanup();
      }
    };
  }, []);

  const initializeNotificationSystem = async () => {
    try {
      setLoading(true);
      console.log('🚀 Initializing notification system...');

      // Initialize provider
      const provider = await initializeProvider();
      
      // Initialize event handlers
      const handlers = await initializeNotificationEventHandlers();
      setEventHandlers(handlers);

      // Update status
      await updateStatus();
      
      console.log('✅ Notification system initialized');
    } catch (error) {
      console.error('❌ Failed to initialize notification system:', error);
      Alert.alert('Initialization Error', 'Failed to initialize notification system');
    } finally {
      setLoading(false);
    }
  };

  const updateStatus = async () => {
    try {
      console.log('🔄 Updating notification status...');

      const provider = getCurrentProvider();
      console.log('📱 Provider:', provider);

      const permissions = await getNotificationPermissions();
      console.log('🔐 Permissions:', permissions);

      const token = await getNotificationToken();
      console.log('🎫 Token:', token ? `${token.substring(0, 20)}...` : 'No token');

      const badgeCount = await getBadgeCount();
      console.log('🔴 Badge count:', badgeCount);

      const scheduled = await getScheduledNotifications();
      console.log('⏰ Scheduled notifications:', scheduled.length);

      const newStatus = {
        isInitialized: provider?.isInitialized || false,
        permissions,
        token: token ? `${token.substring(0, 20)}...` : null,
        badgeCount,
        scheduledCount: scheduled.length,
        provider: provider?.provider || null,
      };

      console.log('✅ New status:', newStatus);
      console.log('🔐 Is authorized?', newStatus.permissions?.authorizationStatus === 'authorized');
      setStatus(newStatus);
    } catch (error) {
      console.error('❌ Failed to update status:', error);
    }
  };

  const handleRequestPermissions = async () => {
    console.log('🔐 Requesting notification permissions...');
    try {
      setLoading(true);
      const permissions = await requestNotificationPermissions();
      console.log('🔐 Permission result:', permissions);

      if (permissions?.authorizationStatus === 'authorized') {
        console.log('✅ Permissions granted!');
        Alert.alert('Success', 'Notification permissions granted!');
      } else {
        console.log('❌ Permissions denied:', permissions?.authorizationStatus);
        Alert.alert('Permission Denied', 'Notification permissions were not granted');
      }

      await updateStatus();
    } catch (error) {
      console.error('❌ Permission request failed:', error);
      Alert.alert('Error', 'Failed to request permissions');
    } finally {
      setLoading(false);
    }
  };

  const handleScheduleImmediate = async () => {
    try {
      const notificationId = await scheduleLocalNotification({
        title: 'Immediate Test',
        body: 'This notification appeared immediately!',
        data: { type: 'demo', immediate: true },
      });
      
      Alert.alert('Success', `Notification scheduled: ${notificationId}`);
      await updateStatus();
    } catch (error) {
      console.error('Failed to schedule notification:', error);
      Alert.alert('Error', 'Failed to schedule notification');
    }
  };

  const handleScheduleDelayed = async () => {
    try {
      const notificationId = await scheduleLocalNotification({
        title: 'Delayed Test',
        body: 'This notification was scheduled for 10 seconds!',
        data: { type: 'demo', delayed: true },
        trigger: {
          type: 'timestamp',
          timestamp: Date.now() + 10000, // 10 seconds
        },
      });
      
      Alert.alert('Success', `Delayed notification scheduled: ${notificationId}`);
      await updateStatus();
    } catch (error) {
      console.error('Failed to schedule delayed notification:', error);
      Alert.alert('Error', 'Failed to schedule delayed notification');
    }
  };

  const handleBadgeChange = async (count: number) => {
    try {
      await setBadgeCount(count);
      await updateStatus();
    } catch (error) {
      console.error('Failed to set badge count:', error);
    }
  };

  const handleClearAll = async () => {
    try {
      await cancelAllLocalNotifications();
      await setBadgeCount(0);
      await updateStatus();
      Alert.alert('Success', 'All notifications and badges cleared!');
    } catch (error) {
      console.error('Failed to clear notifications:', error);
      Alert.alert('Error', 'Failed to clear notifications');
    }
  };

  const isAuthorized = status.permissions?.authorizationStatus === 'authorized';

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>Notification Demo</Text>
          <Text style={styles.subtitle}>Test notification features and permissions</Text>
        </View>

        {/* Status Card */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>System Status</Text>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Provider:</Text>
            <Text style={[styles.statusValue, status.provider ? styles.success : styles.error]}>
              {status.provider || 'Not initialized'}
            </Text>
          </View>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Initialized:</Text>
            <Text style={[styles.statusValue, status.isInitialized ? styles.success : styles.error]}>
              {status.isInitialized ? 'Yes' : 'No'}
            </Text>
          </View>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Permissions:</Text>
            <Text style={[styles.statusValue, isAuthorized ? styles.success : styles.warning]}>
              {status.permissions?.authorizationStatus || 'Unknown'}
            </Text>
          </View>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Token:</Text>
            <Text style={[styles.statusValue, status.token ? styles.success : styles.error]}>
              {status.token || 'No token'}
            </Text>
          </View>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Badge Count:</Text>
            <Text style={styles.statusValue}>{status.badgeCount}</Text>
          </View>
          <View style={styles.statusRow}>
            <Text style={styles.statusLabel}>Scheduled:</Text>
            <Text style={styles.statusValue}>{status.scheduledCount}</Text>
          </View>
        </View>

        {/* Permissions Section */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Permissions</Text>
          <Text style={styles.cardDescription}>
            Request notification permissions to enable push notifications
          </Text>
          
          <Button
            title={isAuthorized ? '✓ Permissions Granted' : 'Request Permissions'}
            onPress={handleRequestPermissions}
            variant={isAuthorized ? 'secondary' : 'primary'}
            disabled={loading}
          />
        </View>

        {/* Local Notifications Section */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Local Notifications</Text>
          <Text style={styles.cardDescription}>
            Test local notification scheduling and display
          </Text>
          
          <View style={styles.buttonGroup}>
            <Button
              title="Show Immediate"
              onPress={handleScheduleImmediate}
              variant="secondary"
              disabled={!isAuthorized}
              style={styles.halfButton}
            />

            <Button
              title="Schedule 10s Delay"
              onPress={handleScheduleDelayed}
              variant="secondary"
              disabled={!isAuthorized}
              style={styles.halfButton}
            />
          </View>
        </View>

        {/* Badge Management Section */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Badge Management</Text>
          <Text style={styles.cardDescription}>
            Control app icon badge count
          </Text>
          
          <View style={styles.badgeControls}>
            {[0, 1, 5, 10].map((count) => (
              <TouchableOpacity
                key={count}
                style={[
                  styles.badgeButton,
                  status.badgeCount === count && styles.badgeButtonActive
                ]}
                onPress={() => handleBadgeChange(count)}
              >
                <Text style={[
                  styles.badgeButtonText,
                  status.badgeCount === count && styles.badgeButtonTextActive
                ]}>
                  {count}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Actions Section */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Actions</Text>
          
          <Button
            title="Clear All Notifications & Badges"
            onPress={handleClearAll}
            variant="secondary"
            style={styles.dangerButton}
          />

          <Button
            title="Refresh Status"
            onPress={updateStatus}
            variant="text"
          />
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text style={styles.footerText}>
            This demo showcases the V7 notification infrastructure with provider architecture
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    paddingVertical: 24,
    alignItems: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  card: {
    backgroundColor: '#ffffff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  cardDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 16,
    lineHeight: 20,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  statusLabel: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '600',
  },
  success: {
    color: '#10b981',
  },
  warning: {
    color: '#f59e0b',
  },
  error: {
    color: '#ef4444',
  },
  buttonGroup: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  halfButton: {
    flex: 1,
  },
  dangerButton: {
    backgroundColor: '#ef4444',
  },
  badgeControls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  badgeButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#f3f4f6',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  badgeButtonActive: {
    backgroundColor: '#3b82f6',
    borderColor: '#1d4ed8',
  },
  badgeButtonText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#6b7280',
  },
  badgeButtonTextActive: {
    color: '#ffffff',
  },
  footer: {
    paddingVertical: 24,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    lineHeight: 18,
  },
});
