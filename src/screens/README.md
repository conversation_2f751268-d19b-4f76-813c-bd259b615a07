# Notification Demo Screen

A modern, simple and effective layout screen to demonstrate the V7 notification infrastructure features.

## Overview

The `NotificationDemoScreen` provides a comprehensive interface to test and demonstrate all notification features including:

- **Permission Management**: Request and check notification permissions
- **Local Notifications**: Schedule immediate and delayed notifications  
- **Badge Management**: Control app icon badge count
- **Provider Status**: View current notification provider and system status
- **Token Management**: Display notification tokens (masked for security)

## Features

### 🔐 Permission Management
- Request notification permissions with visual feedback
- Display current permission status (authorized/denied/unknown)
- Clear indication of permission state with color coding

### 📱 Local Notifications
- **Immediate Notifications**: Test instant notification display
- **Delayed Notifications**: Schedule notifications with 10-second delay
- **Notification Scheduling**: Uses the V7 notification infrastructure with notifee

### 🔴 Badge Management
- Set badge count to predefined values (0, 1, 5, 10)
- Visual indication of current badge count
- Clear all badges functionality

### ⚙️ System Status
- **Provider Information**: Shows current notification provider (APNs/FCM)
- **Initialization Status**: Displays if the notification system is properly initialized
- **Token Status**: Shows if notification token is available (masked for security)
- **Scheduled Count**: Number of currently scheduled notifications

### 🧹 Actions
- **Clear All**: Remove all scheduled notifications and reset badges
- **Refresh Status**: Update all status information manually

## Usage

### Navigation
The screen is accessible via the "Notifications" tab in the bottom tab navigator.

### Testing Workflow
1. **Initialize**: The screen automatically initializes the notification system on load
2. **Request Permissions**: Tap "Request Permissions" to enable notifications
3. **Test Local Notifications**: Use "Show Immediate" or "Schedule 10s Delay" buttons
4. **Manage Badges**: Tap badge count buttons (0, 1, 5, 10) to set app icon badge
5. **Monitor Status**: Check the System Status section for real-time information
6. **Clean Up**: Use "Clear All" to reset notifications and badges

### Permission States
- **✅ Authorized**: Green text, notifications enabled
- **⚠️ Denied/Unknown**: Orange/red text, notifications disabled
- **🔄 Loading**: Shows activity indicator during permission requests

## Architecture Integration

### V7 Notification Infrastructure
The demo screen integrates with the complete V7 notification infrastructure:

- **Provider Architecture**: Uses APNs (iOS) or FCM (Android) providers
- **Event Handlers**: Integrates with notification event handling system
- **Redux Integration**: Connected to the Redux store for state management
- **Error Handling**: Graceful error handling with user feedback

### Components Used
- **Existing UI Components**: Uses `Button` and `ListItem` from `src/shared/ui/`
- **Consistent Styling**: Follows app design patterns and color schemes
- **Responsive Layout**: Adapts to different screen sizes with proper spacing

## Technical Details

### Dependencies
- React Navigation (tab navigation)
- Redux (state management)
- V7 Notification Services
- Shared UI Components

### Error Handling
- Permission request failures show alert dialogs
- Network errors are logged and displayed to user
- Graceful degradation when notification services are unavailable

### Testing
- Basic rendering tests included
- Mocked notification services for testing environment
- Snapshot testing for UI consistency

## Development

### Adding New Features
1. Import additional notification services from `../features/notifications/services/`
2. Add new UI sections following the existing card-based layout
3. Update the status interface to include new status fields
4. Add corresponding test cases

### Styling
The screen uses a modern card-based layout with:
- Clean white cards with subtle shadows
- Consistent spacing and typography
- Color-coded status indicators
- Responsive button layouts

### Performance
- Efficient state updates with React hooks
- Minimal re-renders with proper dependency arrays
- Cleanup of event handlers on component unmount

## Troubleshooting

### Common Issues
1. **Permissions Not Working**: Ensure device/simulator supports notifications
2. **Provider Not Initialized**: Check console logs for initialization errors
3. **Tokens Not Available**: Verify Firebase/APNs configuration
4. **Notifications Not Appearing**: Check device notification settings

### Debug Information
The screen provides comprehensive debug information in the System Status section:
- Provider type and availability
- Initialization status
- Permission details
- Token availability
- Scheduled notification count

This information helps diagnose notification system issues during development and testing.
