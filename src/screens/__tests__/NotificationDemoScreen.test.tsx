// NotificationDemoScreen Test
// Basic rendering tests

import React from 'react';
import renderer from 'react-test-renderer';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import NotificationDemoScreen from '../NotificationDemoScreen';

// Mock the notification services
jest.mock('../../features/notifications/services/NotificationService', () => ({
  initializeProvider: jest.fn().mockResolvedValue({
    provider: 'apns',
    isInitialized: true,
    isAvailable: true,
    capabilities: {
      richNotifications: true,
      badgeSupport: true,
      soundSupport: true,
      imageSupport: true,
      actionSupport: true,
      scheduledNotifications: false,
    },
  }),
  getCurrentProvider: jest.fn().mockReturnValue({
    provider: 'apns',
    isInitialized: true,
    isAvailable: true,
    capabilities: {
      richNotifications: true,
      badgeSupport: true,
      soundSupport: true,
      imageSupport: true,
      actionSupport: true,
      scheduledNotifications: false,
    },
  }),
  getNotificationToken: jest.fn().mockResolvedValue('mock-token-12345'),
  requestNotificationPermissions: jest.fn().mockResolvedValue({
    authorizationStatus: 'authorized',
    sound: 1,
    badge: 1,
    alert: 1,
    criticalAlert: 0,
    provisional: 0,
    lockScreen: 1,
    notificationCenter: 1,
  }),
  getNotificationPermissions: jest.fn().mockResolvedValue({
    authorizationStatus: 'authorized',
    sound: 1,
    badge: 1,
    alert: 1,
    criticalAlert: 0,
    provisional: 0,
    lockScreen: 1,
    notificationCenter: 1,
  }),
  scheduleLocalNotification: jest.fn().mockResolvedValue('notification-id-123'),
  cancelAllLocalNotifications: jest.fn().mockResolvedValue(undefined),
  setBadgeCount: jest.fn().mockResolvedValue(undefined),
  getBadgeCount: jest.fn().mockResolvedValue(0),
  getScheduledNotifications: jest.fn().mockResolvedValue([]),
}));

jest.mock('../../features/notifications/services/NotificationEventHandler', () => ({
  initializeNotificationEventHandlers: jest.fn().mockResolvedValue({
    cleanup: jest.fn(),
    isInitialized: true,
  }),
}));

// Mock react-native-safe-area-context
jest.mock('react-native-safe-area-context', () => ({
  SafeAreaView: ({ children }: any) => children,
}));

// Create a mock store
const createMockStore = () => {
  return configureStore({
    reducer: {
      // Add minimal reducers for testing
      app: (state = { initialized: true }, action) => state,
    },
  });
};

describe('NotificationDemoScreen', () => {
  let store: any;

  beforeEach(() => {
    store = createMockStore();
    jest.clearAllMocks();
  });

  test('renders without crashing', () => {
    const tree = renderer.create(
      <Provider store={store}>
        <NotificationDemoScreen />
      </Provider>
    );

    expect(tree).toBeTruthy();
    expect(tree.toJSON()).toMatchSnapshot();
  });

  test('component structure is stable', () => {
    const tree = renderer.create(
      <Provider store={store}>
        <NotificationDemoScreen />
      </Provider>
    );

    // Test that the component renders consistently
    expect(tree.toJSON()).toBeTruthy();
  });
});
