/**
 * Test Setup Configuration
 * 
 * Global test setup and mocks
 */

// <PERSON>ck react-redux to avoid ES module issues
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn(),
  Provider: ({ children }) => children,
}));

// Mock redux-persist
jest.mock('redux-persist/integration/react', () => ({
  PersistGate: ({ children }) => children,
}));

// Mock React Native components
jest.mock('react-native', () => ({
  SafeAreaView: 'SafeAreaView',
  Text: 'Text',
  StyleSheet: {
    create: jest.fn((styles) => styles),
  },
}));

// Mock global objects that might be used in legacy code
global._ = {
  Card: {
    findById: jest.fn(),
    findAll: jest.fn(() => ({
      filtered: jest.fn(() => []),
    })),
  },
  Offer: {
    findById: jest.fn(),
    findAll: jest.fn(() => ({
      filtered: jest.fn(() => []),
    })),
  },
};

global.$ = {
  Event: {
    emit: jest.fn(),
    on: jest.fn(),
  },
};

// Mock __DEV__ global
global.__DEV__ = true;
