/**
 * Profile Image Component
 * Simple profile image with fallback and edit capability
 */

import React from 'react';
import { View, Image, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { ProfileImage as ProfileImageType } from '../../core/types';

interface ProfileImageProps {
  image?: ProfileImageType | null;
  size?: number;
  editable?: boolean;
  onPress?: () => void;
  gender?: string;
}

export const ProfileImage: React.FC<ProfileImageProps> = ({
  image,
  size = 80,
  editable = false,
  onPress,
  gender,
}) => {
  const containerStyle = {
    width: size,
    height: size,
    borderRadius: size / 2,
  };

  const renderImage = () => {
    if (image?.uri) {
      return (
        <Image
          source={{ uri: image.uri }}
          style={[styles.image, containerStyle]}
          resizeMode="cover"
        />
      );
    }

    // Fallback to initials or default avatar
    return (
      <View style={[styles.fallback, containerStyle]}>
        <Text style={[styles.fallbackText, { fontSize: size * 0.4 }]}>
          {getInitials(gender)}
        </Text>
      </View>
    );
  };

  const content = (
    <View style={[styles.container, containerStyle]}>
      {renderImage()}
      {editable && (
        <View style={styles.editOverlay}>
          <Text style={styles.editText}>✏️</Text>
        </View>
      )}
    </View>
  );

  if (editable && onPress) {
    return (
      <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
        {content}
      </TouchableOpacity>
    );
  }

  return content;
};

const getInitials = (gender?: string): string => {
  // Simple fallback based on gender or default
  if (gender === 'female') return '👩';
  if (gender === 'male') return '👨';
  return '👤';
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  image: {
    backgroundColor: '#F2F2F7',
  },
  fallback: {
    backgroundColor: '#E5E5EA',
    alignItems: 'center',
    justifyContent: 'center',
  },
  fallbackText: {
    color: '#8E8E93',
    fontWeight: '600',
  },
  editOverlay: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#007AFF',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  editText: {
    fontSize: 12,
    color: '#FFFFFF',
  },
});
