// Global type declarations for Perkd v7
// Extends global namespace with test mocks and legacy compatibility

declare global {
  // Development flag
  var __DEV__: boolean;

  // Test environment global mocks
  namespace NodeJS {
    interface Global {
      // Notification system test mocks
      handleNotificationSync?: (syncType: string) => Promise<void>;
      handleNotificationEngagement?: (engageData: any) => Promise<void>;
      selectNotificationProvider?: (platform: string, region: string) => string;
      loadNotificationProvider?: (provider: string) => Promise<any>;
      createNotificationChannels?: () => Promise<void>;
      isChineseMarket?: (region: string) => boolean;

      // Permission system test mocks
      checkPermissionStatus?: (feature: string) => Promise<any>;
      checkAndRequestPermission?: (feature: string, options?: any) => Promise<boolean>;
      detectPermissionChanges?: (feature: string) => any[];
      requestNativePermission?: (feature: string, type: string) => Promise<boolean>;

      // Legacy system mocks for testing
      _?: {
        Card?: {
          findById?: jest.MockedFunction<any>;
          findAll?: jest.MockedFunction<any>;
        };
        Offer?: {
          findById?: jest.MockedFunction<any>;
          findAll?: jest.MockedFunction<any>;
        };
      };

      $?: {
        Event?: {
          emit?: jest.MockedFunction<any>;
          on?: jest.MockedFunction<any>;
        };
      };
    }
  }

  // Performance API for testing
  interface Performance {
    now(): number;
    mark(name: string): void;
    measure(name: string, startMark?: string, endMark?: string): void;
    getEntriesByName(name: string): PerformanceEntry[];
    getEntriesByType(type: string): PerformanceEntry[];
    clearMarks(name?: string): void;
    clearMeasures(name?: string): void;
  }

  // Console interface extension for test mocking
  interface Console {
    log: jest.MockedFunction<any> | typeof console.log;
    info: jest.MockedFunction<any> | typeof console.info;
    warn: jest.MockedFunction<any> | typeof console.warn;
    error: jest.MockedFunction<any> | typeof console.error;
  }

  // Jest global functions
  var jest: typeof import('jest');
  var describe: jest.Describe;
  var it: jest.It;
  var test: jest.It;
  var expect: jest.Expect;
  var beforeAll: jest.Lifecycle;
  var beforeEach: jest.Lifecycle;
  var afterAll: jest.Lifecycle;
  var afterEach: jest.Lifecycle;
}

// Module augmentation for global namespace
declare var global: NodeJS.Global & typeof globalThis;

export {};
