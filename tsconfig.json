{"extends": "@react-native/typescript-config", "compilerOptions": {"target": "ES2017", "lib": ["ES2017", "ES2015", "DOM"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/store/*": ["src/store/*"], "@/data/*": ["src/data/*"], "@/shared/*": ["src/shared/*"]}}, "include": ["src/**/*", "__tests__/**/*", "App.tsx", "index.js"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "types": ["jest", "react-native"]}