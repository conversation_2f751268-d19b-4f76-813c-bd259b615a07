#!/usr/bin/env node

/**
 * Clean Architecture Validation Script
 * 
 * Validates that our clean architecture implementation is working correctly
 * Tests elimination of version numbers and clean schema structure
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Validating Clean Architecture Implementation');
console.log('='.repeat(60));

let allTestsPassed = true;

// =============================================================================
// VALIDATION TESTS
// =============================================================================

function validateTest(testName, testFunction, description) {
  try {
    const result = testFunction();
    if (result) {
      console.log(`✅ ${testName}: ${description}`);
      return true;
    } else {
      console.log(`❌ ${testName}: ${description}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${testName}: ${description} - Error: ${error.message}`);
    return false;
  }
}

// Test 1: Clean schema files exist
const test1 = validateTest(
  'Schema Files',
  () => {
    const schemaFiles = [
      'src/data/schemas/index.ts',
      'src/data/schemas/types.ts',
      'src/data/schemas/indexing.ts'
    ];
    return schemaFiles.every(file => fs.existsSync(file));
  },
  'Clean schema files exist (no v7 directory)'
);

// Test 2: No version numbers in schema paths
const test2 = validateTest(
  'Version-Agnostic Paths',
  () => {
    return !fs.existsSync('src/data/schemas/v7') && 
           !fs.existsSync('src/data/schemas/v8') &&
           !fs.existsSync('src/data/schemas/v9');
  },
  'No version-specific directories exist'
);

// Test 3: Clean imports in schema files
const test3 = validateTest(
  'Clean Schema Exports',
  () => {
    const indexFile = 'src/data/schemas/index.ts';
    if (!fs.existsSync(indexFile)) return false;
    
    const content = fs.readFileSync(indexFile, 'utf8');
    return content.includes('CLEAN_SCHEMAS') && 
           content.includes('CURRENT_SCHEMA_VERSION') &&
           !content.includes('V7_CLEAN_SCHEMAS') &&
           !content.includes('V7_SCHEMA_VERSION');
  },
  'Schema exports use clean, version-agnostic names'
);

// Test 4: Database configuration uses clean imports
const test4 = validateTest(
  'Database Configuration',
  () => {
    const configFile = 'src/data/database/RealmConfig.ts';
    if (!fs.existsSync(configFile)) return false;
    
    const content = fs.readFileSync(configFile, 'utf8');
    return content.includes('DatabaseManager') &&
           content.includes('REALM_CONFIG') &&
           !content.includes('V7DatabaseManager') &&
           !content.includes('V7_REALM_CONFIG');
  },
  'Database configuration uses clean class and config names'
);

// Test 5: Redux slices exist
const test5 = validateTest(
  'Redux Slices',
  () => {
    const sliceFiles = [
      'src/store/slices/cardSlice.ts',
      'src/store/slices/offerSlice.ts',
      'src/store/slices/personSlice.ts',
      'src/store/slices/appSlice.ts'
    ];
    return sliceFiles.every(file => fs.existsSync(file));
  },
  'All required Redux slices exist'
);

// Test 6: Store configuration exists
const test6 = validateTest(
  'Store Configuration',
  () => {
    const storeFile = 'src/store/index.ts';
    if (!fs.existsSync(storeFile)) return false;
    
    const content = fs.readFileSync(storeFile, 'utf8');
    return content.includes('configureStore') &&
           content.includes('initializeStore');
  },
  'Redux store configuration is properly set up'
);

// Test 7: Migration components exist (temporary)
const test7 = validateTest(
  'Migration Components',
  () => {
    const migrationFiles = [
      'src/data/migration/V6DataExtractor.ts',
      'src/data/migration/DataTransformationEngine.ts'
    ];
    return migrationFiles.every(file => fs.existsSync(file));
  },
  'Migration components exist (temporary for v6 extraction)'
);

// Test 8: Test infrastructure exists
const test8 = validateTest(
  'Test Infrastructure',
  () => {
    const testFiles = [
      'jest.setup.v7.js'
    ];
    return testFiles.every(file => fs.existsSync(file));
  },
  'Test infrastructure is properly configured'
);

// Test 9: Documentation exists
const test9 = validateTest(
  'Documentation',
  () => {
    const docFiles = [
      'docs/clean-architecture-structure.md'
    ];
    return docFiles.every(file => fs.existsSync(file));
  },
  'Clean architecture documentation exists'
);

// =============================================================================
// SUMMARY
// =============================================================================

const tests = [test1, test2, test3, test4, test5, test6, test7, test8, test9];
const passedTests = tests.filter(Boolean).length;
const totalTests = tests.length;

console.log('\n' + '='.repeat(60));
console.log('📊 Validation Summary');
console.log('='.repeat(60));
console.log(`Total Tests: ${totalTests}`);
console.log(`Passed: ${passedTests}`);
console.log(`Failed: ${totalTests - passedTests}`);

if (passedTests === totalTests) {
  console.log('\n🎉 All validation tests passed!');
  console.log('✅ Clean architecture implementation is working correctly');
  console.log('🗑️ Version numbers successfully eliminated from architecture');
  console.log('🚀 Ready for fresh start migration strategy');
  process.exit(0);
} else {
  console.log('\n⚠️ Some validation tests failed');
  console.log('🔍 Review the failed tests and fix issues before proceeding');
  process.exit(1);
}
